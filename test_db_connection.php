<?php
/**
 * Test Database Connection and Table Structure
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Connection Test</h2>";

try {
    // Test basic connection
    $pdo = new PDO('mysql:host=localhost;dbname=blood_donation_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Users table exists</p>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<h3>Users table structure:</h3><ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Users table does not exist</p>";
    }
    
    // Check if blood_types table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'blood_types'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Blood types table exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Blood types table does not exist</p>";
    }
    
    // Test a simple query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "<p>Total users in database: {$result['count']}</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
