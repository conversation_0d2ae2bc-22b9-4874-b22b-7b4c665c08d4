<?php
/**
 * AJAX User Details Endpoint
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/UnifiedUser.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

// Get user ID
$userId = (int)($_GET['id'] ?? 0);

if ($userId <= 0) {
    echo '<div class="alert alert-danger">Invalid user ID</div>';
    exit;
}

// Load user data
try {
    $user = new UnifiedUser($userId);
    $userRoles = $user->getActiveRoles();

    // Build user data array from getter methods
    $userData = [
        'id' => $user->getId(),
        'username' => $user->getUsername(),
        'first_name' => $user->getFirstName(),
        'last_name' => $user->getLastName(),
        'email' => $user->getEmail(),
        'phone' => $user->getPhone(),
        'address' => $user->getAddress(),
        'profile_photo' => $user->getProfilePhoto(),
        'status' => $user->getStatus(),
        'user_type' => $user->getUserType(),
        'email_verified' => $user->isEmailVerified(),
        'last_login' => $user->getLastLogin(),
        'created_at' => $user->getCreatedAt()
    ];
} catch (Exception $e) {
    echo '<div class="alert alert-danger">User not found</div>';
    exit;
}

// Get role-specific data
$donorData = null;
$recipientData = null;
$donorStats = null;
$recipientStats = null;

if (in_array('donor', $userRoles)) {
    try {
        $donor = $user->getDonorInstance();
        if ($donor) {
            $donorData = [
                'blood_type' => $donor->getBloodType(),
                'weight' => $donor->getWeight(),
                'birth_date' => $donor->getBirthDate(),
                'last_donation_date' => $donor->getLastDonationDate(),
                'medical_conditions' => $donor->getMedicalConditions(),
                'eligibility_status' => $donor->getEligibilityStatus(),
                'total_donations' => $donor->getTotalDonations()
            ];
            $donorStats = $donor->getDonationStatistics();
        }
    } catch (Exception $e) {
        // Handle gracefully
    }
}

if (in_array('recipient', $userRoles)) {
    try {
        $recipient = $user->getRecipientInstance();
        if ($recipient) {
            $recipientData = [
                'medical_condition' => $recipient->getMedicalCondition(),
                'emergency_contact' => $recipient->getEmergencyContact(),
                'emergency_phone' => $recipient->getEmergencyPhone(),
                'doctor_name' => $recipient->getDoctorName(),
                'doctor_contact' => $recipient->getDoctorContact()
            ];
            $recipientStats = $recipient->getRequestStatistics();
        }
    } catch (Exception $e) {
        // Handle gracefully
    }
}
?>

<div class="row">
    <!-- User Header -->
    <div class="col-12 mb-3">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <?php if ($userData['profile_photo']): ?>
                    <img src="../uploads/<?php echo htmlspecialchars($userData['profile_photo']); ?>"
                         class="rounded-circle" width="60" height="60">
                <?php else: ?>
                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-user text-white fa-lg"></i>
                    </div>
                <?php endif; ?>
            </div>
            <div>
                <h4 class="mb-1"><?php echo htmlspecialchars($userData['first_name'] . ' ' . $userData['last_name']); ?></h4>
                <p class="text-muted mb-2">@<?php echo htmlspecialchars($userData['username']); ?></p>
                <div class="d-flex flex-wrap gap-2">
                    <span class="badge bg-<?php echo $userData['user_type'] === 'unified' ? 'primary' : ($userData['user_type'] === 'donor' ? 'success' : ($userData['user_type'] === 'admin' ? 'danger' : 'info')); ?>">
                        <i class="fas fa-<?php echo $userData['user_type'] === 'unified' ? 'user-tag' : ($userData['user_type'] === 'donor' ? 'heart' : ($userData['user_type'] === 'admin' ? 'shield-alt' : 'hand-holding-medical')); ?>"></i>
                        <?php echo ucfirst($userData['user_type']); ?>
                    </span>
                    <?php foreach ($userRoles as $role): ?>
                        <span class="badge bg-<?php echo $role === 'donor' ? 'success' : 'info'; ?>">
                            <i class="fas fa-<?php echo $role === 'donor' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                            <?php echo ucfirst($role); ?>
                        </span>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-user"></i> Basic Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Full Name:</strong></td>
                        <td><?php echo htmlspecialchars($userData['first_name'] . ' ' . $userData['last_name']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Username:</strong></td>
                        <td><?php echo htmlspecialchars($userData['username']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Phone:</strong></td>
                        <td><?php echo htmlspecialchars($userData['phone'] ?: 'Not provided'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Address:</strong></td>
                        <td><?php echo htmlspecialchars($userData['address'] ?: 'Not provided'); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="col-md-6">
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-cog"></i> Account Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge bg-<?php echo $userData['status'] === 'active' ? 'success' : ($userData['status'] === 'suspended' ? 'warning' : 'secondary'); ?>">
                                <?php echo ucfirst($userData['status']); ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Joined:</strong></td>
                        <td><?php echo formatDate($userData['created_at']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Last Login:</strong></td>
                        <td><?php echo $userData['last_login'] ? formatDate($userData['last_login']) : 'Never'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>User ID:</strong></td>
                        <td><?php echo $userData['id']; ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<?php if ($donorData): ?>
<!-- Donor Information -->
<div class="row">
    <div class="col-md-6">
        <div class="card mb-3">
            <div class="card-header bg-success text-white">
                <h6><i class="fas fa-heart"></i> Donor Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Blood Type:</strong></td>
                        <td><span class="badge bg-danger"><?php echo htmlspecialchars($donorData['blood_type'] ?? 'Not set'); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong>Total Donations:</strong></td>
                        <td><?php echo $donorStats['completed_donations'] ?? 0; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Units Donated:</strong></td>
                        <td><?php echo $donorStats['total_units_donated'] ?? 0; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Last Donation:</strong></td>
                        <td><?php echo $donorData['last_donation_date'] ? formatDate($donorData['last_donation_date']) : 'Never'; ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-3">
            <div class="card-header bg-success text-white">
                <h6><i class="fas fa-chart-bar"></i> Donation Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-success"><?php echo $donorStats['completed_donations'] ?? 0; ?></h4>
                        <small class="text-muted">Completed</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-primary"><?php echo $donorStats['scheduled_donations'] ?? 0; ?></h4>
                        <small class="text-muted">Scheduled</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($recipientData): ?>
<!-- Recipient Information -->
<div class="row">
    <div class="col-md-6">
        <div class="card mb-3">
            <div class="card-header bg-info text-white">
                <h6><i class="fas fa-hand-holding-medical"></i> Recipient Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Blood Type:</strong></td>
                        <td><span class="badge bg-danger"><?php echo htmlspecialchars($recipientData['blood_type'] ?? 'Not set'); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong>Total Requests:</strong></td>
                        <td><?php echo $recipientStats['total_requests'] ?? 0; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Emergency Contact:</strong></td>
                        <td><?php echo htmlspecialchars($recipientData['emergency_contact'] ?? 'Not provided'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Doctor:</strong></td>
                        <td><?php echo htmlspecialchars($recipientData['doctor_name'] ?? 'Not provided'); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-3">
            <div class="card-header bg-info text-white">
                <h6><i class="fas fa-chart-bar"></i> Request Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info"><?php echo $recipientStats['active_requests'] ?? 0; ?></h4>
                        <small class="text-muted">Active</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success"><?php echo $recipientStats['fulfilled_requests'] ?? 0; ?></h4>
                        <small class="text-muted">Fulfilled</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-tools"></i> Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <a href="user-details.php?id=<?php echo $userData['id']; ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i> Full Details
                    </a>
                    <?php if ($userData['status'] === 'active'): ?>
                        <button class="btn btn-warning btn-sm" onclick="suspendUser(<?php echo $userData['id']; ?>)">
                            <i class="fas fa-pause"></i> Suspend
                        </button>
                    <?php else: ?>
                        <button class="btn btn-success btn-sm" onclick="activateUser(<?php echo $userData['id']; ?>)">
                            <i class="fas fa-play"></i> Activate
                        </button>
                    <?php endif; ?>
                    <button class="btn btn-danger btn-sm" onclick="deleteUser(<?php echo $userData['id']; ?>)">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
