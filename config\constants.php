<?php
/**
 * System Constants
 * Blood Donation Management System
 */

// Application settings
define('APP_NAME', 'Blood Donation Management System');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/blood_donation_system');

// Security settings
define('SESSION_TIMEOUT', 1800); // 30 minutes
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes
define('CSRF_TOKEN_EXPIRE', 3600); // 1 hour
define('PASSWORD_RESET_EXPIRE', 3600); // 1 hour

// File upload settings
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Chat settings
define('MAX_MESSAGE_LENGTH', 1000); // Maximum characters per message
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx']);

// Pagination settings
define('RECORDS_PER_PAGE', 10);
define('MAX_PAGINATION_LINKS', 5);

// Blood donation settings
define('MIN_DONATION_INTERVAL', 56); // days between donations
define('MIN_DONOR_WEIGHT', 50); // kg
define('MIN_DONOR_AGE', 18);
define('MAX_DONOR_AGE', 65);

// User types
define('USER_TYPE_ADMIN', 'admin');
define('USER_TYPE_DONOR', 'donor');
define('USER_TYPE_RECIPIENT', 'recipient');

// User status
define('USER_STATUS_ACTIVE', 'active');
define('USER_STATUS_SUSPENDED', 'suspended');
define('USER_STATUS_PENDING', 'pending');

// Blood types
define('BLOOD_TYPES', [
    'A+' => 'A Positive',
    'A-' => 'A Negative',
    'B+' => 'B Positive',
    'B-' => 'B Negative',
    'AB+' => 'AB Positive',
    'AB-' => 'AB Negative',
    'O+' => 'O Positive',
    'O-' => 'O Negative'
]);

// Blood compatibility matrix
define('BLOOD_COMPATIBILITY', [
    'A+' => ['A+', 'AB+'],
    'A-' => ['A+', 'A-', 'AB+', 'AB-'],
    'B+' => ['B+', 'AB+'],
    'B-' => ['B+', 'B-', 'AB+', 'AB-'],
    'AB+' => ['AB+'],
    'AB-' => ['AB+', 'AB-'],
    'O+' => ['A+', 'B+', 'AB+', 'O+'],
    'O-' => ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
]);

// Request urgency levels
define('URGENCY_LOW', 'low');
define('URGENCY_MEDIUM', 'medium');
define('URGENCY_HIGH', 'high');
define('URGENCY_CRITICAL', 'critical');

define('URGENCY_LEVELS', [
    URGENCY_LOW => 'Low',
    URGENCY_MEDIUM => 'Medium',
    URGENCY_HIGH => 'High',
    URGENCY_CRITICAL => 'Critical'
]);

// Request status
define('REQUEST_STATUS_PENDING', 'pending');
define('REQUEST_STATUS_APPROVED', 'approved');
define('REQUEST_STATUS_FULFILLED', 'fulfilled');
define('REQUEST_STATUS_CANCELLED', 'cancelled');

define('REQUEST_STATUSES', [
    REQUEST_STATUS_PENDING => 'Pending',
    REQUEST_STATUS_APPROVED => 'Approved',
    REQUEST_STATUS_FULFILLED => 'Fulfilled',
    REQUEST_STATUS_CANCELLED => 'Cancelled'
]);

// Donation status
define('DONATION_STATUS_SCHEDULED', 'scheduled');
define('DONATION_STATUS_COMPLETED', 'completed');
define('DONATION_STATUS_CANCELLED', 'cancelled');

define('DONATION_STATUSES', [
    DONATION_STATUS_SCHEDULED => 'Scheduled',
    DONATION_STATUS_COMPLETED => 'Completed',
    DONATION_STATUS_CANCELLED => 'Cancelled'
]);

// Notification types
define('NOTIFICATION_TYPE_GENERAL', 'general');
define('NOTIFICATION_TYPE_URGENT', 'urgent');
define('NOTIFICATION_TYPE_REMINDER', 'reminder');

// Chat message status
define('MESSAGE_STATUS_SENT', 'sent');
define('MESSAGE_STATUS_DELIVERED', 'delivered');
define('MESSAGE_STATUS_READ', 'read');

// Error messages
define('ERROR_MESSAGES', [
    'INVALID_LOGIN' => 'Invalid username or password',
    'ACCOUNT_SUSPENDED' => 'Your account has been suspended',
    'SESSION_EXPIRED' => 'Your session has expired. Please login again',
    'ACCESS_DENIED' => 'Access denied. Insufficient permissions',
    'INVALID_TOKEN' => 'Invalid or expired token',
    'USERNAME_EXISTS' => 'Username already exists',
    'WEAK_PASSWORD' => 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long',
    'REQUIRED_FIELD' => 'This field is required',
    'FILE_TOO_LARGE' => 'File size exceeds maximum limit',
    'INVALID_FILE_TYPE' => 'Invalid file type',
    'UPLOAD_FAILED' => 'File upload failed',
    'DATABASE_ERROR' => 'Database operation failed',
    'EMAIL_SEND_FAILED' => 'Failed to send notification',
    'INVALID_BLOOD_TYPE' => 'Invalid blood type selected',
    'DONATION_TOO_SOON' => 'You must wait at least ' . MIN_DONATION_INTERVAL . ' days between donations',
    'INELIGIBLE_DONOR' => 'You are not eligible to donate at this time'
]);

// Success messages
define('SUCCESS_MESSAGES', [
    'REGISTRATION_SUCCESS' => 'Registration successful. Please select your role to get started',
    'LOGIN_SUCCESS' => 'Login successful',
    'LOGOUT_SUCCESS' => 'Logged out successfully',
    'PASSWORD_RESET_SENT' => 'Password reset instructions sent',
    'PASSWORD_CHANGED' => 'Password changed successfully',
    'PROFILE_UPDATED' => 'Profile updated successfully',
    'REQUEST_SUBMITTED' => 'Blood request submitted successfully',
    'DONATION_SCHEDULED' => 'Donation appointment scheduled successfully',
    'MESSAGE_SENT' => 'Message sent successfully',
    'NOTIFICATION_SENT' => 'Notification sent successfully'
]);

// Date and time formats
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// Timezone
define('DEFAULT_TIMEZONE', 'UTC');
date_default_timezone_set(DEFAULT_TIMEZONE);

// Logging settings
define('LOG_DIR', __DIR__ . '/../logs/');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// Cache settings
define('CACHE_ENABLED', false);
define('CACHE_EXPIRE', 3600); // 1 hour

// API settings (for future expansion)
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 100); // requests per hour



// Admin credentials (Configure these in production)
// define('ADMIN_USERNAME', 'your_admin_username');
// define('ADMIN_PASSWORD_HASH', password_hash('your_secure_password', PASSWORD_DEFAULT));
// define('ADMIN_EMAIL', '<EMAIL>');

// System paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('CLASSES_PATH', ROOT_PATH . '/classes');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('TEMPLATES_PATH', ROOT_PATH . '/templates');

// URL paths
define('BASE_URL', '/blood_donation_system');
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');

// Create necessary directories if they don't exist
$directories = [UPLOAD_DIR, LOG_DIR];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
?>
