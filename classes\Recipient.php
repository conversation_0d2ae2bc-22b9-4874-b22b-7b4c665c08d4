<?php
/**
 * Recipient Class
 * Blood Donation Management System
 */

require_once __DIR__ . '/User.php';
require_once __DIR__ . '/../config/constants.php';

class Recipient extends User {
    private $medicalCondition;
    private $emergencyContact;
    private $emergencyPhone;
    private $doctorName;
    private $doctorContact;
    
    public function __construct($id = null) {
        parent::__construct($id);
        
        if ($id) {
            $this->loadRecipientProfile();
        }
    }
    
    /**
     * Load recipient profile data
     */
    private function loadRecipientProfile() {
        $sql = "SELECT * FROM recipient_profiles WHERE user_id = ?";
        $profile = $this->db->fetch($sql, [$this->id]);
        
        if ($profile) {
            $this->medicalCondition = $profile['medical_condition'];
            $this->emergencyContact = $profile['emergency_contact'];
            $this->emergencyPhone = $profile['emergency_phone'];
            $this->doctorName = $profile['doctor_name'];
            $this->doctorContact = $profile['doctor_contact'];
        }
    }
    
    /**
     * Create new recipient
     */
    public static function create($userData) {
        $db = Database::getInstance();
        
        try {
            $db->beginTransaction();
            
            // Create user first
            $user = parent::create($userData);
            
            // Create recipient profile
            $sql = "INSERT INTO recipient_profiles (user_id, medical_condition, emergency_contact, emergency_phone, doctor_name, doctor_contact) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $db->execute($sql, [
                $user->getId(),
                $userData['medical_condition'] ?? '',
                $userData['emergency_contact'] ?? '',
                $userData['emergency_phone'] ?? '',
                $userData['doctor_name'] ?? '',
                $userData['doctor_contact'] ?? ''
            ]);
            
            $db->commit();
            
            logEvent('INFO', 'Recipient profile created', ['user_id' => $user->getId()]);
            
            return new self($user->getId());
            
        } catch (Exception $e) {
            $db->rollback();
            logEvent('ERROR', 'Recipient creation failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Update recipient profile
     */
    public function updateProfile($data) {
        try {
            $this->db->beginTransaction();
            
            // Update user data
            parent::update($data);
            
            // Update recipient profile
            $sql = "UPDATE recipient_profiles SET 
                    medical_condition = ?, 
                    emergency_contact = ?, 
                    emergency_phone = ?, 
                    doctor_name = ?, 
                    doctor_contact = ? 
                    WHERE user_id = ?";
            
            $this->db->execute($sql, [
                $data['medical_condition'] ?? $this->medicalCondition,
                $data['emergency_contact'] ?? $this->emergencyContact,
                $data['emergency_phone'] ?? $this->emergencyPhone,
                $data['doctor_name'] ?? $this->doctorName,
                $data['doctor_contact'] ?? $this->doctorContact,
                $this->id
            ]);
            
            $this->db->commit();
            
            // Update object properties
            $this->medicalCondition = $data['medical_condition'] ?? $this->medicalCondition;
            $this->emergencyContact = $data['emergency_contact'] ?? $this->emergencyContact;
            $this->emergencyPhone = $data['emergency_phone'] ?? $this->emergencyPhone;
            $this->doctorName = $data['doctor_name'] ?? $this->doctorName;
            $this->doctorContact = $data['doctor_contact'] ?? $this->doctorContact;
            
            logEvent('INFO', 'Recipient profile updated', ['user_id' => $this->id]);
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            logEvent('ERROR', 'Recipient profile update failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Submit blood request
     */
    public function submitBloodRequest($data) {
        $sql = "INSERT INTO blood_requests (
                    recipient_id, blood_type_id, units_needed, urgency_level, 
                    hospital_name, hospital_address, hospital_contact, 
                    required_by_date, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $this->db->execute($sql, [
            $this->id,
            $data['blood_type_id'],
            $data['units_needed'],
            $data['urgency_level'],
            $data['hospital_name'],
            $data['hospital_address'],
            $data['hospital_contact'] ?? '',
            $data['required_by_date'],
            REQUEST_STATUS_PENDING
        ]);
        
        $requestId = $this->db->lastInsertId();
        
        logEvent('INFO', 'Blood request submitted', [
            'user_id' => $this->id,
            'request_id' => $requestId,
            'blood_type_id' => $data['blood_type_id'],
            'urgency_level' => $data['urgency_level']
        ]);
        
        return $requestId;
    }
    
    /**
     * Update blood request
     */
    public function updateBloodRequest($requestId, $data) {
        // Check if request belongs to this recipient and is still pending
        $checkSql = "SELECT id FROM blood_requests 
                     WHERE id = ? AND recipient_id = ? AND status = ?";
        
        $request = $this->db->fetch($checkSql, [$requestId, $this->id, REQUEST_STATUS_PENDING]);
        
        if (!$request) {
            throw new Exception('Request not found or cannot be updated');
        }
        
        $sql = "UPDATE blood_requests SET 
                blood_type_id = ?, 
                units_needed = ?, 
                urgency_level = ?, 
                hospital_name = ?, 
                hospital_address = ?, 
                hospital_contact = ?, 
                required_by_date = ?, 
                updated_at = NOW() 
                WHERE id = ?";
        
        $this->db->execute($sql, [
            $data['blood_type_id'],
            $data['units_needed'],
            $data['urgency_level'],
            $data['hospital_name'],
            $data['hospital_address'],
            $data['hospital_contact'] ?? '',
            $data['required_by_date'],
            $requestId
        ]);
        
        logEvent('INFO', 'Blood request updated', [
            'user_id' => $this->id,
            'request_id' => $requestId
        ]);
        
        return true;
    }
    
    /**
     * Cancel blood request
     */
    public function cancelBloodRequest($requestId, $reason = '') {
        // Check if request belongs to this recipient
        $checkSql = "SELECT id FROM blood_requests 
                     WHERE id = ? AND recipient_id = ? AND status IN (?, ?)";
        
        $request = $this->db->fetch($checkSql, [
            $requestId, 
            $this->id, 
            REQUEST_STATUS_PENDING, 
            REQUEST_STATUS_APPROVED
        ]);
        
        if (!$request) {
            throw new Exception('Request not found or cannot be cancelled');
        }
        
        $sql = "UPDATE blood_requests SET 
                status = ?, 
                admin_notes = CONCAT(COALESCE(admin_notes, ''), '\nCancelled by recipient: ', ?), 
                updated_at = NOW() 
                WHERE id = ?";
        
        $this->db->execute($sql, [
            REQUEST_STATUS_CANCELLED,
            $reason,
            $requestId
        ]);
        
        logEvent('INFO', 'Blood request cancelled', [
            'user_id' => $this->id,
            'request_id' => $requestId,
            'reason' => $reason
        ]);
        
        return true;
    }
    
    /**
     * Get blood request history
     */
    public function getBloodRequestHistory($page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM blood_requests WHERE recipient_id = ?";
        $totalResult = $this->db->fetch($countSql, [$this->id]);
        $total = $totalResult['total'];
        
        // Get requests
        $sql = "SELECT br.*, bt.type as blood_type 
                FROM blood_requests br
                JOIN blood_types bt ON br.blood_type_id = bt.id
                WHERE br.recipient_id = ?
                ORDER BY br.created_at DESC
                LIMIT ? OFFSET ?";
        
        $requests = $this->db->fetchAll($sql, [$this->id, $limit, $offset]);
        
        return [
            'requests' => $requests,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
    
    /**
     * Get active blood requests
     */
    public function getActiveBloodRequests() {
        $sql = "SELECT br.*, bt.type as blood_type 
                FROM blood_requests br
                JOIN blood_types bt ON br.blood_type_id = bt.id
                WHERE br.recipient_id = ? 
                AND br.status IN (?, ?) 
                AND br.required_by_date >= CURDATE()
                ORDER BY br.urgency_level DESC, br.required_by_date ASC";
        
        return $this->db->fetchAll($sql, [
            $this->id, 
            REQUEST_STATUS_PENDING, 
            REQUEST_STATUS_APPROVED
        ]);
    }
    
    /**
     * Get request statistics
     */
    public function getRequestStatistics() {
        $sql = "SELECT 
                COUNT(*) as total_requests,
                SUM(units_needed) as total_units_requested,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as pending_requests,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as approved_requests,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as fulfilled_requests,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as cancelled_requests,
                SUM(CASE WHEN status = ? THEN units_needed ELSE 0 END) as fulfilled_units
                FROM blood_requests
                WHERE recipient_id = ?";
        
        return $this->db->fetch($sql, [
            REQUEST_STATUS_PENDING,
            REQUEST_STATUS_APPROVED,
            REQUEST_STATUS_FULFILLED,
            REQUEST_STATUS_CANCELLED,
            REQUEST_STATUS_FULFILLED,
            $this->id
        ]);
    }
    
    /**
     * Get compatible donors for a blood type
     */
    public function getCompatibleDonors($bloodTypeId, $page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        
        // Get blood type
        $bloodTypeSql = "SELECT type FROM blood_types WHERE id = ?";
        $bloodTypeResult = $this->db->fetch($bloodTypeSql, [$bloodTypeId]);
        
        if (!$bloodTypeResult) {
            return [
                'donors' => [],
                'pagination' => paginate(0, $page, $limit)
            ];
        }
        
        $recipientBloodType = $bloodTypeResult['type'];
        
        // Get compatible donor blood types
        $compatibleTypes = getCompatibleBloodTypes($recipientBloodType);
        
        if (empty($compatibleTypes)) {
            return [
                'donors' => [],
                'pagination' => paginate(0, $page, $limit)
            ];
        }
        
        // Get blood type IDs
        $placeholders = implode(',', array_fill(0, count($compatibleTypes), '?'));
        $bloodTypesSql = "SELECT id FROM blood_types WHERE type IN ($placeholders)";
        $bloodTypesResult = $this->db->fetchAll($bloodTypesSql, $compatibleTypes);
        $bloodTypeIds = array_column($bloodTypesResult, 'id');
        
        if (empty($bloodTypeIds)) {
            return [
                'donors' => [],
                'pagination' => paginate(0, $page, $limit)
            ];
        }
        
        // Get total count
        $placeholders = implode(',', array_fill(0, count($bloodTypeIds), '?'));
        $countSql = "SELECT COUNT(*) as total 
                     FROM users u 
                     JOIN donor_profiles dp ON u.id = dp.user_id 
                     WHERE u.user_type = ? 
                     AND u.status = ? 
                     AND dp.blood_type_id IN ($placeholders) 
                     AND dp.eligibility_status = ?";
        
        $countParams = array_merge([USER_TYPE_DONOR, USER_STATUS_ACTIVE], $bloodTypeIds, ['eligible']);
        $totalResult = $this->db->fetch($countSql, $countParams);
        $total = $totalResult['total'];
        
        // Get donors
        $sql = "SELECT u.*, dp.*, bt.type as blood_type 
                FROM users u 
                JOIN donor_profiles dp ON u.id = dp.user_id 
                JOIN blood_types bt ON dp.blood_type_id = bt.id 
                WHERE u.user_type = ? 
                AND u.status = ? 
                AND dp.blood_type_id IN ($placeholders) 
                AND dp.eligibility_status = ?
                ORDER BY dp.last_donation_date ASC, u.created_at DESC
                LIMIT ? OFFSET ?";
        
        $params = array_merge([USER_TYPE_DONOR, USER_STATUS_ACTIVE], $bloodTypeIds, ['eligible', $limit, $offset]);
        $donors = $this->db->fetchAll($sql, $params);
        
        return [
            'donors' => $donors,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
    
    /**
     * Get request details with donations
     */
    public function getRequestDetails($requestId) {
        // Check if request belongs to this recipient
        $sql = "SELECT br.*, bt.type as blood_type 
                FROM blood_requests br
                JOIN blood_types bt ON br.blood_type_id = bt.id
                WHERE br.id = ? AND br.recipient_id = ?";
        
        $request = $this->db->fetch($sql, [$requestId, $this->id]);
        
        if (!$request) {
            return null;
        }
        
        // Get related donations
        $donationsSql = "SELECT d.*, u.first_name, u.last_name, u.phone 
                         FROM donations d
                         JOIN users u ON d.donor_id = u.id
                         WHERE d.request_id = ?
                         ORDER BY d.donation_date DESC";
        
        $donations = $this->db->fetchAll($donationsSql, [$requestId]);
        
        $request['donations'] = $donations;
        
        return $request;
    }
    
    // Getters
    public function getMedicalCondition() { return $this->medicalCondition; }
    public function getEmergencyContact() { return $this->emergencyContact; }
    public function getEmergencyPhone() { return $this->emergencyPhone; }
    public function getDoctorName() { return $this->doctorName; }
    public function getDoctorContact() { return $this->doctorContact; }
    
    /**
     * Get all recipients with pagination
     */
    public static function getAll($page = 1, $limit = RECORDS_PER_PAGE, $filters = []) {
        $db = Database::getInstance();
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE u.user_type = ?";
        $params = [USER_TYPE_RECIPIENT];
        
        if (!empty($filters['search'])) {
            $whereClause .= " AND (u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ? OR u.username LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM users u 
                     JOIN recipient_profiles rp ON u.id = rp.user_id 
                     $whereClause";
        
        $totalResult = $db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get recipients
        $sql = "SELECT u.*, rp.* 
                FROM users u 
                JOIN recipient_profiles rp ON u.id = rp.user_id 
                $whereClause 
                ORDER BY u.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $recipients = $db->fetchAll($sql, $params);
        
        return [
            'recipients' => $recipients,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
}
?>
