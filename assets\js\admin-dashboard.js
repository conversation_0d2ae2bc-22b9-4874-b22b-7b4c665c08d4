/**
 * Admin Dashboard JavaScript
 * Blood Donation Management System
 */

class AdminDashboard {
    constructor() {
        this.updateInterval = 30000; // 30 seconds
        this.isUpdating = false;
        this.init();
    }

    init() {
        this.startRealTimeUpdates();
        this.initializeCounters();
        this.setupEventListeners();
    }

    /**
     * Start real-time updates for dashboard statistics
     */
    startRealTimeUpdates() {
        // Initial update
        this.updateDashboardStats();
        
        // Set up periodic updates
        setInterval(() => {
            if (!this.isUpdating) {
                this.updateDashboardStats();
            }
        }, this.updateInterval);
    }

    /**
     * Update dashboard statistics via AJAX
     */
    async updateDashboardStats() {
        this.isUpdating = true;
        
        try {
            const response = await fetch('/blood donation system/api/dashboard-stats.php', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.updateStatCards(data.stats);
                    this.updateSystemStatus(data.status);
                }
            }
        } catch (error) {
            console.warn('Dashboard update error:', error);
        } finally {
            this.isUpdating = false;
        }
    }

    /**
     * Update statistics cards with new data
     */
    updateStatCards(stats) {
        // Update user statistics
        if (stats.users) {
            this.animateCounter('.total-users', stats.users.total_users);
            this.animateCounter('.active-users', stats.users.active_users);
        }

        // Update donation statistics
        if (stats.donations) {
            this.animateCounter('.total-donations', stats.donations.total_donations);
            this.animateCounter('.completed-donations', stats.donations.completed_donations);
        }

        // Update request statistics
        if (stats.requests) {
            this.animateCounter('.total-requests', stats.requests.total_requests);
            this.animateCounter('.pending-requests', stats.requests.pending_requests);
        }

        // Update blood inventory
        if (stats.inventory) {
            this.updateBloodInventory(stats.inventory);
        }
    }

    /**
     * Animate counter from current value to new value
     */
    animateCounter(selector, newValue) {
        const element = document.querySelector(selector);
        if (!element) return;

        const currentValue = parseInt(element.textContent.replace(/,/g, '')) || 0;
        const increment = (newValue - currentValue) / 20;
        let current = currentValue;

        const timer = setInterval(() => {
            current += increment;
            if ((increment > 0 && current >= newValue) || (increment < 0 && current <= newValue)) {
                current = newValue;
                clearInterval(timer);
            }
            element.textContent = this.formatNumber(Math.round(current));
        }, 50);
    }

    /**
     * Format number with commas
     */
    formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    /**
     * Update blood inventory display
     */
    updateBloodInventory(inventory) {
        inventory.forEach(blood => {
            const element = document.querySelector(`[data-blood-type="${blood.type}"] .units-count`);
            if (element) {
                this.animateCounter(`[data-blood-type="${blood.type}"] .units-count`, blood.available_units);
            }
        });
    }

    /**
     * Update system status indicators
     */
    updateSystemStatus(status) {
        // Update database status
        const dbStatus = document.querySelector('.db-status');
        if (dbStatus) {
            dbStatus.className = `badge bg-${status.database ? 'success' : 'danger'}`;
            dbStatus.textContent = status.database ? 'Online' : 'Offline';
        }

        // Update other status indicators
        const activeUsersStatus = document.querySelector('.active-users-status');
        if (activeUsersStatus) {
            activeUsersStatus.textContent = status.active_users || 0;
        }
    }

    /**
     * Initialize counter animations on page load
     */
    initializeCounters() {
        const counters = document.querySelectorAll('.card-text');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent.replace(/,/g, '')) || 0;
            counter.textContent = '0';
            this.animateCounter(counter, target);
        });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Refresh button
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.updateDashboardStats();
                this.showRefreshFeedback();
            });
        }

        // Auto-refresh toggle
        const autoRefreshToggle = document.getElementById('autoRefreshToggle');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.startRealTimeUpdates();
                } else {
                    clearInterval(this.updateInterval);
                }
            });
        }
    }

    /**
     * Show refresh feedback to user
     */
    showRefreshFeedback() {
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            const originalText = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
            refreshBtn.disabled = true;

            setTimeout(() => {
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            }, 2000);
        }
    }

    /**
     * Add notification for critical alerts
     */
    addCriticalAlert(message, type = 'warning') {
        const alertContainer = document.getElementById('criticalAlerts');
        if (!alertContainer) return;

        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Alert:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        alertContainer.appendChild(alert);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 10000);
    }

    /**
     * Check for low blood stock and show alerts
     */
    checkBloodStockLevels(inventory) {
        inventory.forEach(blood => {
            if (blood.available_units < 5) {
                this.addCriticalAlert(
                    `Low stock alert: ${blood.type} blood type has only ${blood.available_units} units remaining.`,
                    'danger'
                );
            } else if (blood.available_units < 10) {
                this.addCriticalAlert(
                    `Stock warning: ${blood.type} blood type is running low (${blood.available_units} units).`,
                    'warning'
                );
            }
        });
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.adminDashboard = new AdminDashboard();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminDashboard;
}
