<?php
/**
 * System Announcements Management
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('announcements.php', 'Invalid security token', 'error');
    }

    $action = $_POST['action'] ?? '';

    try {
        // Create announcements table if it doesn't exist
        $db->execute("CREATE TABLE IF NOT EXISTS system_announcements (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            announcement_type ENUM('info', 'warning', 'urgent', 'success') DEFAULT 'info',
            target_roles JSON,
            is_active BOOLEAN DEFAULT TRUE,
            is_pinned BOOLEAN DEFAULT FALSE,
            expires_at DATETIME NULL,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");

        switch ($action) {
            case 'create':
                $title = sanitizeInput($_POST['title'] ?? '');
                $content = sanitizeInput($_POST['content'] ?? '');
                $type = sanitizeInput($_POST['announcement_type'] ?? 'info');
                $targetRoles = $_POST['target_roles'] ?? ['all'];
                $isPinned = isset($_POST['is_pinned']) ? 1 : 0;
                $expiresAt = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;

                if (empty($title) || empty($content)) {
                    throw new Exception('Title and content are required.');
                }

                $sql = "INSERT INTO system_announcements (title, content, announcement_type, target_roles, is_active, is_pinned, expires_at, created_by)
                        VALUES (?, ?, ?, ?, TRUE, ?, ?, ?)";

                $db->execute($sql, [
                    $title,
                    $content,
                    $type,
                    json_encode($targetRoles),
                    $isPinned,
                    $expiresAt,
                    $currentUser['id']
                ]);

                redirectWithMessage('announcements.php', 'Announcement created successfully!', 'success');
                break;

            case 'toggle':
                $id = (int)$_POST['id'];
                $db->execute("UPDATE system_announcements SET is_active = NOT is_active WHERE id = ?", [$id]);
                redirectWithMessage('announcements.php', 'Announcement status updated!', 'success');
                break;

            case 'delete':
                $id = (int)$_POST['id'];
                $db->execute("DELETE FROM system_announcements WHERE id = ?", [$id]);
                redirectWithMessage('announcements.php', 'Announcement deleted!', 'success');
                break;
        }
    } catch (Exception $e) {
        redirectWithMessage('announcements.php', 'Error: ' . $e->getMessage(), 'error');
    }
}

// Create table if it doesn't exist
try {
    $db->execute("CREATE TABLE IF NOT EXISTS system_announcements (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        announcement_type ENUM('info', 'warning', 'urgent', 'success') DEFAULT 'info',
        target_roles JSON,
        is_active BOOLEAN DEFAULT TRUE,
        is_pinned BOOLEAN DEFAULT FALSE,
        expires_at DATETIME NULL,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id)
    )");
} catch (Exception $e) {
    // Table might already exist
}

// Pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Get total count and announcements
$totalCount = 0;
$announcements = [];
try {
    $countResult = $db->fetch("SELECT COUNT(*) as count FROM system_announcements");
    $totalCount = $countResult ? $countResult['count'] : 0;
    $totalPages = ceil($totalCount / $limit);

    // Get announcements with pagination
    $announcements = $db->fetchAll("
        SELECT sa.*, u.first_name, u.last_name
        FROM system_announcements sa
        JOIN users u ON sa.created_by = u.id
        ORDER BY sa.is_pinned DESC, sa.created_at DESC
        LIMIT ? OFFSET ?
    ", [$limit, $offset]);
} catch (Exception $e) {
    // Table might not exist yet
    $totalPages = 0;
}

$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Announcements - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 2px 10px rgba(139,0,0,0.3);">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?> - Admin
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-dashboard"></i> Dashboard
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-bullhorn"></i> System Announcements</h2>
                    <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#announcementModal">
                        <i class="fas fa-plus"></i> Create Announcement
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-white" style="background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);">
                    <div class="card-body">
                        <h5>Total Announcements</h5>
                        <h3><?php echo number_format($totalCount); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white" style="background: linear-gradient(135deg, #B22222 0%, #DC143C 100%);">
                    <div class="card-body">
                        <h5>Active</h5>
                        <h3><?php
                            $activeResult = $db->fetch("SELECT COUNT(*) as count FROM system_announcements WHERE is_active = TRUE");
                            echo number_format($activeResult ? $activeResult['count'] : 0);
                        ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white" style="background: linear-gradient(135deg, #800000 0%, #B22222 100%);">
                    <div class="card-body">
                        <h5>Pinned</h5>
                        <h3><?php
                            $pinnedResult = $db->fetch("SELECT COUNT(*) as count FROM system_announcements WHERE is_pinned = TRUE");
                            echo number_format($pinnedResult ? $pinnedResult['count'] : 0);
                        ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white" style="background: linear-gradient(135deg, #A0522D 0%, #8B0000 100%);">
                    <div class="card-body">
                        <h5>Expired</h5>
                        <h3><?php
                            $expiredResult = $db->fetch("SELECT COUNT(*) as count FROM system_announcements WHERE expires_at IS NOT NULL AND expires_at < NOW()");
                            echo number_format($expiredResult ? $expiredResult['count'] : 0);
                        ?></h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Announcements Table -->
        <div class="card">
            <div class="card-header">
                <h5>All Announcements</h5>
            </div>
            <div class="card-body">
                <?php if (empty($announcements)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No announcements yet</h5>
                        <p class="text-muted">Create your first system announcement to communicate with users.</p>
                        <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#announcementModal">
                            <i class="fas fa-plus"></i> Create First Announcement
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Type</th>
                                    <th>Target Audience</th>
                                    <th>Status</th>
                                    <th>Views</th>
                                    <th>Created</th>
                                    <th>Expires</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($announcements as $announcement): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($announcement['title']); ?></strong>
                                                <?php if ($announcement['is_pinned']): ?>
                                                    <i class="fas fa-thumbtack text-warning ms-1" title="Pinned"></i>
                                                <?php endif; ?>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars(substr($announcement['content'], 0, 100)); ?>
                                                    <?php if (strlen($announcement['content']) > 100): ?>...<?php endif; ?>
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $announcement['announcement_type'] === 'urgent' ? 'danger' : ($announcement['announcement_type'] === 'warning' ? 'warning' : ($announcement['announcement_type'] === 'success' ? 'success' : 'info')); ?>">
                                                <?php echo ucfirst($announcement['announcement_type']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php 
                                            $targets = json_decode($announcement['target_roles'], true);
                                            echo implode(', ', array_map('ucfirst', $targets));
                                            ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $announcement['is_active'] ? 'success' : 'secondary'; ?>">
                                                <?php echo $announcement['is_active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                            <?php if ($announcement['expires_at'] && strtotime($announcement['expires_at']) < time()): ?>
                                                <br><small class="text-danger">Expired</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo number_format($announcement['view_count'] ?? 0); ?></span>
                                        </td>
                                        <td>
                                            <?php echo formatDate($announcement['created_at']); ?>
                                            <br>
                                            <small class="text-muted">
                                                by <?php echo htmlspecialchars($announcement['first_name'] . ' ' . $announcement['last_name']); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if ($announcement['expires_at']): ?>
                                                <?php echo formatDate($announcement['expires_at']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">Never</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewAnnouncement(<?php echo $announcement['id']; ?>)" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-<?php echo $announcement['is_active'] ? 'warning' : 'success'; ?>" 
                                                        onclick="toggleAnnouncement(<?php echo $announcement['id']; ?>)"
                                                        title="<?php echo $announcement['is_active'] ? 'Deactivate' : 'Activate'; ?>">
                                                    <i class="fas fa-<?php echo $announcement['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteAnnouncement(<?php echo $announcement['id']; ?>)" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Announcements pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Announcement Modal (same as in index.php) -->
    <div class="modal fade" id="announcementModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create System Announcement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="announcementForm" action="create-announcement.php" method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="announcementTitle" class="form-label">Title *</label>
                            <input type="text" class="form-control" id="announcementTitle" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="announcementContent" class="form-label">Content *</label>
                            <textarea class="form-control" id="announcementContent" name="content" rows="4" required></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="announcementType" class="form-label">Type</label>
                                    <select class="form-select" id="announcementType" name="announcement_type">
                                        <option value="info">Info</option>
                                        <option value="warning">Warning</option>
                                        <option value="urgent">Urgent</option>
                                        <option value="success">Success</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expiresAt" class="form-label">Expires At (Optional)</label>
                                    <input type="datetime-local" class="form-control" id="expiresAt" name="expires_at">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Target Audience *</label>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetAll" name="target_roles[]" value="all" checked>
                                        <label class="form-check-label" for="targetAll">All Users</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetDonors" name="target_roles[]" value="donors">
                                        <label class="form-check-label" for="targetDonors">Donors</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetRecipients" name="target_roles[]" value="recipients">
                                        <label class="form-check-label" for="targetRecipients">Recipients</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetUnified" name="target_roles[]" value="unified">
                                        <label class="form-check-label" for="targetUnified">Unified Users</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isPinned" name="is_pinned" value="1">
                            <label class="form-check-label" for="isPinned">Pin this announcement</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Create Announcement</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Announcement management functions
        function viewAnnouncement(id) {
            // For now, just show an alert - can be enhanced later
            alert('View announcement functionality - ID: ' + id);
        }

        function toggleAnnouncement(id) {
            if (confirm('Are you sure you want to toggle this announcement status?')) {
                fetch('toggle-announcement.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
            }
        }

        function deleteAnnouncement(id) {
            if (confirm('Are you sure you want to delete this announcement? This action cannot be undone.')) {
                fetch('delete-announcement.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
            }
        }

        // Handle target audience selection
        document.getElementById('targetAll').addEventListener('change', function() {
            const otherCheckboxes = document.querySelectorAll('input[name="target_roles[]"]:not(#targetAll)');
            if (this.checked) {
                otherCheckboxes.forEach(cb => cb.checked = false);
            }
        });

        document.querySelectorAll('input[name="target_roles[]"]:not(#targetAll)').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    document.getElementById('targetAll').checked = false;
                }
            });
        });
    </script>
</body>
</html>
