<?php
/**
 * Admin Users Management
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';
require_once '../classes/Donor.php';
require_once '../classes/Recipient.php';
require_once '../includes/audit.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('users.php', 'Invalid security token', 'error');
    }

    // Handle bulk actions
    if (isset($_POST['bulk_action']) && isset($_POST['selected_users'])) {
        $bulkAction = $_POST['bulk_action'];
        $selectedUsers = array_map('intval', $_POST['selected_users']);
        $successCount = 0;
        $errorCount = 0;

        foreach ($selectedUsers as $userId) {
            if ($userId > 0) {
                try {
                    $user = new User($userId);

                    switch ($bulkAction) {
                        case 'suspend':
                            $user->suspend();
                            logAdminAction('BULK_USER_SUSPEND', ['user_id' => $userId, 'username' => $user->getUsername()]);
                            $successCount++;
                            break;

                        case 'activate':
                            $user->activate();
                            logAdminAction('BULK_USER_ACTIVATE', ['user_id' => $userId, 'username' => $user->getUsername()]);
                            $successCount++;
                            break;

                        case 'delete':
                            logAdminAction('BULK_USER_DELETE', ['user_id' => $userId, 'username' => $user->getUsername()]);
                            $user->delete();
                            $successCount++;
                            break;
                    }
                } catch (Exception $e) {
                    $errorCount++;
                    logEvent('ERROR', 'Bulk action failed', ['user_id' => $userId, 'action' => $bulkAction, 'error' => $e->getMessage()]);
                }
            }
        }

        $message = "Bulk action completed: {$successCount} users processed successfully";
        if ($errorCount > 0) {
            $message .= ", {$errorCount} errors occurred";
        }

        redirectWithMessage('users.php', $message, $errorCount > 0 ? 'warning' : 'success');
    }

    // Handle single user actions
    $action = $_POST['action'] ?? '';
    $userId = (int)($_POST['user_id'] ?? 0);

    if ($userId > 0) {
        $user = new User($userId);

        switch ($action) {
            case 'suspend':
                logAdminAction('USER_SUSPEND', ['user_id' => $userId, 'username' => $user->getUsername()]);
                $user->suspend();
                redirectWithMessage('users.php', 'User suspended successfully', 'success');
                break;

            case 'activate':
                logAdminAction('USER_ACTIVATE', ['user_id' => $userId, 'username' => $user->getUsername()]);
                $user->activate();
                redirectWithMessage('users.php', 'User activated successfully', 'success');
                break;

            case 'archive':
                logAdminAction('USER_ARCHIVE', ['user_id' => $userId, 'username' => $user->getUsername()]);
                $user->archive(getCurrentUser()['id'], 'Archived by admin');
                redirectWithMessage('users.php', 'User archived successfully', 'success');
                break;

            case 'restore':
                logAdminAction('USER_RESTORE', ['user_id' => $userId, 'username' => $user->getUsername()]);
                $user->restore(getCurrentUser()['id'], 'Restored by admin');
                redirectWithMessage('users.php', 'User restored successfully', 'success');
                break;
        }
    }
}

// Get filters - Default to showing active users only
$filters = [
    'user_type' => sanitizeInput($_GET['user_type'] ?? ''),
    'status' => sanitizeInput($_GET['status'] ?? 'active'), // Default to active users
    'search' => sanitizeInput($_GET['search'] ?? '')
];

// Remove pagination - show all users for direct access
$result = User::getAll(1, 999999, $filters); // Large number to get all users
$users = $result['users'];
$pagination = $result['pagination'];

// Get blood types for filter
$bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");

// Get flash message
$flash = getFlashMessage();
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.php">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>

                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5><i class="fas fa-users"></i> User Management</h5>
                                <small class="text-muted">Manage system users and their roles</small>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group">
                                    <button class="btn btn-outline-danger" id="bulkActionsBtn" disabled>
                                        <i class="fas fa-tasks"></i> Bulk Actions
                                    </button>
                                    <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                        <i class="fas fa-plus"></i> Add User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <!-- User Count Information -->
                        <div class="alert alert-info d-flex align-items-center mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>Viewing Active Users Only</strong><br>
                                <small>By default, only active users are shown. Use the status filter to view all users including suspended and pending accounts.</small>
                            </div>
                        </div>

                        <!-- Enhanced Filters -->
                        <div class="admin-panel-section">
                            <div class="section-header">
                                <h5><i class="fas fa-filter"></i> Filter & Search Users</h5>
                            </div>
                            <div class="section-body">
                                <form method="GET" class="row g-3 align-items-end">
                                    <div class="col-md-3">
                                        <label class="form-label">User Type</label>
                                        <select name="user_type" class="form-select">
                                            <option value="">All Types</option>
                                            <option value="donor" <?php echo $filters['user_type'] === 'donor' ? 'selected' : ''; ?>>Donors</option>
                                            <option value="recipient" <?php echo $filters['user_type'] === 'recipient' ? 'selected' : ''; ?>>Recipients</option>
                                            <option value="unified" <?php echo $filters['user_type'] === 'unified' ? 'selected' : ''; ?>>Unified</option>
                                            <option value="admin" <?php echo $filters['user_type'] === 'admin' ? 'selected' : ''; ?>>Admins</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select">
                                            <option value="">All Status</option>
                                            <option value="active" <?php echo $filters['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="suspended" <?php echo $filters['status'] === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                            <option value="pending" <?php echo $filters['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Search</label>
                                        <div class="input-group">
                                            <input type="text" name="search" class="form-control" placeholder="Search by name or username..."
                                                   value="<?php echo htmlspecialchars($filters['search']); ?>">
                                            <button type="submit" class="btn btn-danger">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-outline-danger">
                                                <i class="fas fa-filter"></i> Apply
                                            </button>
                                            <a href="users.php" class="btn btn-outline-secondary" title="Clear filters">
                                                <i class="fas fa-times"></i> Clear
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Users Table -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th width="60">ID</th>
                                        <th>User</th>
                                        <th>Contact</th>
                                        <th>Type & Roles</th>
                                        <th>Status</th>
                                        <th>Last Activity</th>
                                        <th width="120">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($users)): ?>
                                        <tr>
                                            <td colspan="8" class="text-center text-muted py-4">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <br>No users found matching your criteria
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($users as $user): ?>
                                            <tr class="user-row" data-user-id="<?php echo $user['id']; ?>" style="cursor: pointer;" onclick="showUserInfo(<?php echo $user['id']; ?>, event)">
                                                <td>
                                                    <input type="checkbox" class="form-check-input user-checkbox"
                                                           value="<?php echo $user['id']; ?>" name="selected_users[]">
                                                </td>
                                                <td><span class="badge bg-light text-dark"><?php echo $user['id']; ?></span></td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="user-avatar-container me-3">
                                                            <?php if ($user['profile_photo']): ?>
                                                                <img src="../uploads/<?php echo htmlspecialchars($user['profile_photo']); ?>"
                                                                     class="rounded-circle" width="40" height="40" data-user-id="<?php echo $user['id']; ?>">
                                                            <?php else: ?>
                                                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center"
                                                                     style="width: 40px; height: 40px;" data-user-id="<?php echo $user['id']; ?>">
                                                                    <i class="fas fa-user text-white"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                            <?php
                                                            // Determine user status (active/inactive based on last activity)
                                                            $userStatus = getUserStatus($user['id']);
                                                            $statusClass = ($userStatus === 'Active') ? 'active' : 'inactive';
                                                            ?>
                                                            <div class="user-status-indicator <?php echo $statusClass; ?>"
                                                                 title="User is <?php echo strtolower($userStatus); ?>"></div>
                                                        </div>
                                                        <div>
                                                            <div class="fw-bold"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                                            <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <div class="small text-muted">No email required</div>
                                                        <?php if ($user['phone']): ?>
                                                            <small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($user['user_type'] === 'unified'): ?>
                                                        <span class="badge bg-primary mb-1">
                                                            <i class="fas fa-user-tag"></i> Unified
                                                        </span>
                                                        <?php
                                                        // Get user roles for unified users
                                                        try {
                                                            $userRoles = $db->fetchAll("SELECT role_type FROM user_roles WHERE user_id = ? AND is_active = TRUE", [$user['id']]);
                                                            if (empty($userRoles)) {
                                                                echo '<br><small class="badge bg-secondary">No Roles</small>';
                                                            } else {
                                                                foreach ($userRoles as $role):
                                                                ?>
                                                                    <br><small class="badge bg-<?php echo $role['role_type'] === 'donor' ? 'success' : 'info'; ?>">
                                                                        <i class="fas fa-<?php echo $role['role_type'] === 'donor' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                                                                        <?php echo ucfirst($role['role_type']); ?>
                                                                    </small>
                                                                <?php
                                                                endforeach;
                                                            }
                                                        } catch (Exception $e) {
                                                            // If user_roles table doesn't exist or has issues, show fallback
                                                            echo '<br><small class="badge bg-secondary">Standard User</small>';
                                                        }
                                                        ?>
                                                    <?php else: ?>
                                                        <span class="badge bg-<?php echo $user['user_type'] === 'donor' ? 'success' : ($user['user_type'] === 'admin' ? 'danger' : 'info'); ?>">
                                                            <i class="fas fa-<?php echo $user['user_type'] === 'donor' ? 'heart' : ($user['user_type'] === 'admin' ? 'shield-alt' : 'hand-holding-medical'); ?>"></i>
                                                            <?php echo ucfirst($user['user_type']); ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <button class="status-toggle <?php echo $user['status']; ?>"
                                                            onclick="toggleUserStatus(<?php echo $user['id']; ?>, '<?php echo $user['status']; ?>')"
                                                            title="Click to <?php echo $user['status'] === 'active' ? 'suspend' : 'activate'; ?> user">
                                                        <i class="fas fa-<?php echo $user['status'] === 'active' ? 'check-circle' : 'ban'; ?> me-1"></i>
                                                        <?php echo ucfirst($user['status']); ?>
                                                    </button>
                                                </td>
                                                <td>
                                                    <div class="small">
                                                        <?php if ($user['last_login']): ?>
                                                            <div>Last login: <?php echo formatDate($user['last_login']); ?></div>
                                                        <?php else: ?>
                                                            <div class="text-muted">Never logged in</div>
                                                        <?php endif; ?>
                                                        <small class="text-muted">Joined: <?php echo formatDate($user['created_at']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="viewUser(<?php echo $user['id']; ?>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <?php if ($user['status'] === 'active'): ?>
                                                            <button class="btn btn-outline-warning" onclick="suspendUser(<?php echo $user['id']; ?>)">
                                                                <i class="fas fa-ban"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button class="btn btn-outline-success" onclick="activateUser(<?php echo $user['id']; ?>)">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        <?php if (isset($user['is_archived']) && $user['is_archived']): ?>
                                                            <button class="btn btn-outline-info" onclick="restoreUser(<?php echo $user['id']; ?>)" title="Restore User">
                                                                <i class="fas fa-undo"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button class="btn btn-outline-secondary" onclick="archiveUser(<?php echo $user['id']; ?>)" title="Archive User">
                                                                <i class="fas fa-archive"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination Removed - Direct User Access Implementation -->
                    </div>
                </div>
            </div>
        </div>

        <!-- User Information Panel -->
        <div class="row" id="userInfoPanel" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-user"></i> User Information</h5>
                            <button class="btn btn-outline-secondary btn-sm" onclick="hideUserInfo()">
                                <i class="fas fa-times"></i> Close
                            </button>
                        </div>
                    </div>
                    <div class="card-body" id="userInfoContent">
                        <!-- User information will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Form (Hidden) -->
    <form id="actionForm" method="POST" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
        <input type="hidden" name="action" id="actionType">
        <input type="hidden" name="user_id" id="actionUserId">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/online-status.js"></script>
    <script>
        function viewUser(userId) {
            window.location.href = 'user-details.php?id=' + userId;
        }

        function suspendUser(userId) {
            if (confirm('Are you sure you want to suspend this user?')) {
                document.getElementById('actionType').value = 'suspend';
                document.getElementById('actionUserId').value = userId;
                document.getElementById('actionForm').submit();
            }
        }

        function activateUser(userId) {
            if (confirm('Are you sure you want to activate this user?')) {
                document.getElementById('actionType').value = 'activate';
                document.getElementById('actionUserId').value = userId;
                document.getElementById('actionForm').submit();
            }
        }

        function archiveUser(userId) {
            if (confirm('Are you sure you want to archive this user? This will preserve their data but make them inactive.')) {
                document.getElementById('actionType').value = 'archive';
                document.getElementById('actionUserId').value = userId;
                document.getElementById('actionForm').submit();
            }
        }

        function restoreUser(userId) {
            if (confirm('Are you sure you want to restore this archived user?')) {
                document.getElementById('actionType').value = 'restore';
                document.getElementById('actionUserId').value = userId;
                document.getElementById('actionForm').submit();
            }
        }

        function toggleUserStatus(userId, currentStatus) {
            const action = currentStatus === 'active' ? 'suspend' : 'activate';
            const message = currentStatus === 'active' ?
                'Are you sure you want to suspend this user?' :
                'Are you sure you want to activate this user?';

            if (confirm(message)) {
                document.getElementById('actionType').value = action;
                document.getElementById('actionUserId').value = userId;
                document.getElementById('actionForm').submit();
            }
        }

        // Bulk actions functionality
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');
            const bulkActionsBtn = document.getElementById('bulkActionsBtn');

            // Handle select all checkbox
            selectAllCheckbox.addEventListener('change', function() {
                userCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActionsButton();
            });

            // Handle individual checkboxes
            userCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBulkActionsButton();

                    // Update select all checkbox state
                    const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
                    selectAllCheckbox.checked = checkedCount === userCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < userCheckboxes.length;
                });
            });

            function updateBulkActionsButton() {
                const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
                bulkActionsBtn.disabled = checkedCount === 0;
                bulkActionsBtn.innerHTML = checkedCount > 0 ?
                    `<i class="fas fa-tasks"></i> Bulk Actions (${checkedCount})` :
                    '<i class="fas fa-tasks"></i> Bulk Actions';
            }

            // Bulk actions dropdown
            bulkActionsBtn.addEventListener('click', function() {
                const checkedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
                if (checkedUsers.length === 0) return;

                const action = prompt(`Selected ${checkedUsers.length} users. Choose action:\n1. Activate\n2. Suspend\n3. Delete\n\nEnter number (1-3):`);

                if (action === '1') {
                    if (confirm(`Activate ${checkedUsers.length} selected users?`)) {
                        performBulkAction('activate', checkedUsers);
                    }
                } else if (action === '2') {
                    if (confirm(`Suspend ${checkedUsers.length} selected users?`)) {
                        performBulkAction('suspend', checkedUsers);
                    }
                } else if (action === '3') {
                    if (confirm(`DELETE ${checkedUsers.length} selected users? This cannot be undone!`)) {
                        performBulkAction('delete', checkedUsers);
                    }
                }
            });

            function performBulkAction(action, userIds) {
                // Create a form to submit bulk action
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                // Add CSRF token
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrf_token';
                csrfInput.value = '<?php echo $csrfToken; ?>';
                form.appendChild(csrfInput);

                // Add action
                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'bulk_action';
                actionInput.value = action;
                form.appendChild(actionInput);

                // Add user IDs
                userIds.forEach(userId => {
                    const userInput = document.createElement('input');
                    userInput.type = 'hidden';
                    userInput.name = 'selected_users[]';
                    userInput.value = userId;
                    form.appendChild(userInput);
                });

                document.body.appendChild(form);
                form.submit();
            }

            // User Information Display Functions
            window.showUserInfo = function(userId, event) {
                // Prevent checkbox click from triggering row click
                if (event) {
                    event.stopPropagation();
                }

                // Show loading state
                const panel = document.getElementById('userInfoPanel');
                const content = document.getElementById('userInfoContent');
                content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading user information...</div>';
                panel.style.display = 'block';

                // Scroll to the panel
                panel.scrollIntoView({ behavior: 'smooth' });

                // Fetch user information via AJAX
                fetch(`user-details-ajax.php?id=${userId}`)
                    .then(response => response.text())
                    .then(data => {
                        content.innerHTML = data;
                    })
                    .catch(error => {
                        content.innerHTML = '<div class="alert alert-danger">Error loading user information. Please try again.</div>';
                        console.error('Error:', error);
                    });
            };

            window.hideUserInfo = function() {
                document.getElementById('userInfoPanel').style.display = 'none';
            };

            // Prevent checkbox clicks from triggering row clicks
            document.addEventListener('click', function(e) {
                if (e.target.type === 'checkbox') {
                    e.stopPropagation();
                }
            });
        });
    </script>
</body>
</html>
