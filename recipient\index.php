<?php
/**
 * Recipient Dashboard
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/Recipient.php';
require_once '../classes/Notification.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');

// Redirect unified users to the new dashboard
$currentUser = getCurrentUser();
if ($currentUser['user_type'] === 'unified') {
    redirectWithMessage('../dashboard/', 'Please use the unified dashboard for better experience.', 'info');
}

requirePermission(USER_TYPE_RECIPIENT, '../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

// Load recipient profile
$recipient = new Recipient($currentUser['id']);

// Get recipient statistics
$recipientStats = $recipient->getRequestStatistics();

// Get active blood requests
$activeRequests = $recipient->getActiveBloodRequests();

// Get recent notifications
$notificationResult = Notification::getUserNotifications($currentUser['id'], 1, 5);
$notifications = $notificationResult['notifications'];

// Get recent request history
$requestHistoryResult = $recipient->getBloodRequestHistory(1, 5);
$recentRequests = $requestHistoryResult['requests'];

// Get flash message
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recipient Dashboard - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-hand-holding-medical"></i> <?php echo APP_NAME; ?> - Recipient
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical"></i> My Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="request-blood.php">
                            <i class="fas fa-plus"></i> Request Blood
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donors.php">
                            <i class="fas fa-heart"></i> Find Donors
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <?php if (Notification::getUnreadCount($currentUser['id']) > 0): ?>
                                <span class="badge bg-warning notification-badge"><?php echo Notification::getUnreadCount($currentUser['id']); ?></span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <li><h6 class="dropdown-header">Recent Notifications</h6></li>
                            <?php if (empty($notifications)): ?>
                                <li><span class="dropdown-item-text text-muted">No notifications</span></li>
                            <?php else: ?>
                                <?php foreach (array_slice($notifications, 0, 3) as $notification): ?>
                                    <li>
                                        <a class="dropdown-item <?php echo !$notification['is_read'] ? 'fw-bold' : ''; ?>" href="notifications.php">
                                            <div class="d-flex w-100 justify-content-between">
                                                <small><?php echo htmlspecialchars($notification['title']); ?></small>
                                                <small><?php echo timeAgo($notification['created_at']); ?></small>
                                            </div>
                                            <small class="text-muted"><?php echo htmlspecialchars(substr($notification['message'], 0, 50)); ?>...</small>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="notifications.php">View All Notifications</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo $currentUser['first_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient" style="background: linear-gradient(135deg, #0d47a1 0%, #2196f3 100%); box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <div class="card-body text-white">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2><i class="fas fa-hand-holding-medical"></i> Welcome back, <?php echo htmlspecialchars($currentUser['first_name']); ?>!</h2>
                                <p class="mb-0">We're here to help you find the blood you need. Our community of donors is ready to help.</p>
                                <?php if ($recipient->getMedicalCondition()): ?>
                                    <p class="mb-0">
                                        <strong>Medical Condition:</strong> <?php echo htmlspecialchars($recipient->getMedicalCondition()); ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 text-end">
                                <?php if (empty($activeRequests)): ?>
                                    <div class="alert alert-light mb-0">
                                        <i class="fas fa-info-circle text-primary"></i> No active requests
                                        <br>
                                        <a href="request-blood.php" class="btn btn-light btn-sm mt-2">
                                            <i class="fas fa-plus"></i> Request Blood
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-warning mb-0">
                                        <i class="fas fa-clock"></i> 
                                        You have <?php echo count($activeRequests); ?> active request(s)
                                        <br>
                                        <a href="requests.php" class="btn btn-light btn-sm mt-2">
                                            <i class="fas fa-eye"></i> View Requests
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card dashboard-card bg-primary text-white">
                    <div class="card-body position-relative">
                        <h5 class="card-title">Total Requests</h5>
                        <p class="card-text"><?php echo number_format($recipientStats['total_requests']); ?></p>
                        <i class="fas fa-hand-holding-medical card-icon"></i>
                    </div>
                    <div class="card-footer">
                        <small>All time requests</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card dashboard-card bg-warning text-white">
                    <div class="card-body position-relative">
                        <h5 class="card-title">Pending</h5>
                        <p class="card-text"><?php echo number_format($recipientStats['pending_requests']); ?></p>
                        <i class="fas fa-clock card-icon"></i>
                    </div>
                    <div class="card-footer">
                        <small>Awaiting approval</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card dashboard-card bg-success text-white">
                    <div class="card-body position-relative">
                        <h5 class="card-title">Fulfilled</h5>
                        <p class="card-text"><?php echo number_format($recipientStats['fulfilled_requests']); ?></p>
                        <i class="fas fa-check-circle card-icon"></i>
                    </div>
                    <div class="card-footer">
                        <small>Successfully completed</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card dashboard-card bg-info text-white">
                    <div class="card-body position-relative">
                        <h5 class="card-title">Units Received</h5>
                        <p class="card-text"><?php echo number_format($recipientStats['fulfilled_units']); ?></p>
                        <i class="fas fa-tint card-icon"></i>
                    </div>
                    <div class="card-footer">
                        <small>Total units</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <!-- Active Blood Requests -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-exclamation-circle"></i> Active Blood Requests</h5>
                            <a href="requests.php" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($activeRequests)): ?>
                            <p class="text-muted text-center">No active blood requests.</p>
                            <div class="text-center">
                                <a href="request-blood.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Request Blood
                                </a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($activeRequests as $request): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div>
                                        <strong>Request #<?php echo $request['id']; ?></strong>
                                        <br>
                                        <span class="badge bg-danger"><?php echo htmlspecialchars($request['blood_type']); ?></span>
                                        <span class="badge urgency-<?php echo $request['urgency_level']; ?>">
                                            <?php echo strtoupper($request['urgency_level']); ?>
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-hospital"></i> <?php echo htmlspecialchars($request['hospital_name']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <strong><?php echo $request['units_needed']; ?> units</strong>
                                        <br>
                                        <span class="badge status-<?php echo $request['status']; ?>">
                                            <?php echo ucfirst($request['status']); ?>
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            Due: <?php echo formatDate($request['required_by_date']); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-3">
                            <a href="request-blood.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus"></i> Request Blood
                                <br>
                                <small>Submit a new blood request</small>
                            </a>
                            
                            <a href="donors.php" class="btn btn-success btn-lg">
                                <i class="fas fa-search"></i> Find Compatible Donors
                                <br>
                                <small>Search for donors who can help</small>
                            </a>
                            
                            <a href="requests.php" class="btn btn-info btn-lg">
                                <i class="fas fa-history"></i> View Request History
                                <br>
                                <small>Track your past requests</small>
                            </a>
                            
                            <a href="profile.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-user-edit"></i> Update Profile
                                <br>
                                <small>Manage your information</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Request History -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-history"></i> Recent Request History</h5>
                            <a href="requests.php" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentRequests)): ?>
                            <p class="text-muted text-center">No request history available.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Request ID</th>
                                            <th>Blood Type</th>
                                            <th>Units</th>
                                            <th>Urgency</th>
                                            <th>Hospital</th>
                                            <th>Required By</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentRequests as $request): ?>
                                            <tr>
                                                <td>#<?php echo $request['id']; ?></td>
                                                <td>
                                                    <span class="badge bg-danger"><?php echo htmlspecialchars($request['blood_type']); ?></span>
                                                </td>
                                                <td><?php echo $request['units_needed']; ?></td>
                                                <td>
                                                    <span class="badge urgency-<?php echo $request['urgency_level']; ?>">
                                                        <?php echo strtoupper($request['urgency_level']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($request['hospital_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($request['hospital_address']); ?></small>
                                                </td>
                                                <td>
                                                    <?php 
                                                    $requiredDate = new DateTime($request['required_by_date']);
                                                    $today = new DateTime();
                                                    $isOverdue = $requiredDate < $today && $request['status'] !== 'fulfilled';
                                                    ?>
                                                    <span class="<?php echo $isOverdue ? 'text-danger fw-bold' : ''; ?>">
                                                        <?php echo formatDate($request['required_by_date']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge status-<?php echo $request['status']; ?>">
                                                        <?php echo ucfirst($request['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatDate($request['created_at']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // Auto-refresh dashboard every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
