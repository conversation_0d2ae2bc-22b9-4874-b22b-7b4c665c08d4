const DatabaseHelper = require('./database-helper');
const TestDataGenerator = require('./test-data');

async function globalSetup() {
  console.log('🚀 Starting global test setup...');
  
  const dbHelper = new DatabaseHelper();
  
  try {
    // Verify database connection
    await dbHelper.connect();
    console.log('✅ Database connection established');
    
    // Verify database schema
    const schemaResults = await dbHelper.verifyDatabaseSchema();
    const missingTables = Object.entries(schemaResults)
      .filter(([table, exists]) => !exists)
      .map(([table]) => table);
    
    if (missingTables.length > 0) {
      console.error('❌ Missing database tables:', missingTables);
      throw new Error(`Missing required database tables: ${missingTables.join(', ')}`);
    }
    console.log('✅ Database schema verified');
    
    // Clean up any existing test data
    await dbHelper.cleanupTestUsers();
    console.log('✅ Cleaned up existing test data');
    
    // Create test users for scenarios
    const scenarios = TestDataGenerator.getTestScenarios();
    
    for (const [scenarioName, scenario] of Object.entries(scenarios)) {
      try {
        const user = await dbHelper.createTestUser(scenario.user);
        console.log(`✅ Created test user for scenario: ${scenarioName} (ID: ${user.id})`);
        
        // Add roles if specified
        if (scenario.roles) {
          for (const role of scenario.roles) {
            await dbHelper.addUserRole(user.id, role);
            console.log(`✅ Added ${role} role to user ${user.id}`);
          }
        }
      } catch (error) {
        console.error(`❌ Failed to create test user for scenario ${scenarioName}:`, error.message);
      }
    }
    
    // Verify blood types exist
    const bloodTypes = await dbHelper.getBloodTypes();
    if (bloodTypes.length === 0) {
      console.warn('⚠️ No blood types found in database. Some tests may fail.');
    } else {
      console.log(`✅ Found ${bloodTypes.length} blood types in database`);
    }
    
    console.log('🎉 Global test setup completed successfully');
    
  } catch (error) {
    console.error('❌ Global test setup failed:', error.message);
    throw error;
  } finally {
    await dbHelper.disconnect();
  }
}

module.exports = globalSetup;
