<?php
/**
 * Admin Blood Matching System
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();

// Handle matching request
$matchResults = [];
$selectedRequest = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_id'])) {
    $requestId = (int)$_POST['request_id'];
    
    // Get the blood request details
    $selectedRequest = $db->fetchOne("SELECT 
        br.*, bt.type as blood_type,
        u.first_name, u.last_name, u.email, u.phone
        FROM blood_requests br
        JOIN blood_types bt ON br.blood_type_id = bt.id
        JOIN users u ON br.user_id = u.id
        WHERE br.id = ? AND br.status = ?", 
        [$requestId, REQUEST_STATUS_PENDING]);
    
    if ($selectedRequest) {
        // Find compatible donors
        $compatibilityMap = [
            'O-' => ['O-', 'O+', 'A-', 'A+', 'B-', 'B+', 'AB-', 'AB+'],
            'O+' => ['O+', 'A+', 'B+', 'AB+'],
            'A-' => ['A-', 'A+', 'AB-', 'AB+'],
            'A+' => ['A+', 'AB+'],
            'B-' => ['B-', 'B+', 'AB-', 'AB+'],
            'B+' => ['B+', 'AB+'],
            'AB-' => ['AB-', 'AB+'],
            'AB+' => ['AB+']
        ];
        
        $compatibleTypes = [];
        foreach ($compatibilityMap as $donorType => $canDonateTo) {
            if (in_array($selectedRequest['blood_type'], $canDonateTo)) {
                $compatibleTypes[] = $donorType;
            }
        }
        
        if (!empty($compatibleTypes)) {
            $placeholders = str_repeat('?,', count($compatibleTypes) - 1) . '?';
            
            // Find available donors
            $matchResults = $db->fetchAll("SELECT 
                u.id, u.first_name, u.last_name, u.email, u.phone,
                bt.type as blood_type, u.city, u.state,
                COALESCE(MAX(d.donation_date), '1970-01-01') as last_donation,
                DATEDIFF(NOW(), COALESCE(MAX(d.donation_date), '1970-01-01')) as days_since_donation
                FROM users u
                JOIN blood_types bt ON u.blood_type_id = bt.id
                LEFT JOIN donations d ON u.id = d.donor_id AND d.status = ?
                WHERE u.user_type = ? 
                AND u.status = 'active'
                AND bt.type IN ($placeholders)
                AND (COALESCE(MAX(d.donation_date), '1970-01-01') <= DATE_SUB(NOW(), INTERVAL ? DAY) OR MAX(d.donation_date) IS NULL)
                GROUP BY u.id, u.first_name, u.last_name, u.email, u.phone, bt.type, u.city, u.state
                ORDER BY days_since_donation DESC, u.city = ? DESC",
                array_merge([DONATION_STATUS_COMPLETED, USER_TYPE_DONOR], $compatibleTypes, [MIN_DONATION_INTERVAL, $selectedRequest['city'] ?? '']));
        }
    }
}

// Get pending blood requests for the dropdown
$pendingRequests = $db->fetchAll("SELECT 
    br.id, br.urgency_level, br.units_needed,
    bt.type as blood_type,
    u.first_name, u.last_name, u.city
    FROM blood_requests br
    JOIN blood_types bt ON br.blood_type_id = bt.id
    JOIN users u ON br.user_id = u.id
    WHERE br.status = ?
    ORDER BY 
        CASE br.urgency_level 
            WHEN 'critical' THEN 1 
            WHEN 'urgent' THEN 2 
            WHEN 'normal' THEN 3 
        END,
        br.created_at ASC", [REQUEST_STATUS_PENDING]);

$pageTitle = "Blood Matching";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.php">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="matching.php">
                            <i class="fas fa-search nav-icon"></i>
                            <span class="nav-text">Find Matches</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="admin-page-title">
                            <i class="fas fa-search text-danger me-2"></i>Blood Matching System
                        </h2>
                        <p class="text-muted">Find compatible donors for blood requests</p>
                    </div>
                    <div>
                        <a href="requests.php" class="btn btn-outline-danger">
                            <i class="fas fa-hand-holding-medical me-1"></i> View All Requests
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Request Selection -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-hand-holding-medical text-primary me-2"></i>Select Blood Request
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pendingRequests)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <p class="text-muted">No pending blood requests at this time</p>
                                <a href="requests.php" class="btn btn-outline-primary">View All Requests</a>
                            </div>
                        <?php else: ?>
                            <form method="POST" class="row g-3">
                                <div class="col-md-8">
                                    <label for="request_id" class="form-label">Select Request to Find Matches</label>
                                    <select name="request_id" id="request_id" class="form-select" required>
                                        <option value="">Choose a blood request...</option>
                                        <?php foreach ($pendingRequests as $request): ?>
                                            <option value="<?php echo $request['id']; ?>" 
                                                    <?php echo ($selectedRequest && $selectedRequest['id'] == $request['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?> - 
                                                <?php echo htmlspecialchars($request['blood_type']); ?> - 
                                                <?php echo $request['units_needed']; ?> units - 
                                                <span class="text-<?php echo $request['urgency_level'] == 'critical' ? 'danger' : ($request['urgency_level'] == 'urgent' ? 'warning' : 'info'); ?>">
                                                    <?php echo ucfirst($request['urgency_level']); ?>
                                                </span>
                                                <?php if ($request['city']): ?>
                                                    - <?php echo htmlspecialchars($request['city']); ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-danger d-block w-100">
                                        <i class="fas fa-search me-1"></i> Find Compatible Donors
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Matching Results -->
        <?php if ($selectedRequest): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">
                                <i class="fas fa-users text-success me-2"></i>Compatible Donors Found
                                <span class="badge bg-primary ms-2"><?php echo count($matchResults); ?> matches</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Request Details -->
                            <div class="alert alert-info mb-4">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Patient:</strong> <?php echo htmlspecialchars($selectedRequest['first_name'] . ' ' . $selectedRequest['last_name']); ?><br>
                                        <strong>Blood Type:</strong> <span class="badge bg-danger"><?php echo htmlspecialchars($selectedRequest['blood_type']); ?></span><br>
                                        <strong>Units Needed:</strong> <?php echo $selectedRequest['units_needed']; ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Urgency:</strong> 
                                        <span class="badge bg-<?php echo $selectedRequest['urgency_level'] == 'critical' ? 'danger' : ($selectedRequest['urgency_level'] == 'urgent' ? 'warning' : 'info'); ?>">
                                            <?php echo ucfirst($selectedRequest['urgency_level']); ?>
                                        </span><br>
                                        <strong>Location:</strong> <?php echo htmlspecialchars($selectedRequest['city'] ?? 'Not specified'); ?><br>
                                        <strong>Contact:</strong> <?php echo htmlspecialchars($selectedRequest['email']); ?>
                                    </div>
                                </div>
                            </div>

                            <?php if (empty($matchResults)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                    <p class="text-muted">No compatible donors found for this request</p>
                                    <small class="text-muted">Try expanding the search criteria or check back later</small>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Donor</th>
                                                <th>Blood Type</th>
                                                <th>Location</th>
                                                <th>Last Donation</th>
                                                <th>Contact</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($matchResults as $donor): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($donor['first_name'] . ' ' . $donor['last_name']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success"><?php echo htmlspecialchars($donor['blood_type']); ?></span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($donor['city'] . ', ' . $donor['state']); ?></td>
                                                    <td>
                                                        <?php if ($donor['last_donation'] == '1970-01-01'): ?>
                                                            <span class="text-muted">Never donated</span>
                                                        <?php else: ?>
                                                            <?php echo date('M j, Y', strtotime($donor['last_donation'])); ?>
                                                            <small class="text-muted">(<?php echo $donor['days_since_donation']; ?> days ago)</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <small>
                                                            <?php echo htmlspecialchars($donor['email']); ?><br>
                                                            <?php echo htmlspecialchars($donor['phone'] ?? 'No phone'); ?>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary" onclick="contactDonor(<?php echo $donor['id']; ?>)">
                                                            <i class="fas fa-envelope me-1"></i> Contact
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        function contactDonor(donorId) {
            // This would typically open a modal or redirect to a contact form
            alert('Contact functionality would be implemented here for donor ID: ' + donorId);
            // You could implement:
            // - Email notification to donor
            // - SMS notification
            // - Internal messaging system
        }
    </script>
</body>
</html>
