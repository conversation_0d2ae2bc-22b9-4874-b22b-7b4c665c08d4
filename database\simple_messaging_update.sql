-- Simple Messaging Schema Update for Restricted Messaging System
-- Blood Donation Management System

USE blood_donation_system;

-- Add additional indexes for better messaging performance
ALTER TABLE chat_messages 
ADD INDEX IF NOT EXISTS idx_conversation_time (sender_id, receiver_id, sent_at);

ALTER TABLE chat_messages 
ADD INDEX IF NOT EXISTS idx_unread_messages (receiver_id, is_read, sent_at);

ALTER TABLE chat_messages 
ADD INDEX IF NOT EXISTS idx_user_messages (sender_id, sent_at);

ALTER TABLE chat_messages 
ADD INDEX IF NOT EXISTS idx_message_status (is_read, read_at);

-- Create messaging_permissions table for fine-grained control
CREATE TABLE IF NOT EXISTS messaging_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    can_message_admins BOOLEAN DEFAULT TRUE,
    can_message_donors BOOLEAN DEFAULT FALSE,
    can_message_recipients BOOLEAN DEFAULT FALSE,
    can_message_all BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_permissions (user_id),
    INDEX idx_user_permissions (user_id)
);

-- Insert default permissions for existing users
INSERT IGNORE INTO messaging_permissions (user_id, can_message_admins, can_message_donors, can_message_recipients, can_message_all)
SELECT 
    id,
    TRUE as can_message_admins,
    CASE WHEN user_type = 'admin' THEN TRUE ELSE FALSE END as can_message_donors,
    CASE WHEN user_type = 'admin' THEN TRUE ELSE FALSE END as can_message_recipients,
    CASE WHEN user_type = 'admin' THEN TRUE ELSE FALSE END as can_message_all
FROM users 
WHERE status = 'active';

-- Create message_reports table for reporting inappropriate messages
CREATE TABLE IF NOT EXISTS message_reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    reported_by INT NOT NULL,
    reason ENUM('spam', 'harassment', 'inappropriate', 'other') NOT NULL,
    description TEXT,
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
    reviewed_by INT NULL,
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES chat_messages(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_message_reports (message_id),
    INDEX idx_reporter (reported_by),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
