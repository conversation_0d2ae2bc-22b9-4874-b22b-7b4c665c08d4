const mysql = require('mysql2/promise');
require('dotenv').config();

class DatabaseHelper {
  constructor() {
    this.connection = null;
  }

  async connect() {
    if (!this.connection) {
      this.connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASS || '',
        database: process.env.DB_NAME || 'blood_donation_system',
        port: process.env.DB_PORT || 3306
      });
    }
    return this.connection;
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      this.connection = null;
    }
  }

  async query(sql, params = []) {
    const connection = await this.connect();
    const [rows] = await connection.execute(sql, params);
    return rows;
  }

  async createTestUser(userData) {
    const connection = await this.connect();
    
    try {
      await connection.beginTransaction();

      // Insert user
      const [userResult] = await connection.execute(
        `INSERT INTO users (username, first_name, last_name, phone, address, user_type, 
         is_unified_user, registration_source, password, status, created_at) 
         VALUES (?, ?, ?, ?, ?, 'unified', TRUE, 'test', ?, 'active', NOW())`,
        [
          userData.username,
          userData.firstName,
          userData.lastName,
          userData.phone,
          userData.address,
          userData.password
        ]
      );

      const userId = userResult.insertId;

      // Create donor profile if needed
      await connection.execute(
        `INSERT INTO donor_profiles (user_id, created_at) VALUES (?, NOW())`,
        [userId]
      );

      // Create recipient profile if needed
      await connection.execute(
        `INSERT INTO recipient_profiles (user_id, created_at) VALUES (?, NOW())`,
        [userId]
      );

      // Create user preferences
      await connection.execute(
        `INSERT INTO user_preferences (user_id, created_at) VALUES (?, NOW())`,
        [userId]
      );

      await connection.commit();
      return { id: userId, ...userData };
    } catch (error) {
      await connection.rollback();
      throw error;
    }
  }

  async addUserRole(userId, roleType) {
    const connection = await this.connect();
    
    try {
      await connection.beginTransaction();

      // Add role
      await connection.execute(
        `INSERT INTO user_roles (user_id, role_type, is_active, activated_at, created_at) 
         VALUES (?, ?, TRUE, NOW(), NOW())`,
        [userId, roleType]
      );

      // Update primary role if this is the first role
      const [existingRoles] = await connection.execute(
        `SELECT COUNT(*) as count FROM user_roles WHERE user_id = ? AND is_active = TRUE`,
        [userId]
      );

      if (existingRoles[0].count === 1) {
        await connection.execute(
          `UPDATE users SET primary_role = ? WHERE id = ?`,
          [roleType, userId]
        );
      }

      await connection.commit();
    } catch (error) {
      await connection.rollback();
      throw error;
    }
  }

  async getUserRoles(userId) {
    const roles = await this.query(
      `SELECT role_type FROM user_roles WHERE user_id = ? AND is_active = TRUE`,
      [userId]
    );
    return roles.map(row => row.role_type);
  }

  async getUserById(userId) {
    const [user] = await this.query(
      `SELECT * FROM users WHERE id = ?`,
      [userId]
    );
    return user;
  }

  async getUserByUsername(username) {
    const [user] = await this.query(
      `SELECT * FROM users WHERE username = ?`,
      [username]
    );
    return user;
  }

  async cleanupTestUsers() {
    const connection = await this.connect();
    
    try {
      await connection.beginTransaction();

      // Get test user IDs
      const testUsers = await this.query(
        `SELECT id FROM users WHERE username LIKE ? OR registration_source = 'test'`,
        [`${process.env.TEST_USER_PREFIX || 'test_user_'}%`]
      );

      if (testUsers.length > 0) {
        const userIds = testUsers.map(user => user.id);
        const placeholders = userIds.map(() => '?').join(',');

        // Delete related records
        await connection.execute(
          `DELETE FROM user_roles WHERE user_id IN (${placeholders})`,
          userIds
        );
        
        await connection.execute(
          `DELETE FROM user_preferences WHERE user_id IN (${placeholders})`,
          userIds
        );
        
        await connection.execute(
          `DELETE FROM donor_profiles WHERE user_id IN (${placeholders})`,
          userIds
        );
        
        await connection.execute(
          `DELETE FROM recipient_profiles WHERE user_id IN (${placeholders})`,
          userIds
        );
        
        await connection.execute(
          `DELETE FROM users WHERE id IN (${placeholders})`,
          userIds
        );
      }

      await connection.commit();
      console.log(`Cleaned up ${testUsers.length} test users`);
    } catch (error) {
      await connection.rollback();
      throw error;
    }
  }

  async getBloodTypes() {
    return await this.query(`SELECT * FROM blood_types ORDER BY type`);
  }

  async verifyDatabaseSchema() {
    const requiredTables = [
      'users', 'user_roles', 'donor_profiles', 'recipient_profiles',
      'user_preferences', 'blood_types'
    ];

    const results = {};
    for (const table of requiredTables) {
      try {
        await this.query(`SELECT 1 FROM ${table} LIMIT 1`);
        results[table] = true;
      } catch (error) {
        results[table] = false;
      }
    }

    return results;
  }
}

module.exports = DatabaseHelper;
