<?php
/**
 * Security Functions
 * Blood Donation Management System
 */

/**
 * Rate limiting for login attempts
 */
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 900) {
    $db = Database::getInstance();
    
    // Clean old attempts
    $db->execute("DELETE FROM rate_limits WHERE created_at < DATE_SUB(NOW(), INTERVAL ? SECOND)", [$timeWindow]);
    
    // Count recent attempts
    $attempts = $db->fetch("SELECT COUNT(*) as count FROM rate_limits WHERE identifier = ? AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)", 
                          [$identifier, $timeWindow]);
    
    if ($attempts['count'] >= $maxAttempts) {
        return false;
    }
    
    return true;
}

/**
 * Record rate limit attempt
 */
function recordRateLimitAttempt($identifier, $action = 'login') {
    $db = Database::getInstance();
    
    $db->execute("INSERT INTO rate_limits (identifier, action, ip_address, user_agent) VALUES (?, ?, ?, ?)", [
        $identifier,
        $action,
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
}

/**
 * Validate file upload security
 */
function validateFileUpload($file, $allowedTypes = [], $maxSize = 5242880) {
    $validation = new ValidationResult();
    
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        $validation->addError('file', 'No file uploaded or invalid upload');
        return $validation;
    }
    
    // Check file size
    if ($file['size'] > $maxSize) {
        $validation->addError('file', 'File size exceeds maximum allowed size (' . formatBytes($maxSize) . ')');
        return $validation;
    }
    
    // Check file type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!empty($allowedTypes) && !in_array($mimeType, $allowedTypes)) {
        $validation->addError('file', 'File type not allowed. Allowed types: ' . implode(', ', $allowedTypes));
        return $validation;
    }
    
    // Check for malicious content
    if (containsMaliciousContent($file['tmp_name'])) {
        $validation->addError('file', 'File contains potentially malicious content');
        return $validation;
    }
    
    $validation->isValid = true;
    return $validation;
}

/**
 * Check for malicious content in uploaded files
 */
function containsMaliciousContent($filePath) {
    $content = file_get_contents($filePath, false, null, 0, 1024); // Read first 1KB
    
    // Check for PHP tags
    if (strpos($content, '<?php') !== false || strpos($content, '<?=') !== false) {
        return true;
    }
    
    // Check for script tags
    if (stripos($content, '<script') !== false) {
        return true;
    }
    
    // Check for executable signatures
    $signatures = [
        "\x4D\x5A", // PE executable
        "\x7F\x45\x4C\x46", // ELF executable
        "\xCA\xFE\xBA\xBE", // Java class file
    ];
    
    foreach ($signatures as $signature) {
        if (strpos($content, $signature) === 0) {
            return true;
        }
    }
    
    return false;
}

/**
 * Sanitize filename for safe storage
 */
function sanitizeFilename($filename) {
    // Remove path information
    $filename = basename($filename);
    
    // Remove special characters
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    
    // Limit length
    if (strlen($filename) > 100) {
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $name = substr(pathinfo($filename, PATHINFO_FILENAME), 0, 96 - strlen($extension));
        $filename = $name . '.' . $extension;
    }
    
    return $filename;
}

/**
 * Generate secure random filename
 */
function generateSecureFilename($originalFilename) {
    $extension = pathinfo($originalFilename, PATHINFO_EXTENSION);
    $randomName = bin2hex(random_bytes(16));
    return $randomName . '.' . $extension;
}

/**
 * Validate and sanitize user input
 */
function validateInput($input, $type, $options = []) {
    $validation = new ValidationResult();
    
    switch ($type) {
        case 'email':
            if (!filter_var($input, FILTER_VALIDATE_EMAIL)) {
                $validation->addError('email', 'Invalid email format');
            }
            break;
            
        case 'phone':
            if (!preg_match('/^[\+]?[0-9\s\-\(\)]{10,20}$/', $input)) {
                $validation->addError('phone', 'Invalid phone number format');
            }
            break;
            
        case 'date':
            if (!validateDate($input)) {
                $validation->addError('date', 'Invalid date format');
            }
            break;
            
        case 'number':
            if (!is_numeric($input)) {
                $validation->addError('number', 'Must be a valid number');
            }
            if (isset($options['min']) && $input < $options['min']) {
                $validation->addError('number', 'Value must be at least ' . $options['min']);
            }
            if (isset($options['max']) && $input > $options['max']) {
                $validation->addError('number', 'Value must be at most ' . $options['max']);
            }
            break;
            
        case 'string':
            if (isset($options['min_length']) && strlen($input) < $options['min_length']) {
                $validation->addError('string', 'Must be at least ' . $options['min_length'] . ' characters');
            }
            if (isset($options['max_length']) && strlen($input) > $options['max_length']) {
                $validation->addError('string', 'Must be at most ' . $options['max_length'] . ' characters');
            }
            break;
    }
    
    if (empty($validation->errors)) {
        $validation->isValid = true;
    }
    
    return $validation;
}

/**
 * Check for SQL injection patterns
 */
function detectSQLInjection($input) {
    $patterns = [
        '/(\bUNION\b.*\bSELECT\b)/i',
        '/(\bSELECT\b.*\bFROM\b)/i',
        '/(\bINSERT\b.*\bINTO\b)/i',
        '/(\bUPDATE\b.*\bSET\b)/i',
        '/(\bDELETE\b.*\bFROM\b)/i',
        '/(\bDROP\b.*\bTABLE\b)/i',
        '/(\bCREATE\b.*\bTABLE\b)/i',
        '/(\bALTER\b.*\bTABLE\b)/i',
        '/(\'|\"|;|--|\#|\*|\/\*|\*\/)/i'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $input)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Check for XSS patterns
 */
function detectXSS($input) {
    $patterns = [
        '/<script[^>]*>.*?<\/script>/is',
        '/<iframe[^>]*>.*?<\/iframe>/is',
        '/<object[^>]*>.*?<\/object>/is',
        '/<embed[^>]*>/i',
        '/<link[^>]*>/i',
        '/<meta[^>]*>/i',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload=/i',
        '/onclick=/i',
        '/onerror=/i',
        '/onmouseover=/i'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $input)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Comprehensive input sanitization
 */
function sanitizeInputAdvanced($input, $allowHTML = false) {
    // Remove null bytes
    $input = str_replace("\0", '', $input);
    
    // Check for malicious patterns
    if (detectSQLInjection($input) || detectXSS($input)) {
        logEvent('SECURITY', 'Malicious input detected', [
            'input' => substr($input, 0, 100),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        return '';
    }
    
    if (!$allowHTML) {
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    } else {
        // Allow only safe HTML tags
        $allowedTags = '<p><br><strong><em><u><ul><ol><li>';
        $input = strip_tags($input, $allowedTags);
    }
    
    return trim($input);
}

/**
 * Generate Content Security Policy header
 */
function setSecurityHeaders() {
    // Content Security Policy
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self'");
    
    // X-Frame-Options
    header('X-Frame-Options: DENY');
    
    // X-Content-Type-Options
    header('X-Content-Type-Options: nosniff');
    
    // X-XSS-Protection
    header('X-XSS-Protection: 1; mode=block');
    
    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Strict Transport Security (if using HTTPS)
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

/**
 * Check if request is from allowed origin
 */
function validateOrigin() {
    $allowedOrigins = [
        'http://localhost',
        'https://localhost',
        BASE_URL
    ];
    
    $origin = $_SERVER['HTTP_ORIGIN'] ?? $_SERVER['HTTP_REFERER'] ?? '';
    
    foreach ($allowedOrigins as $allowed) {
        if (strpos($origin, $allowed) === 0) {
            return true;
        }
    }
    
    return false;
}

/**
 * Validate session security
 */
function validateSessionSecurity() {
    // Check session timeout
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > SESSION_TIMEOUT) {
        session_destroy();
        return false;
    }
    
    // Update last activity
    $_SESSION['last_activity'] = time();
    
    // Check IP address consistency (optional, can cause issues with mobile users)
    if (ENABLE_IP_CHECK && isset($_SESSION['ip_address'])) {
        if ($_SESSION['ip_address'] !== $_SERVER['REMOTE_ADDR']) {
            session_destroy();
            return false;
        }
    }
    
    // Check user agent consistency
    if (isset($_SESSION['user_agent'])) {
        if ($_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
            session_destroy();
            return false;
        }
    }
    
    return true;
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details = []) {
    $logData = [
        'event' => $event,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        'user_id' => $_SESSION['user_id'] ?? null,
        'details' => $details
    ];
    
    logEvent('SECURITY', $event, $logData);
}

/**
 * Format bytes for display
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

?>
