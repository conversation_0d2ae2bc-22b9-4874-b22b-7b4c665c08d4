<?php
/**
 * Create Missing Tables for Admin Dashboard
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Creating Missing Tables</h2>";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=blood_donation_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Connected to database</p>";
    
    // Create blood_requests table
    $sql = "CREATE TABLE IF NOT EXISTS blood_requests (
        id INT PRIMARY KEY AUTO_INCREMENT,
        recipient_id INT NOT NULL,
        blood_type_id INT NOT NULL,
        units_needed INT NOT NULL,
        urgency_level ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        hospital_name VARCHAR(100) NOT NULL,
        hospital_address TEXT NOT NULL,
        hospital_contact VARCHAR(20),
        request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        required_by_date DATE NOT NULL,
        status ENUM('pending', 'approved', 'fulfilled', 'cancelled') DEFAULT 'pending',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (blood_type_id) REFERENCES blood_types(id),
        INDEX idx_recipient (recipient_id),
        INDEX idx_blood_type (blood_type_id),
        INDEX idx_status (status),
        INDEX idx_urgency (urgency_level)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Blood requests table created</p>";
    
    // Create donations table
    $sql = "CREATE TABLE IF NOT EXISTS donations (
        id INT PRIMARY KEY AUTO_INCREMENT,
        donor_id INT NOT NULL,
        blood_type_id INT NOT NULL,
        donation_date DATE NOT NULL,
        units_donated INT NOT NULL,
        status ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled',
        donation_center VARCHAR(100),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (donor_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (blood_type_id) REFERENCES blood_types(id),
        INDEX idx_donor (donor_id),
        INDEX idx_blood_type (blood_type_id),
        INDEX idx_status (status),
        INDEX idx_donation_date (donation_date)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Donations table created</p>";
    
    // Create notifications table
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_is_read (is_read),
        INDEX idx_created_at (created_at)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Notifications table created</p>";
    
    // Create system_logs table
    $sql = "CREATE TABLE IF NOT EXISTS system_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NULL,
        action VARCHAR(100) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ System logs table created</p>";
    
    // Create password_resets table
    $sql = "CREATE TABLE IF NOT EXISTS password_resets (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        token VARCHAR(255) NOT NULL UNIQUE,
        expires_at TIMESTAMP NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_token (token),
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Password resets table created</p>";
    
    // Create rate_limits table
    $sql = "CREATE TABLE IF NOT EXISTS rate_limits (
        id INT PRIMARY KEY AUTO_INCREMENT,
        identifier VARCHAR(255) NOT NULL,
        action VARCHAR(50) NOT NULL,
        attempts INT DEFAULT 1,
        first_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_identifier_action (identifier, action),
        INDEX idx_identifier (identifier),
        INDEX idx_action (action),
        INDEX idx_last_attempt (last_attempt)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Rate limits table created</p>";
    
    echo "<h3>✅ All missing tables created successfully!</h3>";
    echo "<p><a href='admin_debug.php'>Go back to debug page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?> 