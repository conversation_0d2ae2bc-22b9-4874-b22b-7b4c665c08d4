<?php
/**
 * User Class
 * Blood Donation Management System
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../includes/functions.php';

class User {
    protected $db;
    protected $id;
    protected $username;
    protected $email;
    protected $userType;
    protected $firstName;
    protected $lastName;
    protected $phone;
    protected $address;
    protected $profilePhoto;
    protected $status;
    protected $emailVerified;
    protected $lastLogin;
    protected $isArchived;
    protected $archivedAt;
    protected $archivedBy;
    protected $createdAt;
    protected $updatedAt;
    
    public function __construct($id = null) {
        $this->db = Database::getInstance();
        
        if ($id) {
            $this->loadUser($id);
        }
    }
    
    /**
     * Load user data from database
     */
    protected function loadUser($id) {
        $sql = "SELECT * FROM users WHERE id = ?";
        $userData = $this->db->fetch($sql, [$id]);
        
        if ($userData) {
            $this->id = $userData['id'];
            $this->username = $userData['username'];
            $this->email = null; // Email functionality removed
            $this->userType = $userData['user_type'];
            $this->firstName = $userData['first_name'];
            $this->lastName = $userData['last_name'];
            $this->phone = $userData['phone'];
            $this->address = $userData['address'];
            $this->profilePhoto = $userData['profile_photo'];
            $this->status = $userData['status'];
            $this->emailVerified = false; // Email verification removed
            $this->lastLogin = $userData['last_login'];
            $this->createdAt = $userData['created_at'];
            $this->updatedAt = $userData['updated_at'];
        }
    }
    
    /**
     * Create new user
     */
    public static function create($userData) {
        $db = Database::getInstance();
        try {
            // If a transaction is already in progress (e.g. Donor::create), don't start a new one
            $pdo = $db->getConnection();
            $startedTransaction = false;
            if (!$pdo->inTransaction()) {
                $db->beginTransaction();
                $startedTransaction = true;
            }
            
            // Check if username exists
            $checkSql = "SELECT id FROM users WHERE username = ?";
            $existing = $db->fetch($checkSql, [$userData['username']]);

            if ($existing) {
                throw new Exception('Username already exists');
            }
            
            // Store password in plaintext (WARNING: insecure)
            $passwordHash = $userData['password'];
            
            // Insert user (using `password` column)
            $sql = "INSERT INTO users (username, password, user_type, first_name, last_name, phone, address, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $db->execute($sql, [
                $userData['username'],
                $passwordHash,
                $userData['user_type'],
                $userData['first_name'],
                $userData['last_name'],
                $userData['phone'] ?? '',
                $userData['address'] ?? '',
                $userData['status'] ?? USER_STATUS_ACTIVE
            ]);
            
            $userId = $db->lastInsertId();

            if ($startedTransaction) {
                $db->commit();
            }
            
            logEvent('INFO', 'User created', ['user_id' => $userId, 'username' => $userData['username']]);
            
            return new self($userId);
            
        } catch (Exception $e) {
            // Rollback only if we started the transaction here
            if (isset($startedTransaction) && $startedTransaction) {
                $db->rollback();
            }
            logEvent('ERROR', 'User creation failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Update user information
     */
    public function update($data) {
        $sql = "UPDATE users SET first_name = ?, last_name = ?, phone = ?, address = ?, updated_at = NOW() WHERE id = ?";
        
        $this->db->execute($sql, [
            $data['first_name'],
            $data['last_name'],
            $data['phone'] ?? '',
            $data['address'] ?? '',
            $this->id
        ]);
        
        // Update object properties
        $this->firstName = $data['first_name'];
        $this->lastName = $data['last_name'];
        $this->phone = $data['phone'] ?? '';
        $this->address = $data['address'] ?? '';
        
        logEvent('INFO', 'User updated', ['user_id' => $this->id]);
        
        return true;
    }
    
    /**
     * Change password
     */
    public function changePassword($currentPassword, $newPassword) {
        // Get current password hash
        $sql = "SELECT password AS password_hash FROM users WHERE id = ?";
        $user = $this->db->fetch($sql, [$this->id]);
        
        if ($currentPassword !== $user['password_hash']) {
            throw new Exception('Current password is incorrect');
        }

        // Update password
        $updateSql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
        $this->db->execute($updateSql, [$newPassword, $this->id]);
        
        logEvent('INFO', 'Password changed', ['user_id' => $this->id]);
        
        return true;
    }
    
    /**
     * Upload profile photo
     */
    public function uploadProfilePhoto($file) {
        $fileName = uploadFile($file, ALLOWED_IMAGE_TYPES);
        
        // Delete old photo if exists
        if ($this->profilePhoto) {
            deleteFile($this->profilePhoto);
        }
        
        // Update database
        $sql = "UPDATE users SET profile_photo = ?, updated_at = NOW() WHERE id = ?";
        $this->db->execute($sql, [$fileName, $this->id]);
        
        $this->profilePhoto = $fileName;
        
        return $fileName;
    }
    
    /**
     * Get user notifications
     */
    public function getNotifications($limit = 10, $offset = 0) {
        $sql = "SELECT n.*, un.is_read, un.read_at 
                FROM notifications n 
                JOIN user_notifications un ON n.id = un.notification_id 
                WHERE un.user_id = ? AND n.is_active = 1 
                ORDER BY n.created_at DESC 
                LIMIT ? OFFSET ?";
        
        return $this->db->fetchAll($sql, [$this->id, $limit, $offset]);
    }
    
    /**
     * Mark notification as read
     */
    public function markNotificationRead($notificationId) {
        $sql = "UPDATE user_notifications SET is_read = 1, read_at = NOW() 
                WHERE user_id = ? AND notification_id = ?";
        
        return $this->db->execute($sql, [$this->id, $notificationId]);
    }
    
    /**
     * Get unread notifications count
     */
    public function getUnreadNotificationsCount() {
        $sql = "SELECT COUNT(*) as count 
                FROM user_notifications un 
                JOIN notifications n ON un.notification_id = n.id 
                WHERE un.user_id = ? AND un.is_read = 0 AND n.is_active = 1";
        
        $result = $this->db->fetch($sql, [$this->id]);
        return $result['count'];
    }
    
    /**
     * Get chat messages
     */
    public function getChatMessages($otherUserId, $limit = 50) {
        $sql = "SELECT cm.*, 
                       sender.first_name as sender_first_name, sender.last_name as sender_last_name,
                       receiver.first_name as receiver_first_name, receiver.last_name as receiver_last_name
                FROM chat_messages cm
                JOIN users sender ON cm.sender_id = sender.id
                JOIN users receiver ON cm.receiver_id = receiver.id
                WHERE (cm.sender_id = ? AND cm.receiver_id = ?) 
                   OR (cm.sender_id = ? AND cm.receiver_id = ?)
                ORDER BY cm.sent_at ASC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$this->id, $otherUserId, $otherUserId, $this->id, $limit]);
    }
    
    /**
     * Send chat message
     */
    public function sendMessage($receiverId, $message, $attachment = null) {
        $sql = "INSERT INTO chat_messages (sender_id, receiver_id, message, attachment, sent_at) 
                VALUES (?, ?, ?, ?, NOW())";
        
        $this->db->execute($sql, [$this->id, $receiverId, $message, $attachment]);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Mark messages as read
     */
    public function markMessagesRead($senderId) {
        $sql = "UPDATE chat_messages SET is_read = 1, read_at = NOW() 
                WHERE sender_id = ? AND receiver_id = ? AND is_read = 0";
        
        return $this->db->execute($sql, [$senderId, $this->id]);
    }
    
    /**
     * Get user statistics
     */
    public function getStatistics() {
        $stats = [];
        
        if ($this->userType === USER_TYPE_DONOR) {
            // Get donation statistics
            $sql = "SELECT COUNT(*) as total_donations,
                           SUM(units_donated) as total_units,
                           MAX(donation_date) as last_donation
                    FROM donations 
                    WHERE donor_id = ? AND status = 'completed'";
            
            $donationStats = $this->db->fetch($sql, [$this->id]);
            $stats['donations'] = $donationStats;
            
        } elseif ($this->userType === USER_TYPE_RECIPIENT) {
            // Get request statistics
            $sql = "SELECT COUNT(*) as total_requests,
                           SUM(units_needed) as total_units_requested,
                           COUNT(CASE WHEN status = 'fulfilled' THEN 1 END) as fulfilled_requests
                    FROM blood_requests 
                    WHERE recipient_id = ?";
            
            $requestStats = $this->db->fetch($sql, [$this->id]);
            $stats['requests'] = $requestStats;
        }
        
        return $stats;
    }
    
    /**
     * Suspend user account
     */
    public function suspend() {
        $sql = "UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?";
        $this->db->execute($sql, [USER_STATUS_SUSPENDED, $this->id]);
        
        $this->status = USER_STATUS_SUSPENDED;
        
        logEvent('INFO', 'User suspended', ['user_id' => $this->id]);
        
        return true;
    }
    
    /**
     * Activate user account
     */
    public function activate() {
        $sql = "UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?";
        $this->db->execute($sql, [USER_STATUS_ACTIVE, $this->id]);
        
        $this->status = USER_STATUS_ACTIVE;
        
        logEvent('INFO', 'User activated', ['user_id' => $this->id]);
        
        return true;
    }
    
    /**
     * Archive user account instead of deleting
     */
    public function archive($adminId, $reason = '') {
        $sql = "UPDATE users SET
                is_archived = TRUE,
                archived_at = NOW(),
                archived_by = ?,
                status = ?,
                updated_at = NOW()
                WHERE id = ?";

        $this->db->execute($sql, [
            $adminId,
            USER_STATUS_SUSPENDED,
            $this->id
        ]);

        $this->isArchived = true;
        $this->archivedAt = date(DATETIME_FORMAT);
        $this->archivedBy = $adminId;
        $this->status = USER_STATUS_SUSPENDED;

        logEvent('INFO', 'User archived', [
            'user_id' => $this->id,
            'admin_id' => $adminId,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * Restore archived user account
     */
    public function restore($adminId, $reason = '') {
        $sql = "UPDATE users SET
                is_archived = FALSE,
                archived_at = NULL,
                archived_by = NULL,
                status = ?,
                updated_at = NOW()
                WHERE id = ?";

        $this->db->execute($sql, [
            USER_STATUS_ACTIVE,
            $this->id
        ]);

        $this->isArchived = false;
        $this->archivedAt = null;
        $this->archivedBy = null;
        $this->status = USER_STATUS_ACTIVE;

        logEvent('INFO', 'User restored', [
            'user_id' => $this->id,
            'admin_id' => $adminId,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * Delete user account (deprecated - use archive instead)
     * @deprecated Use archive() method instead
     */
    public function delete() {
        // For backward compatibility, archive instead of delete
        return $this->archive(getCurrentUser()['id'] ?? 1, 'Legacy delete operation converted to archive');
    }
    
    // Getters
    public function getId() { return $this->id; }
    public function getUsername() { return $this->username; }
    public function getEmail() { return $this->email; }
    public function getUserType() { return $this->userType; }
    public function getFirstName() { return $this->firstName; }
    public function getLastName() { return $this->lastName; }
    public function getFullName() { return $this->firstName . ' ' . $this->lastName; }
    public function getPhone() { return $this->phone; }
    public function getAddress() { return $this->address; }
    public function getProfilePhoto() { return $this->profilePhoto; }
    public function getStatus() { return $this->status; }
    public function isEmailVerified() { return $this->emailVerified; }
    public function getLastLogin() { return $this->lastLogin; }
    public function getCreatedAt() { return $this->createdAt; }
    public function getUpdatedAt() { return $this->updatedAt; }
    
    /**
     * Check if user is active
     */
    public function isActive() {
        return $this->status === USER_STATUS_ACTIVE;
    }
    
    /**
     * Check if user has permission
     */
    public function hasPermission($requiredType) {
        if ($this->userType === USER_TYPE_ADMIN) {
            return true; // Admin has all permissions
        }
        
        return $this->userType === $requiredType;
    }
    
    /**
     * Get all users with pagination
     */
    public static function getAll($page = 1, $limit = RECORDS_PER_PAGE, $filters = []) {
        $db = Database::getInstance();
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if (!empty($filters['user_type'])) {
            $whereClause .= " AND user_type = ?";
            $params[] = $filters['user_type'];
        }
        
        if (!empty($filters['status'])) {
            $whereClause .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['search'])) {
            $whereClause .= " AND (first_name LIKE ? OR last_name LIKE ? OR username LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM users $whereClause";
        $totalResult = $db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get users
        $sql = "SELECT * FROM users $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $users = $db->fetchAll($sql, $params);
        
        return [
            'users' => $users,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
}
?>
