<?php
/**
 * Setup schema_migrations table and run donor medical fields migration
 * Blood Donation Management System
 */

require_once '../config/database.php';

try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    echo "=== Setting up schema_migrations table ===\n";
    
    // Create schema_migrations table
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS schema_migrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration_name VARCHAR(255) NOT NULL UNIQUE,
            success BOOLEAN NOT NULL DEFAULT FALSE,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            error_message TEXT NULL
        )
    ";
    
    $connection->exec($createTableSQL);
    echo "✓ schema_migrations table created or already exists\n";
    
    // Check if our migration has already been recorded
    $stmt = $connection->prepare("SELECT * FROM schema_migrations WHERE migration_name = 'donor_medical_fields_migration'");
    $stmt->execute();
    $migrationRecord = $stmt->fetch();
    
    if ($migrationRecord) {
        if ($migrationRecord['success']) {
            echo "✓ donor_medical_fields_migration already recorded as successful\n";
            exit(0);
        } else {
            echo "✗ donor_medical_fields_migration previously failed\n";
        }
    } else {
        echo "→ donor_medical_fields_migration not yet recorded\n";
    }
    
    echo "\n=== Adding donor medical fields to donor_profiles table ===\n";
    
    // Add medical fields to donor_profiles table
    $alterTableSQL = "
        ALTER TABLE donor_profiles 
        ADD COLUMN IF NOT EXISTS medications TEXT NULL,
        ADD COLUMN IF NOT EXISTS allergies TEXT NULL,
        ADD COLUMN IF NOT EXISTS emergency_contact VARCHAR(100) NULL,
        ADD COLUMN IF NOT EXISTS emergency_phone VARCHAR(20) NULL,
        ADD COLUMN IF NOT EXISTS relationship VARCHAR(50) NULL
    ";
    
    $connection->exec($alterTableSQL);
    echo "✓ Medical fields added to donor_profiles table\n";
    
    // Record the migration as successful
    $insertSQL = "
        INSERT INTO schema_migrations (migration_name, success) 
        VALUES ('donor_medical_fields_migration', TRUE)
        ON DUPLICATE KEY UPDATE 
            executed_at = CURRENT_TIMESTAMP,
            success = TRUE,
            error_message = NULL
    ";
    
    $connection->exec($insertSQL);
    echo "✓ Migration recorded as successful in schema_migrations table\n";
    
    echo "\n=== Migration completed successfully! ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    
    // Try to record the error in schema_migrations table
    try {
        $insertErrorSQL = "
            INSERT INTO schema_migrations (migration_name, success, error_message) 
            VALUES ('donor_medical_fields_migration', FALSE, ?)
            ON DUPLICATE KEY UPDATE 
                executed_at = CURRENT_TIMESTAMP,
                success = FALSE,
                error_message = ?
        ";
        
        $stmt = $connection->prepare($insertErrorSQL);
        $stmt->execute([$e->getMessage(), $e->getMessage()]);
        echo "✗ Migration error recorded in schema_migrations table\n";
    } catch (Exception $logError) {
        echo "✗ Failed to record migration error: " . $logError->getMessage() . "\n";
    }
    
    exit(1);
}
?>
