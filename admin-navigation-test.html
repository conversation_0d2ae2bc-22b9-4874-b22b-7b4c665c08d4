<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Navigation Test - Blood Donation System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/responsive.css" rel="stylesheet">
    <style>
        .test-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }
        .viewport-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 0.5rem;
            font-size: 0.8rem;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <!-- Viewport Information -->
    <div class="viewport-info">
        <div>Width: <span id="width">0</span>px</div>
        <div>Height: <span id="height">0</span>px</div>
        <div>Device: <span id="device">Unknown</span></div>
    </div>

    <!-- Admin Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar" style="background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 2px 10px rgba(139,0,0,0.3);">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">Administrator</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Test Content -->
    <div class="container-fluid" style="margin-top: 80px;">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">Admin Navigation Test Suite</h1>
                <p class="text-center mb-5">Testing the admin mobile navigation functionality</p>
            </div>
        </div>

        <div class="test-info">
            <h3>Admin Navigation Test</h3>
            <p>This page tests the admin-specific mobile navigation features including the enhanced hamburger menu, admin styling, and responsive behavior.</p>
            
            <h5>Test Instructions:</h5>
            <ol>
                <li><strong>Mobile Testing:</strong> Resize browser to mobile width (320px-767px) and test hamburger menu</li>
                <li><strong>Tablet Testing:</strong> Resize to tablet width (768px-1023px) and verify horizontal navigation</li>
                <li><strong>Desktop Testing:</strong> Resize to desktop width (1024px+) and check full navigation</li>
                <li><strong>Admin Features:</strong> Test the admin profile dropdown and navigation items</li>
                <li><strong>Keyboard Testing:</strong> Use Tab, Enter, Space, and Escape keys for navigation</li>
            </ol>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/mobile-navigation.js"></script>
    <script>
        // Viewport information display
        function updateViewportInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            let device = 'Desktop';
            
            if (width < 768) {
                device = 'Mobile';
            } else if (width < 1024) {
                device = 'Tablet';
            }
            
            document.getElementById('width').textContent = width;
            document.getElementById('height').textContent = height;
            document.getElementById('device').textContent = device;
        }

        // Update viewport info on load and resize
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
    </script>
</body>
</html>
