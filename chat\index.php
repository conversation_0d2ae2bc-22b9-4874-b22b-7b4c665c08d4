<?php
/**
 * Chat System Main Interface
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';
require_once '../classes/UnifiedUser.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

// Get chat partner if specified
$partnerId = (int)($_GET['user_id'] ?? 0);
$chatPartner = null;

if ($partnerId > 0) {
    $chatPartner = new User($partnerId);
    
    // Verify user can chat with this partner
    if (!canChatWith($currentUser['id'], $partnerId)) {
        redirectWithMessage('index.php', 'You cannot chat with this user', 'error');
    }
}

// Get user's chat conversations
$conversations = getUserConversations($currentUser['id']);

// Add activity status to conversations
foreach ($conversations as &$conversation) {
    // Check if user is active based on the data we already have
    $isActive = false;
    if (isset($conversation['is_online']) && $conversation['is_online'] && isset($conversation['last_activity'])) {
        $lastActivity = new DateTime($conversation['last_activity']);
        $now = new DateTime();
        $diff = $now->diff($lastActivity);

        // If last activity was within 5 minutes, consider active
        if ($diff->i < 5 && $diff->h == 0 && $diff->d == 0) {
            $isActive = true;
        }
    }

    $conversation['is_active'] = $isActive;
    $conversation['status'] = $isActive ? 'Active' : 'Inactive';
}

// Get messages if chat partner is selected
$messages = [];
if ($chatPartner) {
    $messages = getChatMessages($currentUser['id'], $partnerId);
    
    // Mark messages as read
    markMessagesAsRead($currentUser['id'], $partnerId);
}

// Determine dashboard URL based on user type (Updated for unified system)
$dashboardUrl = '../';
switch ($currentUser['user_type']) {
    case USER_TYPE_ADMIN:
        $dashboardUrl = '../admin/';
        break;
    case 'unified':
        $dashboardUrl = '../dashboard/';
        break;
    case USER_TYPE_DONOR:
        // Legacy donor - redirect to unified dashboard
        $dashboardUrl = '../dashboard/';
        break;
    case USER_TYPE_RECIPIENT:
        // Legacy recipient - redirect to unified dashboard
        $dashboardUrl = '../dashboard/';
        break;
    default:
        $dashboardUrl = '../';
        break;
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        /* Status indicator dots */
        .status-indicator {
            position: relative;
            display: inline-block;
        }

        .status-indicator::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            border: 2px solid white;
            border-radius: 50%;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .status-indicator.active::after {
            background: #28a745;
            animation: pulse-green 2s infinite;
        }

        .status-indicator.inactive::after {
            background: #dc3545;
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-green {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes pulse-red {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        /* Conversation item styling */
        .conversation-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .conversation-item:hover {
            background-color: #f8f9fa;
        }

        .conversation-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #DC143C;
        }

        .conversation-item.unread {
            background-color: #fff3cd;
        }

        /* Status badges */
        .status-badge {
            font-size: 0.65rem !important;
            padding: 0.2em 0.4em !important;
        }
    </style>
    <style>
        .chat-sidebar {
            height: calc(100vh - 56px);
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        
        .chat-main {
            height: calc(100vh - 56px);
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            background-color: #f8f9fa;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        
        .chat-input {
            padding: 1rem;
            border-top: 1px solid #dee2e6;
            background-color: white;
        }
        
        .message {
            margin-bottom: 1rem;
            max-width: 70%;
        }
        
        .message.sent {
            margin-left: auto;
        }
        
        .message.received {
            margin-right: auto;
        }
        
        .message-bubble {
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            word-wrap: break-word;
        }
        
        .message.sent .message-bubble {
            background-color: #007bff;
            color: white;
            border-bottom-right-radius: 0.25rem;
        }
        
        .message.received .message-bubble {
            background-color: white;
            border: 1px solid #dee2e6;
            border-bottom-left-radius: 0.25rem;
        }
        
        .message-time {
            font-size: 0.75rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        
        .message.sent .message-time {
            text-align: right;
        }
        
        .conversation-item {
            padding: 0.75rem;
            border-bottom: 1px solid #dee2e6;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .conversation-item:hover {
            background-color: #f8f9fa;
        }
        
        .conversation-item.active {
            background-color: #e3f2fd;
            border-left: 3px solid #007bff;
        }
        
        .conversation-item.unread {
            background-color: #fff3cd;
        }
        


    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?php echo $dashboardUrl; ?>">
                <i class="fas fa-tint"></i> <?php echo APP_NAME; ?>
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="<?php echo $dashboardUrl; ?>">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
            
            <div class="navbar-nav">
                <span class="navbar-text">
                    <i class="fas fa-comments"></i> Chat System
                </span>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo $currentUser['first_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid p-0">
        <div class="row g-0">
            <!-- Chat Sidebar -->
            <div class="col-md-4 col-lg-3 chat-sidebar">
                <div class="p-3 border-bottom">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="mb-0"><i class="fas fa-comments"></i> Conversations</h5>
                        <a href="find-users-restricted.php" class="btn btn-danger btn-sm" title="Find people to chat with">
                            <i class="fas fa-user-plus"></i>
                        </a>
                    </div>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control" id="searchConversations" placeholder="Search conversations...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <?php if ($currentUser['user_type'] !== USER_TYPE_ADMIN): ?>
                <!-- Messaging Restrictions Notice for Regular Users -->
                <div class="p-3 border-bottom bg-light">
                    <div class="d-flex align-items-start">
                        <i class="fas fa-info-circle text-primary me-2 mt-1"></i>
                        <div>
                            <small class="text-muted">
                                <strong>Messaging Policy:</strong> You can only send messages to system administrators.
                                For privacy and security, direct messaging between donors and recipients is not permitted.
                            </small>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <div id="conversationsList">
                    <?php if (empty($conversations)): ?>
                        <div class="p-3 text-center text-muted">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <p>No conversations yet</p>
                            <small>
                                <?php if ($currentUser['user_type'] === USER_TYPE_ADMIN): ?>
                                    Start chatting with any user in the system
                                <?php else: ?>
                                    Contact administrators for support and assistance
                                <?php endif; ?>
                            </small>
                            <div class="mt-3">
                                <a href="find-users-restricted.php" class="btn btn-danger btn-sm">
                                    <i class="fas fa-search"></i>
                                    <?php echo $currentUser['user_type'] === USER_TYPE_ADMIN ? 'Find Users' : 'Contact Admins'; ?>
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($conversations as $conversation): ?>
                            <div class="conversation-item <?php echo $conversation['unread_count'] > 0 ? 'unread' : ''; ?> <?php echo $partnerId == $conversation['user_id'] ? 'active' : ''; ?>"
                                 data-user-id="<?php echo $conversation['user_id']; ?>"
                                 onclick="openConversation(<?php echo $conversation['user_id']; ?>)">
                                <div class="d-flex align-items-center">
                                    <?php if ($conversation['profile_photo']): ?>
                                        <div class="status-indicator <?php echo $conversation['is_active'] ? 'active' : 'inactive'; ?>">
                                            <img src="../uploads/<?php echo htmlspecialchars($conversation['profile_photo']); ?>"
                                                 class="rounded-circle me-2" width="40" height="40">
                                        </div>
                                    <?php else: ?>
                                        <div class="status-indicator <?php echo $conversation['is_active'] ? 'active' : 'inactive'; ?>">
                                            <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center"
                                                 style="width: 40px; height: 40px;">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong><?php echo htmlspecialchars($conversation['first_name'] . ' ' . $conversation['last_name']); ?></strong>
                                                <?php if ($conversation['is_active']): ?>
                                                    <span class="badge bg-success ms-1 status-badge" style="font-size: 0.6rem;">
                                                        Active
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary ms-1 status-badge" style="font-size: 0.6rem;">
                                                        Inactive
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted"><?php echo timeAgo($conversation['last_message_time']); ?></small>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars(substr($conversation['last_message'], 0, 30)); ?>
                                                <?php if (strlen($conversation['last_message']) > 30): ?>...<?php endif; ?>
                                            </small>
                                            <?php if ($conversation['unread_count'] > 0): ?>
                                                <span class="badge bg-primary"><?php echo $conversation['unread_count']; ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <?php
                                            // Display user roles for unified users
                                            if ($conversation['is_unified_user'] && !empty($conversation['roles'])) {
                                                $roles = explode(',', $conversation['roles']);
                                                foreach ($roles as $role) {
                                                    $badgeClass = $role === 'donor' ? 'success' : 'info';
                                                    echo '<span class="badge bg-' . $badgeClass . ' me-1">' . ucfirst($role) . '</span>';
                                                }
                                            } else {
                                                // Legacy user types
                                                $badgeClass = $conversation['user_type'] === 'donor' ? 'success' : 'info';
                                                echo '<span class="badge bg-' . $badgeClass . '">' . ucfirst($conversation['user_type']) . '</span>';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Chat Main Area -->
            <div class="col-md-8 col-lg-9 chat-main">
                <?php if ($chatPartner): ?>
                    <!-- Chat Header -->
                    <div class="chat-header">
                        <div class="d-flex align-items-center">
                            <?php if ($chatPartner->getProfilePhoto()): ?>
                                <img src="../uploads/<?php echo htmlspecialchars($chatPartner->getProfilePhoto()); ?>" 
                                     class="rounded-circle me-3" width="50" height="50">
                            <?php else: ?>
                                <div class="bg-secondary rounded-circle me-3 d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div>
                                <h5 class="mb-0"><?php echo htmlspecialchars($chatPartner->getFullName()); ?></h5>
                                <small class="text-muted">
                                    <?php
                                    // Check if this is a unified user
                                    if ($chatPartner->getUserType() === 'unified') {
                                        // Load unified user to get roles
                                        $unifiedPartner = new UnifiedUser($chatPartner->getId());
                                        $partnerRoles = $unifiedPartner->getActiveRoles();
                                        foreach ($partnerRoles as $role) {
                                            $badgeClass = $role === 'donor' ? 'success' : 'info';
                                            echo '<span class="badge bg-' . $badgeClass . ' me-1">' . ucfirst($role) . '</span>';
                                        }
                                    } else {
                                        // Legacy user types
                                        $badgeClass = $chatPartner->getUserType() === 'donor' ? 'success' : 'info';
                                        echo '<span class="badge bg-' . $badgeClass . '">' . ucfirst($chatPartner->getUserType()) . '</span>';
                                    }
                                    ?>
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div class="chat-messages" id="chatMessages">
                        <?php if (empty($messages)): ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-comments fa-3x mb-3"></i>
                                <p>No messages yet. Start the conversation!</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($messages as $message): ?>
                                <div class="message <?php echo $message['sender_id'] == $currentUser['id'] ? 'sent' : 'received'; ?>">
                                    <div class="message-bubble">
                                        <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                    </div>
                                    <div class="message-time">
                                        <?php echo formatDateTime($message['sent_at']); ?>
                                        <?php if ($message['sender_id'] == $currentUser['id']): ?>
                                            <?php if ($message['is_read']): ?>
                                                <i class="fas fa-check-double text-primary" title="Read"></i>
                                            <?php else: ?>
                                                <i class="fas fa-check" title="Sent"></i>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Chat Input -->
                    <div class="chat-input">
                        <form id="chatForm" onsubmit="sendMessage(event)">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                            <input type="hidden" id="receiverId" value="<?php echo $partnerId; ?>">
                            
                            <div class="input-group">
                                <input type="text" class="form-control" id="messageInput" 
                                       placeholder="Type your message..." required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-paper-plane"></i> Send
                                </button>
                            </div>
                        </form>
                    </div>
                <?php else: ?>
                    <!-- No Chat Selected -->
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center text-muted">
                            <i class="fas fa-comments fa-4x mb-3"></i>
                            <h4>Select a conversation to start chatting</h4>
                            <p>Choose a conversation from the sidebar to begin messaging</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        let currentReceiverId = <?php echo $partnerId; ?>;
        let messageRefreshInterval;

        function openConversation(userId) {
            window.location.href = 'index.php?user_id=' + userId;
        }

        function sendMessage(event) {
            event.preventDefault();
            
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message || !currentReceiverId) {
                return;
            }
            
            fetch('api/send-message.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    receiver_id: currentReceiverId,
                    message: message,
                    csrf_token: '<?php echo $csrfToken; ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageInput.value = '';
                    loadMessages();
                } else {
                    alert('Error sending message: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error sending message');
            });
        }

        function loadMessages() {
            if (!currentReceiverId) return;
            
            fetch(`api/get-messages.php?user_id=${currentReceiverId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateChatMessages(data.messages);
                    }
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                });
        }

        function updateChatMessages(messages) {
            const chatMessages = document.getElementById('chatMessages');
            const wasAtBottom = chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10;
            
            chatMessages.innerHTML = '';
            
            if (messages.length === 0) {
                chatMessages.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <p>No messages yet. Start the conversation!</p>
                    </div>
                `;
            } else {
                messages.forEach(function(message) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `message ${message.sender_id == <?php echo $currentUser['id']; ?> ? 'sent' : 'received'}`;
                    
                    const readStatus = message.sender_id == <?php echo $currentUser['id']; ?> ? 
                        (message.is_read ? '<i class="fas fa-check-double text-primary" title="Read"></i>' : '<i class="fas fa-check" title="Sent"></i>') : '';
                    
                    messageDiv.innerHTML = `
                        <div class="message-bubble">
                            ${message.message.replace(/\n/g, '<br>')}
                        </div>
                        <div class="message-time">
                            ${formatDateTime(message.sent_at)}
                            ${readStatus}
                        </div>
                    `;
                    
                    chatMessages.appendChild(messageDiv);
                });
            }
            
            // Scroll to bottom if user was at bottom
            if (wasAtBottom) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }

        function formatDateTime(dateTimeString) {
            const date = new Date(dateTimeString);
            return date.toLocaleString();
        }

        // Initialize chat
        if (currentReceiverId > 0) {
            // Scroll to bottom on load
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // Set up periodic message refresh
            messageRefreshInterval = setInterval(loadMessages, 3000);
        }

        // Search conversations
        document.getElementById('searchConversations').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const conversations = document.querySelectorAll('.conversation-item');
            
            conversations.forEach(function(conversation) {
                const name = conversation.querySelector('strong').textContent.toLowerCase();
                if (name.includes(searchTerm)) {
                    conversation.style.display = 'block';
                } else {
                    conversation.style.display = 'none';
                }
            });
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (messageRefreshInterval) {
                clearInterval(messageRefreshInterval);
            }
        });

        // Initialize online status monitoring for chat
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof OnlineStatusManager !== 'undefined') {
                const statusManager = new OnlineStatusManager();
                statusManager.start();

                // Get all user IDs from conversations
                const userIds = [];
                document.querySelectorAll('[data-user-id]').forEach(element => {
                    const userId = element.getAttribute('data-user-id');
                    if (userId && !userIds.includes(userId)) {
                        userIds.push(userId);
                    }
                });

                // Monitor these users
                if (userIds.length > 0) {
                    statusManager.monitorUsers(userIds);
                }
            }
        });
    </script>
    <script src="../assets/js/online-status.js"></script>
</body>
</html>
