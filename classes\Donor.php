<?php
/**
 * Donor Class
 * Blood Donation Management System
 */

require_once __DIR__ . '/User.php';
require_once __DIR__ . '/../config/constants.php';

class Donor extends User {
    private $bloodTypeId;
    private $bloodType;
    private $weight;
    private $birthDate;
    private $lastDonationDate;
    private $medicalConditions;
    private $eligibilityStatus;
    private $totalDonations;
    
    public function __construct($id = null) {
        parent::__construct($id);
        
        if ($id) {
            $this->loadDonorProfile();
        }
    }
    
    /**
     * Load donor profile data
     */
    private function loadDonorProfile() {
        $sql = "SELECT dp.*, bt.type as blood_type 
                FROM donor_profiles dp 
                JOIN blood_types bt ON dp.blood_type_id = bt.id 
                WHERE dp.user_id = ?";
        
        $profile = $this->db->fetch($sql, [$this->id]);
        
        if ($profile) {
            $this->bloodTypeId = $profile['blood_type_id'];
            $this->bloodType = $profile['blood_type'];
            $this->weight = $profile['weight'];
            $this->birthDate = $profile['birth_date'];
            $this->lastDonationDate = $profile['last_donation_date'];
            $this->medicalConditions = $profile['medical_conditions'];
            $this->eligibilityStatus = $profile['eligibility_status'];
            $this->totalDonations = $profile['total_donations'];
        }
    }
    
    /**
     * Create new donor
     */
    public static function create($userData) {
        $db = Database::getInstance();
        
        try {
            $db->beginTransaction();
            
            // Create user first
            $user = parent::create($userData);
            
            // Create donor profile
            $sql = "INSERT INTO donor_profiles (user_id, blood_type_id, weight, birth_date, medical_conditions, eligibility_status) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $db->execute($sql, [
                $user->getId(),
                $userData['blood_type_id'],
                $userData['weight'] ?? 0,
                $userData['birth_date'] ?? null,
                $userData['medical_conditions'] ?? '',
                'eligible'
            ]);
            
            $db->commit();
            
            logEvent('INFO', 'Donor profile created', ['user_id' => $user->getId()]);
            
            return new self($user->getId());
            
        } catch (Exception $e) {
            $db->rollback();
            logEvent('ERROR', 'Donor creation failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Update donor profile
     */
    public function updateProfile($data) {
        try {
            $this->db->beginTransaction();
            
            // Update user data
            parent::update($data);
            
            // Update donor profile
            $sql = "UPDATE donor_profiles SET 
                    weight = ?, 
                    medical_conditions = ? 
                    WHERE user_id = ?";
            
            $this->db->execute($sql, [
                $data['weight'] ?? $this->weight,
                $data['medical_conditions'] ?? $this->medicalConditions,
                $this->id
            ]);
            
            // Update blood type if provided
            if (!empty($data['blood_type_id'])) {
                $sql = "UPDATE donor_profiles SET blood_type_id = ? WHERE user_id = ?";
                $this->db->execute($sql, [$data['blood_type_id'], $this->id]);
                $this->bloodTypeId = $data['blood_type_id'];
                
                // Get blood type name
                $sql = "SELECT type FROM blood_types WHERE id = ?";
                $result = $this->db->fetch($sql, [$data['blood_type_id']]);
                $this->bloodType = $result['type'];
            }
            
            // Update birth date if provided
            if (!empty($data['birth_date'])) {
                $sql = "UPDATE donor_profiles SET birth_date = ? WHERE user_id = ?";
                $this->db->execute($sql, [$data['birth_date'], $this->id]);
                $this->birthDate = $data['birth_date'];
            }
            
            $this->db->commit();
            
            // Update object properties
            $this->weight = $data['weight'] ?? $this->weight;
            $this->medicalConditions = $data['medical_conditions'] ?? $this->medicalConditions;
            
            logEvent('INFO', 'Donor profile updated', ['user_id' => $this->id]);
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            logEvent('ERROR', 'Donor profile update failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Check if donor is eligible for donation
     */
    public function isEligibleForDonation() {
        // Check eligibility status
        if ($this->eligibilityStatus !== 'eligible') {
            return false;
        }
        
        // Check weight
        if ($this->weight < MIN_DONOR_WEIGHT) {
            return false;
        }
        
        // Check age
        if ($this->birthDate) {
            $age = calculateAge($this->birthDate);
            if ($age < MIN_DONOR_AGE || $age > MAX_DONOR_AGE) {
                return false;
            }
        }
        
        // Check last donation date
        if ($this->lastDonationDate) {
            $lastDonation = new DateTime($this->lastDonationDate);
            $today = new DateTime();
            $daysSinceLastDonation = $lastDonation->diff($today)->days;
            
            if ($daysSinceLastDonation < MIN_DONATION_INTERVAL) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Get days until eligible for donation
     */
    public function getDaysUntilEligible() {
        if (!$this->lastDonationDate) {
            return 0;
        }
        
        $lastDonation = new DateTime($this->lastDonationDate);
        $today = new DateTime();
        $daysSinceLastDonation = $lastDonation->diff($today)->days;
        
        if ($daysSinceLastDonation >= MIN_DONATION_INTERVAL) {
            return 0;
        }
        
        return MIN_DONATION_INTERVAL - $daysSinceLastDonation;
    }
    
    /**
     * Schedule a donation
     */
    public function scheduleDonation($data) {
        if (!$this->isEligibleForDonation()) {
            throw new Exception(ERROR_MESSAGES['INELIGIBLE_DONOR']);
        }
        
        $sql = "INSERT INTO donations (donor_id, blood_type_id, donation_date, location, status, request_id) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $this->db->execute($sql, [
            $this->id,
            $this->bloodTypeId,
            $data['donation_date'],
            $data['location'],
            DONATION_STATUS_SCHEDULED,
            $data['request_id'] ?? null
        ]);
        
        $donationId = $this->db->lastInsertId();
        
        logEvent('INFO', 'Donation scheduled', [
            'user_id' => $this->id,
            'donation_id' => $donationId,
            'donation_date' => $data['donation_date']
        ]);
        
        return $donationId;
    }
    
    /**
     * Complete a donation
     */
    public function completeDonation($donationId, $unitsDonated = 1, $notes = '') {
        $sql = "UPDATE donations SET 
                status = ?, 
                units_donated = ?, 
                notes = ?, 
                medical_clearance = 1, 
                updated_at = NOW() 
                WHERE id = ? AND donor_id = ?";
        
        $this->db->execute($sql, [
            DONATION_STATUS_COMPLETED,
            $unitsDonated,
            $notes,
            $donationId,
            $this->id
        ]);
        
        // Update last donation date and total donations
        $sql = "UPDATE donor_profiles SET 
                last_donation_date = CURDATE(), 
                total_donations = total_donations + 1 
                WHERE user_id = ?";
        
        $this->db->execute($sql, [$this->id]);
        
        // Update object properties
        $this->lastDonationDate = date('Y-m-d');
        $this->totalDonations += 1;
        
        logEvent('INFO', 'Donation completed', [
            'user_id' => $this->id,
            'donation_id' => $donationId,
            'units_donated' => $unitsDonated
        ]);
        
        return true;
    }
    
    /**
     * Cancel a donation
     */
    public function cancelDonation($donationId, $reason = '') {
        $sql = "UPDATE donations SET 
                status = ?, 
                notes = ?, 
                updated_at = NOW() 
                WHERE id = ? AND donor_id = ? AND status = ?";
        
        $this->db->execute($sql, [
            DONATION_STATUS_CANCELLED,
            $reason,
            $donationId,
            $this->id,
            DONATION_STATUS_SCHEDULED
        ]);
        
        logEvent('INFO', 'Donation cancelled', [
            'user_id' => $this->id,
            'donation_id' => $donationId,
            'reason' => $reason
        ]);
        
        return true;
    }
    
    /**
     * Get donation history
     */
    public function getDonationHistory($page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM donations WHERE donor_id = ?";
        $totalResult = $this->db->fetch($countSql, [$this->id]);
        $total = $totalResult['total'];
        
        // Get donations
        $sql = "SELECT d.*, bt.type as blood_type, 
                       br.id as request_id, br.recipient_id, br.hospital_name,
                       u.first_name as recipient_first_name, u.last_name as recipient_last_name
                FROM donations d
                JOIN blood_types bt ON d.blood_type_id = bt.id
                LEFT JOIN blood_requests br ON d.request_id = br.id
                LEFT JOIN users u ON br.recipient_id = u.id
                WHERE d.donor_id = ?
                ORDER BY d.donation_date DESC
                LIMIT ? OFFSET ?";
        
        $donations = $this->db->fetchAll($sql, [$this->id, $limit, $offset]);
        
        return [
            'donations' => $donations,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
    
    /**
     * Get upcoming donations
     */
    public function getUpcomingDonations() {
        $sql = "SELECT d.*, bt.type as blood_type 
                FROM donations d
                JOIN blood_types bt ON d.blood_type_id = bt.id
                WHERE d.donor_id = ? AND d.status = ? AND d.donation_date >= CURDATE()
                ORDER BY d.donation_date ASC";
        
        return $this->db->fetchAll($sql, [$this->id, DONATION_STATUS_SCHEDULED]);
    }
    
    /**
     * Get matching blood requests
     */
    public function getMatchingBloodRequests($page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        
        // Get compatible recipient blood types
        $compatibleTypes = [];
        foreach (BLOOD_COMPATIBILITY as $donorType => $recipientTypes) {
            if ($donorType === $this->bloodType) {
                $compatibleTypes = $recipientTypes;
                break;
            }
        }
        
        if (empty($compatibleTypes)) {
            return [
                'requests' => [],
                'pagination' => paginate(0, $page, $limit)
            ];
        }
        
        // Get blood type IDs
        $placeholders = implode(',', array_fill(0, count($compatibleTypes), '?'));
        $bloodTypesSql = "SELECT id FROM blood_types WHERE type IN ($placeholders)";
        $bloodTypesResult = $this->db->fetchAll($bloodTypesSql, $compatibleTypes);
        $bloodTypeIds = array_column($bloodTypesResult, 'id');
        
        if (empty($bloodTypeIds)) {
            return [
                'requests' => [],
                'pagination' => paginate(0, $page, $limit)
            ];
        }
        
        // Get total count
        $placeholders = implode(',', array_fill(0, count($bloodTypeIds), '?'));
        $countSql = "SELECT COUNT(*) as total 
                     FROM blood_requests 
                     WHERE blood_type_id IN ($placeholders) 
                     AND status IN (?, ?) 
                     AND required_by_date >= CURDATE()";
        
        $countParams = array_merge($bloodTypeIds, [REQUEST_STATUS_PENDING, REQUEST_STATUS_APPROVED]);
        $totalResult = $this->db->fetch($countSql, $countParams);
        $total = $totalResult['total'];
        
        // Get requests
        $sql = "SELECT br.*, bt.type as blood_type, 
                       u.first_name as recipient_first_name, u.last_name as recipient_last_name
                FROM blood_requests br
                JOIN blood_types bt ON br.blood_type_id = bt.id
                JOIN users u ON br.recipient_id = u.id
                WHERE br.blood_type_id IN ($placeholders) 
                AND br.status IN (?, ?) 
                AND br.required_by_date >= CURDATE()
                ORDER BY br.urgency_level DESC, br.required_by_date ASC
                LIMIT ? OFFSET ?";
        
        $params = array_merge($bloodTypeIds, [REQUEST_STATUS_PENDING, REQUEST_STATUS_APPROVED, $limit, $offset]);
        $requests = $this->db->fetchAll($sql, $params);
        
        return [
            'requests' => $requests,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
    
    /**
     * Get donation statistics
     */
    public function getDonationStatistics() {
        $sql = "SELECT 
                COUNT(*) as total_donations,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as completed_donations,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as scheduled_donations,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as cancelled_donations,
                SUM(CASE WHEN status = ? THEN units_donated ELSE 0 END) as total_units_donated
                FROM donations
                WHERE donor_id = ?";
        
        return $this->db->fetch($sql, [
            DONATION_STATUS_COMPLETED,
            DONATION_STATUS_SCHEDULED,
            DONATION_STATUS_CANCELLED,
            DONATION_STATUS_COMPLETED,
            $this->id
        ]);
    }
    
    // Getters
    public function getBloodTypeId() { return $this->bloodTypeId; }
    public function getBloodType() { return $this->bloodType; }
    public function getWeight() { return $this->weight; }
    public function getBirthDate() { return $this->birthDate; }
    public function getLastDonationDate() { return $this->lastDonationDate; }
    public function getMedicalConditions() { return $this->medicalConditions; }
    public function getEligibilityStatus() { return $this->eligibilityStatus; }
    public function getTotalDonations() { return $this->totalDonations; }
    
    /**
     * Get all donors with pagination
     */
    public static function getAll($page = 1, $limit = RECORDS_PER_PAGE, $filters = []) {
        $db = Database::getInstance();
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE u.user_type = ?";
        $params = [USER_TYPE_DONOR];
        
        if (!empty($filters['blood_type_id'])) {
            $whereClause .= " AND dp.blood_type_id = ?";
            $params[] = $filters['blood_type_id'];
        }
        
        if (!empty($filters['eligibility_status'])) {
            $whereClause .= " AND dp.eligibility_status = ?";
            $params[] = $filters['eligibility_status'];
        }
        
        if (!empty($filters['search'])) {
            $whereClause .= " AND (u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ? OR u.username LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM users u 
                     JOIN donor_profiles dp ON u.id = dp.user_id 
                     $whereClause";
        
        $totalResult = $db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get donors
        $sql = "SELECT u.*, dp.*, bt.type as blood_type 
                FROM users u 
                JOIN donor_profiles dp ON u.id = dp.user_id 
                JOIN blood_types bt ON dp.blood_type_id = bt.id 
                $whereClause 
                ORDER BY u.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $donors = $db->fetchAll($sql, $params);
        
        return [
            'donors' => $donors,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
}
?>
