-- Create donation_centers table
-- Blood Donation Management System

USE blood_donation_system;

-- Create donation_centers table
CREATE TABLE IF NOT EXISTS donation_centers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    operating_hours VARCHAR(255),
    capacity INT DEFAULT 0 COMMENT 'Daily donor capacity',
    facilities JSON COMMENT 'Available facilities as JSON array',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_city (city),
    INDEX idx_active (is_active),
    INDEX idx_name (name)
);

-- Insert default donation centers
INSERT INTO donation_centers (name, address, city, phone, email, operating_hours, capacity, facilities, is_active) VALUES
('City Blood Bank - Main Branch', '123 Main Street, Downtown', 'Manila', '+63-2-123-4567', '<EMAIL>', 'Mon-Fri 8:00AM-5:00PM, Sat 8:00AM-12:00PM', 50, '["parking", "wheelchair", "refreshments", "ac", "medical_staff"]', TRUE),
('City Blood Bank - North Branch', '456 North Avenue, Quezon City', 'Quezon City', '+63-2-234-5678', '<EMAIL>', 'Mon-Fri 9:00AM-6:00PM', 30, '["parking", "wheelchair", "wifi", "ac"]', TRUE),
('City Blood Bank - South Branch', '789 South Highway, Makati', 'Makati', '+63-2-345-6789', '<EMAIL>', 'Mon-Sat 8:00AM-4:00PM', 25, '["parking", "refreshments", "ac"]', TRUE),
('General Hospital Blood Center', 'General Hospital Complex, Medical District', 'Manila', '+63-2-456-7890', '<EMAIL>', '24/7 Emergency Services', 40, '["wheelchair", "refreshments", "medical_staff"]', TRUE),
('Community Health Center', '321 Community Road, Pasig', 'Pasig', '+63-2-567-8901', '<EMAIL>', 'Mon-Fri 7:00AM-3:00PM', 20, '["parking", "wheelchair", "wifi"]', TRUE),
('Mobile Blood Drive Unit', 'Various Locations (Check Schedule)', 'Metro Manila', '+63-2-678-9012', '<EMAIL>', 'Schedule Varies', 15, '["mobile", "medical_staff"]', TRUE),
('Philippine Red Cross - Manila', 'Red Cross Building, Bonifacio Drive', 'Manila', '+63-2-789-0123', '<EMAIL>', 'Mon-Fri 8:00AM-5:00PM', 35, '["parking", "wheelchair", "refreshments", "ac", "medical_staff"]', TRUE),
('St. Luke\'s Medical Center Blood Bank', 'St. Luke\'s Hospital, Cathedral Heights', 'Quezon City', '+63-2-890-1234', '<EMAIL>', 'Mon-Sun 6:00AM-6:00PM', 45, '["parking", "wheelchair", "refreshments", "wifi", "ac", "medical_staff"]', TRUE);

-- Update existing donations table to reference donation centers (optional)
-- This is for future enhancement to link donations to specific centers
-- ALTER TABLE donations ADD COLUMN center_id INT NULL;
-- ALTER TABLE donations ADD FOREIGN KEY (center_id) REFERENCES donation_centers(id) ON DELETE SET NULL;

SELECT 'Donation centers table created successfully!' as message;
