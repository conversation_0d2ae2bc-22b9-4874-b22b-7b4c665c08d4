/**
 * Main JavaScript File
 * Blood Donation Management System
 */

// Global variables
let notificationCount = 0;
let chatSocket = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize application
 */
function initializeApp() {
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize alerts auto-hide
    initializeAlerts();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize notification system
    initializeNotifications();
    
    // Initialize chat system
    initializeChat();
    
    // Initialize data tables
    initializeDataTables();
    
    // Initialize date pickers
    initializeDatePickers();
}

/**
 * Initialize Bootstrap tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Initialize alert auto-hide functionality
 */
function initializeAlerts() {
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            if (!alert.classList.contains('alert-info')) {
                fadeOutElement(alert);
            }
        });
    }, 5000);
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    // Add custom validation styles
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // Password confirmation validation
    const confirmPasswordFields = document.querySelectorAll('input[name="confirm_password"]');
    confirmPasswordFields.forEach(function(field) {
        field.addEventListener('input', function() {
            const passwordField = document.querySelector('input[name="password"]');
            if (passwordField && passwordField.value !== this.value) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    });
}

/**
 * Initialize notification system
 */
function initializeNotifications() {
    // Load notification count
    loadNotificationCount();
    
    // Set up periodic notification check
    setInterval(loadNotificationCount, 30000); // Check every 30 seconds
    
    // Mark notification as read when clicked
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(function(item) {
        item.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            if (notificationId) {
                markNotificationAsRead(notificationId);
            }
        });
    });
}

/**
 * Load notification count
 */
function loadNotificationCount() {
    fetch('api/notifications/count.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationBadge(data.count);
            }
        })
        .catch(error => {
            console.error('Error loading notification count:', error);
        });
}

/**
 * Update notification badge
 */
function updateNotificationBadge(count) {
    notificationCount = count;
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'inline-block';
        } else {
            badge.style.display = 'none';
        }
    }
}

/**
 * Mark notification as read
 */
function markNotificationAsRead(notificationId) {
    fetch('api/notifications/mark-read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            notification_id: notificationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.classList.remove('unread');
            }
            
            // Update count
            if (notificationCount > 0) {
                updateNotificationBadge(notificationCount - 1);
            }
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

/**
 * Initialize chat system
 */
function initializeChat() {
    const chatContainer = document.querySelector('.chat-container');
    if (chatContainer) {
        // Scroll to bottom
        chatContainer.scrollTop = chatContainer.scrollHeight;
        
        // Set up message sending
        const chatForm = document.querySelector('#chatForm');
        if (chatForm) {
            chatForm.addEventListener('submit', function(e) {
                e.preventDefault();
                sendChatMessage();
            });
        }
        
        // Set up periodic message refresh
        setInterval(loadNewMessages, 5000); // Check every 5 seconds
    }
}

/**
 * Send chat message
 */
function sendChatMessage() {
    const messageInput = document.querySelector('#messageInput');
    const receiverId = document.querySelector('#receiverId');
    
    if (!messageInput || !receiverId || !messageInput.value.trim()) {
        return;
    }
    
    const message = messageInput.value.trim();
    
    fetch('api/chat/send.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            receiver_id: receiverId.value,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            messageInput.value = '';
            loadNewMessages();
        } else {
            showAlert('Error sending message: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error sending message:', error);
        showAlert('Error sending message', 'danger');
    });
}

/**
 * Load new chat messages
 */
function loadNewMessages() {
    const receiverId = document.querySelector('#receiverId');
    const chatContainer = document.querySelector('.chat-container');
    
    if (!receiverId || !chatContainer) {
        return;
    }
    
    fetch(`api/chat/messages.php?receiver_id=${receiverId.value}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateChatMessages(data.messages);
            }
        })
        .catch(error => {
            console.error('Error loading messages:', error);
        });
}

/**
 * Update chat messages
 */
function updateChatMessages(messages) {
    const chatContainer = document.querySelector('.chat-container');
    if (!chatContainer) return;
    
    const wasAtBottom = chatContainer.scrollTop + chatContainer.clientHeight >= chatContainer.scrollHeight - 10;
    
    chatContainer.innerHTML = '';
    
    messages.forEach(function(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${message.is_sender ? 'chat-message-sender' : 'chat-message-receiver'}`;
        
        messageDiv.innerHTML = `
            <div class="message-content">${escapeHtml(message.message)}</div>
            <div class="chat-time">${formatDateTime(message.sent_at)}</div>
        `;
        
        chatContainer.appendChild(messageDiv);
    });
    
    // Scroll to bottom if user was at bottom
    if (wasAtBottom) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
}

/**
 * Initialize data tables
 */
function initializeDataTables() {
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(function(table) {
        // Add sorting functionality
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(function(header) {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                sortTable(table, this.dataset.sort);
            });
        });
    });
}

/**
 * Initialize date pickers
 */
function initializeDatePickers() {
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(function(input) {
        // Set min date to today for future dates
        if (input.classList.contains('future-date')) {
            input.min = new Date().toISOString().split('T')[0];
        }
        
        // Set max date to today for past dates
        if (input.classList.contains('past-date')) {
            input.max = new Date().toISOString().split('T')[0];
        }
    });
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.querySelector('.alert-container') || document.body;
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
    
    // Auto-hide after duration
    if (duration > 0) {
        setTimeout(function() {
            fadeOutElement(alertDiv);
        }, duration);
    }
}

/**
 * Fade out element
 */
function fadeOutElement(element) {
    element.style.transition = 'opacity 0.5s';
    element.style.opacity = '0';
    setTimeout(function() {
        if (element.parentNode) {
            element.parentNode.removeChild(element);
        }
    }, 500);
}

/**
 * Escape HTML
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Format date time
 */
function formatDateTime(dateTimeString) {
    const date = new Date(dateTimeString);
    return date.toLocaleString();
}

/**
 * Format date
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

/**
 * Sort table
 */
function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    const isAscending = table.dataset.sortDirection !== 'asc';
    table.dataset.sortDirection = isAscending ? 'asc' : 'desc';
    
    rows.sort(function(a, b) {
        const aValue = a.querySelector(`[data-sort="${column}"]`)?.textContent || '';
        const bValue = b.querySelector(`[data-sort="${column}"]`)?.textContent || '';
        
        if (isAscending) {
            return aValue.localeCompare(bValue);
        } else {
            return bValue.localeCompare(aValue);
        }
    });
    
    // Clear tbody and append sorted rows
    tbody.innerHTML = '';
    rows.forEach(function(row) {
        tbody.appendChild(row);
    });
    
    // Update sort indicators
    table.querySelectorAll('th[data-sort]').forEach(function(th) {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    const currentHeader = table.querySelector(`th[data-sort="${column}"]`);
    if (currentHeader) {
        currentHeader.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    }
}

/**
 * Confirm action
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * Copy to clipboard
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('Copied to clipboard!', 'success', 2000);
    }).catch(function() {
        showAlert('Failed to copy to clipboard', 'danger', 3000);
    });
}

/**
 * Toggle password visibility
 */
function togglePasswordVisibility(inputId, buttonId) {
    const input = document.getElementById(inputId);
    const button = document.getElementById(buttonId);
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

/**
 * Validate form before submission
 */
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

/**
 * Format file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
