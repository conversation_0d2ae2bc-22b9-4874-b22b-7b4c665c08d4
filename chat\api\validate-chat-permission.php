<?php
/**
 * Validate Chat Permission API - Restricted Messaging System
 * Blood Donation Management System
 * 
 * Validates if the current user can chat with a specific target user
 */

require_once '../../config/constants.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Start session and check authentication
startSecureSession();

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$currentUser = getCurrentUser();
$targetUserId = (int)($_GET['target_user_id'] ?? 0);

// Validate input
if ($targetUserId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid target user ID']);
    exit;
}

try {
    $db = Database::getInstance();
    
    // Get target user info
    $targetUser = $db->fetch("SELECT id, username, first_name, last_name, user_type, status FROM users WHERE id = ?", [$targetUserId]);
    
    if (!$targetUser) {
        echo json_encode(['success' => false, 'message' => 'Target user not found']);
        exit;
    }
    
    if ($targetUser['status'] !== 'active') {
        echo json_encode(['success' => false, 'message' => 'Target user is not active']);
        exit;
    }
    
    // Check if user can chat with target
    $canChat = canChatWith($currentUser['id'], $targetUserId);
    
    // Determine the reason if chat is not allowed
    $reason = '';
    if (!$canChat) {
        if ($currentUser['user_type'] !== USER_TYPE_ADMIN && $targetUser['user_type'] !== USER_TYPE_ADMIN) {
            $reason = 'Regular users can only message administrators';
        } elseif ($currentUser['id'] === $targetUserId) {
            $reason = 'Cannot message yourself';
        } else {
            $reason = 'Chat not permitted';
        }
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'can_chat' => $canChat,
            'reason' => $reason,
            'current_user' => [
                'id' => $currentUser['id'],
                'user_type' => $currentUser['user_type'],
                'is_admin' => $currentUser['user_type'] === USER_TYPE_ADMIN
            ],
            'target_user' => [
                'id' => $targetUser['id'],
                'username' => $targetUser['username'],
                'full_name' => $targetUser['first_name'] . ' ' . $targetUser['last_name'],
                'user_type' => $targetUser['user_type'],
                'is_admin' => $targetUser['user_type'] === USER_TYPE_ADMIN
            ],
            'restrictions' => [
                'regular_users_can_only_message_admins' => true,
                'admins_can_message_anyone' => true,
                'no_self_messaging' => true
            ]
        ]
    ]);
    
} catch (Exception $e) {
    logEvent('ERROR', 'Validate chat permission failed', [
        'user_id' => $currentUser['id'],
        'target_user_id' => $targetUserId,
        'error' => $e->getMessage()
    ]);
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Failed to validate chat permission'
    ]);
}
?>
