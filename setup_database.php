<?php
/**
 * Database Setup Script
 * This script creates the database and runs all necessary setup scripts
 */

echo "<h2>Database Setup</h2>";

try {
    // Connect to MySQL without specifying database
    $dsn = "mysql:host=localhost;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<p style='color: green;'>✅ Connected to MySQL server</p>";
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS blood_donation_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ Database 'blood_donation_system' created/verified</p>";
    
    // Use the database
    $pdo->exec("USE blood_donation_system");
    
    // Read and execute schema.sql
    $schemaFile = 'database/schema.sql';
    if (file_exists($schemaFile)) {
        $schema = file_get_contents($schemaFile);
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $schema)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(--|\/\*|\s*$)/', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Ignore "table already exists" errors
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<p style='color: orange;'>⚠️ Warning: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
        echo "<p style='color: green;'>✅ Schema.sql executed</p>";
    } else {
        echo "<p style='color: red;'>❌ Schema file not found: $schemaFile</p>";
    }
    
    // Run unified system migration
    $migrationFile = 'database/migrate_to_unified_system.sql';
    if (file_exists($migrationFile)) {
        $migration = file_get_contents($migrationFile);
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $migration)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(--|\/\*|\s*$)/', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Ignore "column already exists" errors
                    if (strpos($e->getMessage(), 'Duplicate column') === false && 
                        strpos($e->getMessage(), 'already exists') === false) {
                        echo "<p style='color: orange;'>⚠️ Migration warning: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
        echo "<p style='color: green;'>✅ Unified system migration executed</p>";
    } else {
        echo "<p style='color: red;'>❌ Migration file not found: $migrationFile</p>";
    }
    
    // Insert default blood types
    $bloodTypes = [
        'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
    ];
    
    foreach ($bloodTypes as $type) {
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO blood_types (type) VALUES (?)");
            $stmt->execute([$type]);
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ Blood type insert warning: " . $e->getMessage() . "</p>";
        }
    }
    echo "<p style='color: green;'>✅ Default blood types inserted</p>";
    
    // Create default admin user
    try {
        $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, password, user_type, first_name, last_name, status, is_unified_user, registration_source, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute(['admin', 'admin123', 'admin', 'System', 'Administrator', 'active', false, 'setup']);
        echo "<p style='color: green;'>✅ Default admin user created (username: admin, password: admin123)</p>";
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠️ Admin user warning: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3 style='color: green;'>🎉 Database setup completed successfully!</h3>";
    echo "<p><strong>You can now:</strong></p>";
    echo "<ul>";
    echo "<li>Try registering a new user</li>";
    echo "<li>Login with admin credentials (username: admin, password: admin123)</li>";
    echo "<li>Delete this setup file for security</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database setup failed: " . $e->getMessage() . "</p>";
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL service is running</li>";
    echo "<li>Check MySQL credentials (currently using root with no password)</li>";
    echo "<li>Ensure MySQL is accessible on localhost:3306</li>";
    echo "</ul>";
}
?>
