<?php
/**
 * Create System Announcement
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate input
        $title = sanitizeInput($_POST['title'] ?? '');
        $content = sanitizeInput($_POST['content'] ?? '');
        $announcementType = sanitizeInput($_POST['announcement_type'] ?? 'info');
        $targetRoles = $_POST['target_roles'] ?? [];
        $isPinned = isset($_POST['is_pinned']);
        $expiresAt = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
        
        // Validation
        if (empty($title) || empty($content)) {
            throw new Exception('Title and content are required.');
        }
        
        if (empty($targetRoles)) {
            throw new Exception('Please select at least one target audience.');
        }
        
        if (!in_array($announcementType, ['info', 'warning', 'urgent', 'success'])) {
            $announcementType = 'info';
        }
        
        // Validate expiration date
        if ($expiresAt && strtotime($expiresAt) <= time()) {
            throw new Exception('Expiration date must be in the future.');
        }
        
        // Insert announcement
        $sql = "INSERT INTO system_announcements (title, content, announcement_type, target_roles, is_active, is_pinned, expires_at, created_by) 
                VALUES (?, ?, ?, ?, TRUE, ?, ?, ?)";
        
        $db->execute($sql, [
            $title,
            $content,
            $announcementType,
            json_encode($targetRoles),
            $isPinned,
            $expiresAt,
            $currentUser['id']
        ]);
        
        // Log the action
        logEvent('INFO', 'System announcement created', [
            'announcement_id' => $db->lastInsertId(),
            'title' => $title,
            'type' => $announcementType,
            'target_roles' => $targetRoles,
            'created_by' => $currentUser['id']
        ]);
        
        redirectWithMessage('index.php', 'Announcement created successfully!', 'success');
        
    } catch (Exception $e) {
        redirectWithMessage('index.php', 'Error creating announcement: ' . $e->getMessage(), 'error');
    }
} else {
    redirectWithMessage('index.php', 'Invalid request method.', 'error');
}
?>
