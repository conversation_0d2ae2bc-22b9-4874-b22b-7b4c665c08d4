-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 21, 2025 at 02:09 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `blood_donation_system`
--

DELIMITER $$
--
-- Procedures
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `SetUserOffline` (IN `user_id` INT)   BEGIN
    UPDATE users 
    SET is_online = FALSE,
        last_seen = CURRENT_TIMESTAMP
    WHERE id = user_id;
    
    -- Deactivate user sessions
    UPDATE user_sessions 
    SET is_active = FALSE
    WHERE user_id = user_id AND is_active = TRUE;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `UpdateUserOnlineStatus` (IN `user_id` INT)   BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Update user's online status and last activity
    UPDATE users 
    SET is_online = TRUE,
        last_activity = CURRENT_TIMESTAMP
    WHERE id = user_id;
    
    -- Clean up expired sessions
    DELETE FROM user_sessions 
    WHERE expires_at < CURRENT_TIMESTAMP 
       OR (last_activity < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 30 MINUTE) AND is_active = TRUE);
    
    -- Update users who haven't been active for 5 minutes to offline
    UPDATE users 
    SET is_online = FALSE,
        last_seen = last_activity
    WHERE last_activity < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 5 MINUTE) 
      AND is_online = TRUE;
    
    COMMIT;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `blood_requests`
--

CREATE TABLE `blood_requests` (
  `id` int(11) NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `blood_type_id` int(11) NOT NULL,
  `units_needed` int(11) NOT NULL,
  `urgency_level` enum('low','medium','high','critical') NOT NULL,
  `hospital_name` varchar(100) NOT NULL,
  `hospital_address` text NOT NULL,
  `hospital_contact` varchar(20) DEFAULT NULL,
  `request_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `required_by_date` date NOT NULL,
  `status` enum('pending','approved','fulfilled','cancelled') DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `fulfilled_date` timestamp NULL DEFAULT NULL,
  `created_by_admin` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `blood_requests`
--

INSERT INTO `blood_requests` (`id`, `recipient_id`, `blood_type_id`, `units_needed`, `urgency_level`, `hospital_name`, `hospital_address`, `hospital_contact`, `request_date`, `required_by_date`, `status`, `admin_notes`, `fulfilled_date`, `created_by_admin`, `created_at`, `updated_at`) VALUES
(1, 5, 1, 2, 'high', 'City General Hospital', '100 Hospital Rd, City', '+**********', '2025-07-15 11:57:17', '2025-07-18', 'pending', NULL, NULL, NULL, '2025-07-15 11:57:17', '2025-07-15 11:57:17'),
(2, 6, 8, 1, 'critical', 'Emergency Medical Center', '200 Emergency Ave, City', '+**********', '2025-07-15 11:57:17', '2025-07-16', 'fulfilled', '\n', '2025-07-15 12:25:18', NULL, '2025-07-15 11:57:17', '2025-07-15 12:25:18');

-- --------------------------------------------------------

--
-- Table structure for table `blood_types`
--

CREATE TABLE `blood_types` (
  `id` int(11) NOT NULL,
  `type` varchar(5) NOT NULL,
  `compatibility_info` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `blood_types`
--

INSERT INTO `blood_types` (`id`, `type`, `compatibility_info`, `created_at`) VALUES
(1, 'A+', 'Can donate to A+ and AB+. Can receive from A+, A-, O+, O-', '2025-07-15 11:57:17'),
(2, 'A-', 'Can donate to A+, A-, AB+, AB-. Can receive from A-, O-', '2025-07-15 11:57:17'),
(3, 'B+', 'Can donate to B+ and AB+. Can receive from B+, B-, O+, O-', '2025-07-15 11:57:17'),
(4, 'B-', 'Can donate to B+, B-, AB+, AB-. Can receive from B-, O-', '2025-07-15 11:57:17'),
(5, 'AB+', 'Universal plasma donor. Can receive from all blood types', '2025-07-15 11:57:17'),
(6, 'AB-', 'Can donate to AB+, AB-. Can receive from AB-, A-, B-, O-', '2025-07-15 11:57:17'),
(7, 'O+', 'Can donate to A+, B+, AB+, O+. Can receive from O+, O-', '2025-07-15 11:57:17'),
(8, 'O-', 'Universal donor. Can donate to all blood types. Can receive from O- only', '2025-07-15 11:57:17');

-- --------------------------------------------------------

--
-- Table structure for table `chat_messages`
--

CREATE TABLE `chat_messages` (
  `id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `receiver_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `sent_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_read` tinyint(1) DEFAULT 0,
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `chat_messages`
--

INSERT INTO `chat_messages` (`id`, `sender_id`, `receiver_id`, `message`, `attachment`, `sent_at`, `is_read`, `read_at`) VALUES
(1, 5, 1, 'Hello, I need urgent help with my blood request.', NULL, '2025-07-15 11:57:17', 1, '2025-07-15 13:28:00'),
(2, 1, 5, 'Hello Alice, I have reviewed your request and it has been approved. We are working to find suitable donors.', NULL, '2025-07-15 11:57:17', 0, NULL),
(3, 2, 1, 'Hi, I would like to schedule a donation appointment.', NULL, '2025-07-15 11:57:17', 1, '2025-07-15 13:27:51'),
(4, 1, 2, 'Hello John, thank you for your willingness to donate. Please check your dashboard for available slots.', NULL, '2025-07-15 11:57:17', 0, NULL),
(5, 1, 5, 'hi', NULL, '2025-07-15 13:50:23', 0, NULL),
(6, 1, 7, 'hi', NULL, '2025-07-15 13:50:49', 0, NULL),
(7, 1, 7, 'hi', NULL, '2025-07-15 14:18:57', 0, NULL),
(8, 10, 8, 'hi', NULL, '2025-07-17 04:08:20', 1, '2025-07-17 04:08:38'),
(9, 8, 10, 'hello', NULL, '2025-07-17 04:08:42', 1, '2025-07-19 06:56:16'),
(10, 8, 10, 'hi buhi paka', NULL, '2025-07-19 06:56:03', 1, '2025-07-19 06:56:16');

-- --------------------------------------------------------

--
-- Table structure for table `donations`
--

CREATE TABLE `donations` (
  `id` int(11) NOT NULL,
  `donor_id` int(11) NOT NULL,
  `blood_type_id` int(11) NOT NULL,
  `units_donated` int(11) DEFAULT 1,
  `donation_date` date NOT NULL,
  `location` varchar(100) NOT NULL,
  `status` enum('scheduled','completed','cancelled') DEFAULT 'scheduled',
  `medical_clearance` tinyint(1) DEFAULT 0,
  `notes` text DEFAULT NULL,
  `request_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `donations`
--

INSERT INTO `donations` (`id`, `donor_id`, `blood_type_id`, `units_donated`, `donation_date`, `location`, `status`, `medical_clearance`, `notes`, `request_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, 1, '2025-06-15', 'City Blood Bank', 'completed', 1, NULL, NULL, '2025-07-15 11:57:17', '2025-07-15 11:57:17'),
(2, 3, 8, 1, '2025-05-31', 'City Blood Bank', 'completed', 1, NULL, NULL, '2025-07-15 11:57:17', '2025-07-15 11:57:17');

-- --------------------------------------------------------

--
-- Table structure for table `donation_centers`
--

CREATE TABLE `donation_centers` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `address` text NOT NULL,
  `city` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `operating_hours` varchar(255) DEFAULT NULL,
  `capacity` int(11) DEFAULT 0 COMMENT 'Daily donor capacity',
  `facilities` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Available facilities as JSON array' CHECK (json_valid(`facilities`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `donation_centers`
--

INSERT INTO `donation_centers` (`id`, `name`, `address`, `city`, `phone`, `email`, `operating_hours`, `capacity`, `facilities`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'City Blood Bank - Main Branch', '123 Main Street, Downtown', 'Manila', '+63-2-123-4567', '<EMAIL>', 'Mon-Fri 8:00AM-5:00PM, Sat 8:00AM-12:00PM', 50, '[\"parking\", \"wheelchair\", \"refreshments\", \"ac\", \"medical_staff\"]', 1, '2025-07-17 02:28:57', '2025-07-17 02:28:57'),
(2, 'City Blood Bank - North Branch', '456 North Avenue, Quezon City', 'Quezon City', '+63-2-234-5678', '<EMAIL>', 'Mon-Fri 9:00AM-6:00PM', 30, '[\"parking\", \"wheelchair\", \"wifi\", \"ac\"]', 1, '2025-07-17 02:28:57', '2025-07-17 02:28:57'),
(3, 'City Blood Bank - South Branch', '789 South Highway, Makati', 'Makati', '+63-2-345-6789', '<EMAIL>', 'Mon-Sat 8:00AM-4:00PM', 25, '[\"parking\", \"refreshments\", \"ac\"]', 1, '2025-07-17 02:28:57', '2025-07-17 02:28:57'),
(4, 'General Hospital Blood Center', 'General Hospital Complex, Medical District', 'Manila', '+63-2-456-7890', '<EMAIL>', '24/7 Emergency Services', 40, '[\"wheelchair\", \"refreshments\", \"medical_staff\"]', 1, '2025-07-17 02:28:57', '2025-07-17 02:28:57'),
(5, 'Community Health Center', '321 Community Road, Pasig', 'Pasig', '+63-2-567-8901', '<EMAIL>', 'Mon-Fri 7:00AM-3:00PM', 20, '[\"parking\", \"wheelchair\", \"wifi\"]', 1, '2025-07-17 02:28:57', '2025-07-17 02:28:57'),
(6, 'Mobile Blood Drive Unit', 'Various Locations (Check Schedule)', 'Metro Manila', '+63-2-678-9012', '<EMAIL>', 'Schedule Varies', 15, '[\"mobile\", \"medical_staff\"]', 1, '2025-07-17 02:28:57', '2025-07-17 02:28:57'),
(7, 'Philippine Red Cross - Manila', 'Red Cross Building, Bonifacio Drive', 'Manila', '+63-2-789-0123', '<EMAIL>', 'Mon-Fri 8:00AM-5:00PM', 35, '[\"parking\", \"wheelchair\", \"refreshments\", \"ac\", \"medical_staff\"]', 1, '2025-07-17 02:28:57', '2025-07-17 02:28:57'),
(8, 'St. Luke\'s Medical Center Blood Bank', 'St. Luke\'s Hospital, Cathedral Heights', 'Quezon City', '+63-2-890-1234', '<EMAIL>', 'Mon-Sun 6:00AM-6:00PM', 45, '[\"parking\", \"wheelchair\", \"refreshments\", \"wifi\", \"ac\", \"medical_staff\"]', 1, '2025-07-17 02:28:57', '2025-07-17 02:28:57');

-- --------------------------------------------------------

--
-- Table structure for table `donor_profiles`
--

CREATE TABLE `donor_profiles` (
  `user_id` int(11) NOT NULL,
  `blood_type_id` int(11) NOT NULL,
  `weight` decimal(5,2) DEFAULT 0.00,
  `birth_date` date DEFAULT NULL,
  `last_donation_date` date DEFAULT NULL,
  `medical_conditions` text DEFAULT NULL,
  `eligibility_status` enum('eligible','ineligible','temporary_defer') DEFAULT 'eligible',
  `total_donations` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `donor_profiles`
--

INSERT INTO `donor_profiles` (`user_id`, `blood_type_id`, `weight`, `birth_date`, `last_donation_date`, `medical_conditions`, `eligibility_status`, `total_donations`, `created_at`, `updated_at`) VALUES
(2, 1, 70.50, '1990-05-15', NULL, 'None', 'eligible', 0, '2025-07-15 11:57:17', '2025-07-15 11:57:17'),
(3, 8, 65.00, '1985-08-22', NULL, 'None', 'eligible', 0, '2025-07-15 11:57:17', '2025-07-15 11:57:17'),
(4, 3, 80.00, '1992-12-10', NULL, 'Mild hypertension', 'eligible', 0, '2025-07-15 11:57:17', '2025-07-15 11:57:17'),
(7, 1, 50.00, '2004-03-25', NULL, 'aa', 'eligible', 0, '2025-07-15 12:27:00', '2025-07-15 12:27:00'),
(8, 1, 50.00, '2004-03-25', NULL, 'aa', 'eligible', 0, '2025-07-15 12:29:47', '2025-07-15 12:29:47'),
(21, 7, 70.00, '1990-01-01', NULL, 'No known medical conditions. Healthy and ready to donate.', 'eligible', 0, '2025-07-21 10:24:25', '2025-07-21 10:24:25');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `target_audience` enum('all','donors','recipients','admins','unified') NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `title`, `message`, `created_by`, `target_audience`, `is_active`, `created_at`) VALUES
(1, 'Welcome to Blood Donation System', 'Thank you for joining our blood donation community. Your contribution can save lives!', 1, 'all', 1, '2025-07-15 11:57:17'),
(2, 'Urgent Blood Request', 'We have an urgent request for O- blood type. Please check your dashboard if you are eligible to donate.', 1, 'donors', 1, '2025-07-15 11:57:17'),
(3, 'Blood Request Update', 'Your blood request has been approved and we are working to fulfill it as soon as possible.', 1, 'recipients', 1, '2025-07-15 11:57:17'),
(4, 'ad', 'asd', 1, 'all', 1, '2025-07-15 12:49:31'),
(5, 'hi', 'hi', 10, 'all', 1, '2025-07-16 22:43:17'),
(7, 'Test Notification', 'This is a test notification to check if the notification system is working properly.', 10, 'all', 1, '2025-07-17 01:26:58'),
(8, 'System Testing - New Notification Feature', 'This is a test notification to verify that the notification system is working correctly. Users should be able to see this notification in their dashboards and notification pages, and mark it as read. This test confirms the end-to-end functionality of the notification system.', 1, 'all', 1, '2025-07-17 01:54:36'),
(9, 'MID RHU', 'HI', 10, 'all', 1, '2025-07-19 06:53:50'),
(10, 'System Test - Notification Delivery Test', 'This is a test notification created on July 21, 2025 to verify that the notification system is working correctly. All users (donors and recipients) should receive this notification. Please check your notifications page to confirm delivery.', 1, 'all', 1, '2025-07-21 11:59:29'),
(11, 'Donor-Only Test - July 21, 2025', 'This is a targeted notification test for DONORS ONLY. If you are seeing this message, it means you have donor privileges and the notification targeting system is working correctly. Recipients should NOT see this message.', 1, 'donors', 1, '2025-07-21 12:04:52');

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rate_limits`
--

CREATE TABLE `rate_limits` (
  `id` int(11) NOT NULL,
  `identifier` varchar(255) NOT NULL,
  `action` varchar(50) NOT NULL DEFAULT 'login',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `rate_limits`
--

INSERT INTO `rate_limits` (`id`, `identifier`, `action`, `ip_address`, `user_agent`, `created_at`) VALUES
(36, 'user3_127.0.0.1', 'failed_login', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-21 12:00:27'),
(37, 'kevenzyrel123@gmail.com_127.0.0.1', 'failed_login', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-21 12:00:57'),
(38, 'johnsmith@email.com_127.0.0.1', 'failed_login', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-21 12:01:24'),
(39, 'johnsmith@email.com_127.0.0.1', 'failed_login', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-21 12:05:51'),
(40, 'kevenzyrel_127.0.0.1', 'failed_login', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-21 12:06:16');

-- --------------------------------------------------------

--
-- Table structure for table `recipient_profiles`
--

CREATE TABLE `recipient_profiles` (
  `user_id` int(11) NOT NULL,
  `medical_condition` text DEFAULT NULL,
  `emergency_contact` varchar(100) DEFAULT NULL,
  `emergency_phone` varchar(20) DEFAULT NULL,
  `doctor_name` varchar(100) DEFAULT NULL,
  `doctor_contact` varchar(20) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `application_status` enum('pending','approved','rejected') DEFAULT 'approved',
  `application_date` timestamp NULL DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `recipient_profiles`
--

INSERT INTO `recipient_profiles` (`user_id`, `medical_condition`, `emergency_contact`, `emergency_phone`, `doctor_name`, `doctor_contact`, `created_at`, `updated_at`, `application_status`, `application_date`, `approved_by`, `approved_at`) VALUES
(5, 'Anemia', 'John Wilson', '+**********', 'Dr. Sarah Connor', '+**********', '2025-07-15 11:57:17', '2025-07-15 11:57:17', 'approved', NULL, NULL, NULL),
(6, 'Surgery patient', 'Jane Davis', '+**********', 'Dr. Michael Smith', '+**********', '2025-07-15 11:57:17', '2025-07-15 11:57:17', 'approved', NULL, NULL, NULL),
(8, 'Need blood', 'Kevenzyrel Sonsona Pascioles', '09127798040', 'Dr. James', '003-004-005', '2025-07-21 10:44:29', '2025-07-21 10:44:29', 'approved', NULL, NULL, NULL),
(21, 'Potential future need for blood transfusion due to planned surgery.', 'Jane Doe', '**********', 'Dr. Smith', '555-0123', '2025-07-21 10:25:56', '2025-07-21 10:25:56', 'approved', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `system_announcements`
--

CREATE TABLE `system_announcements` (
  `id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `announcement_type` enum('info','warning','urgent','success') DEFAULT 'info',
  `target_roles` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`target_roles`)),
  `is_active` tinyint(1) DEFAULT 1,
  `is_pinned` tinyint(1) DEFAULT 0,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `system_announcements`
--

INSERT INTO `system_announcements` (`id`, `title`, `content`, `announcement_type`, `target_roles`, `is_active`, `is_pinned`, `expires_at`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'Welcome to the New Unified System!', 'We have upgraded our system to provide a better user experience. You can now easily switch between donor and recipient roles from your dashboard.', 'info', '[\"all\"]', 1, 1, NULL, 1, '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(2, 'System Maintenance Notice', 'The system will undergo scheduled maintenance on Sunday from 2:00 AM to 4:00 AM. During this time, some features may be temporarily unavailable. We apologize for any inconvenience.', 'warning', '[\"all\"]', 1, 0, NULL, 10, '2025-07-17 00:47:40', '2025-07-17 00:47:40'),
(3, 'today is blood donation day at RHU', 'Come at 10-11', 'info', '[\"all\"]', 1, 1, '2025-07-19 06:47:00', 10, '2025-07-19 06:47:36', '2025-07-19 06:47:36'),
(4, 'aaa', 'aa', 'info', '[\"all\"]', 1, 1, '2025-07-19 06:48:00', 10, '2025-07-19 06:49:00', '2025-07-19 06:49:00');

-- --------------------------------------------------------

--
-- Table structure for table `system_logs`
--

CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL,
  `level` enum('DEBUG','INFO','WARNING','ERROR','CRITICAL') NOT NULL,
  `event` varchar(100) NOT NULL,
  `message` text DEFAULT NULL,
  `context` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`context`)),
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `user_type` enum('admin','donor','recipient','unified') NOT NULL,
  `is_unified_user` tinyint(1) DEFAULT 0,
  `primary_role` enum('donor','recipient') DEFAULT NULL,
  `registration_source` enum('donor','recipient','unified') DEFAULT 'unified',
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `status` enum('active','suspended','pending') DEFAULT 'active',
  `email_verified` tinyint(1) DEFAULT 0,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_online` tinyint(1) DEFAULT 0,
  `last_activity` timestamp NULL DEFAULT NULL,
  `last_seen` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password`, `user_type`, `is_unified_user`, `primary_role`, `registration_source`, `first_name`, `last_name`, `phone`, `address`, `profile_photo`, `status`, `email_verified`, `last_login`, `is_online`, `last_activity`, `last_seen`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', 'admin123', 'admin', 0, NULL, 'unified', 'System', 'Administrator', NULL, NULL, NULL, 'active', 1, '2025-07-21 12:03:25', 0, '2025-07-21 12:05:17', '2025-07-21 12:05:18', '2025-07-15 11:57:17', '2025-07-21 12:05:18'),
(2, 'john_donor', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'unified', 1, 'donor', 'donor', 'John', 'Smith', '+1234567890', '123 Main St, City', NULL, 'active', 1, NULL, 0, NULL, NULL, '2025-07-15 11:57:17', '2025-07-15 12:51:46'),
(3, 'mary_donor', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'unified', 1, 'donor', 'donor', 'Mary', 'Johnson', '+**********', '456 Oak Ave, City', NULL, 'active', 1, NULL, 0, NULL, NULL, '2025-07-15 11:57:17', '2025-07-15 12:51:46'),
(4, 'david_donor', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'unified', 1, 'donor', 'donor', 'David', 'Brown', '+**********', '789 Pine St, City', NULL, 'active', 1, NULL, 0, NULL, NULL, '2025-07-15 11:57:17', '2025-07-15 12:51:46'),
(5, 'alice_patient', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'unified', 1, 'recipient', 'recipient', 'Alice', 'Wilson', '+**********', '321 Elm St, City', NULL, 'active', 1, NULL, 0, NULL, NULL, '2025-07-15 11:57:17', '2025-07-15 12:51:46'),
(6, 'bob_patient', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'unified', 1, 'recipient', 'recipient', 'Bob', 'Davis', '+**********', '654 Maple Ave, City', NULL, 'active', 1, NULL, 0, NULL, NULL, '2025-07-15 11:57:17', '2025-07-15 12:51:46'),
(7, 'user2', '<EMAIL>', '$2y$10$POQYIMPVE7D6e6FwfqAy6e/U2wzLimsXpWxGW84kpYS1PT6Ftu23W', 'unified', 1, 'donor', 'donor', 'Kevenzyrel', 'Pascioles', '09511993046', '2342344', NULL, 'active', 0, NULL, 0, NULL, NULL, '2025-07-15 12:27:00', '2025-07-15 12:51:46'),
(8, 'user3', '<EMAIL>', 'Pascioles123', 'unified', 1, 'donor', 'donor', 'Kevenzyrel', 'Pascioles', '09511993046', '2342344', NULL, 'active', 0, '2025-07-21 12:07:21', 0, '2025-07-21 12:08:26', '2025-07-21 12:08:26', '2025-07-15 12:29:47', '2025-07-21 12:08:26'),
(10, 'admin2', '<EMAIL>', 'Fariol12345', 'admin', 0, NULL, 'unified', 'System', 'Administrator', NULL, NULL, NULL, 'active', NULL, '2025-07-21 10:13:07', 0, '2025-07-21 10:20:00', '2025-07-21 10:20:00', '2025-07-16 21:59:06', '2025-07-21 10:20:00'),
(11, 'testuser', '<EMAIL>', 'Test123!', 'donor', 0, NULL, 'unified', 'Test', 'User', '+1234567890', '123 Test Street', NULL, 'active', 1, '2025-07-21 10:41:42', 0, '2025-07-21 10:42:34', '2025-07-21 10:42:34', '2025-07-17 02:00:50', '2025-07-21 10:42:34'),
(12, 'EARL', '<EMAIL>', 'Pascioles12345', 'unified', 1, NULL, 'unified', 'EARL', 'FARIOL', '09127799040', 'GUMS', NULL, 'active', 0, '2025-07-21 09:38:12', 0, '2025-07-21 10:06:08', '2025-07-21 10:06:08', '2025-07-21 09:25:14', '2025-07-21 10:06:08'),
(14, 'role_test_1753093072', '<EMAIL>', 'TestPass123', 'unified', 1, NULL, 'unified', 'Role', 'Test', '1234567890', '123 Test St', NULL, 'active', 0, NULL, 0, NULL, NULL, '2025-07-21 10:17:52', '2025-07-21 10:17:52'),
(21, 'testuser2025', '<EMAIL>', 'TestPass123', 'unified', 1, 'donor', 'unified', 'Test', 'User', '1234567890', '123 Test Street, Test City', NULL, 'active', 0, '2025-07-21 10:59:57', 0, '2025-07-21 11:23:43', '2025-07-21 11:23:43', '2025-07-21 10:21:55', '2025-07-21 11:23:43');

-- --------------------------------------------------------

--
-- Table structure for table `user_announcement_views`
--

CREATE TABLE `user_announcement_views` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `announcement_id` int(11) NOT NULL,
  `viewed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_notifications`
--

CREATE TABLE `user_notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `notification_id` int(11) NOT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_notifications`
--

INSERT INTO `user_notifications` (`id`, `user_id`, `notification_id`, `is_read`, `read_at`, `created_at`) VALUES
(1, 1, 1, 0, NULL, '2025-07-15 11:57:17'),
(2, 2, 1, 0, NULL, '2025-07-15 11:57:17'),
(3, 2, 2, 0, NULL, '2025-07-15 11:57:17'),
(4, 3, 1, 0, NULL, '2025-07-15 11:57:17'),
(5, 3, 2, 0, NULL, '2025-07-15 11:57:17'),
(6, 4, 1, 0, NULL, '2025-07-15 11:57:17'),
(7, 4, 2, 0, NULL, '2025-07-15 11:57:17'),
(8, 5, 1, 0, NULL, '2025-07-15 11:57:17'),
(9, 5, 3, 0, NULL, '2025-07-15 11:57:17'),
(10, 6, 1, 0, NULL, '2025-07-15 11:57:17'),
(11, 6, 3, 0, NULL, '2025-07-15 11:57:17'),
(16, 2, 4, 0, NULL, '2025-07-15 12:49:31'),
(17, 3, 4, 0, NULL, '2025-07-15 12:49:31'),
(18, 4, 4, 0, NULL, '2025-07-15 12:49:31'),
(19, 5, 4, 0, NULL, '2025-07-15 12:49:31'),
(20, 6, 4, 0, NULL, '2025-07-15 12:49:31'),
(21, 7, 4, 0, NULL, '2025-07-15 12:49:31'),
(22, 8, 4, 1, '2025-07-17 02:03:45', '2025-07-15 12:49:31'),
(23, 11, 9, 0, NULL, '2025-07-19 06:53:50'),
(24, 2, 10, 0, NULL, '2025-07-21 11:59:29'),
(25, 3, 10, 0, NULL, '2025-07-21 11:59:29'),
(26, 4, 10, 0, NULL, '2025-07-21 11:59:29'),
(27, 5, 10, 0, NULL, '2025-07-21 11:59:29'),
(28, 6, 10, 0, NULL, '2025-07-21 11:59:29'),
(29, 7, 10, 0, NULL, '2025-07-21 11:59:29'),
(30, 8, 10, 1, '2025-07-21 12:02:40', '2025-07-21 11:59:29'),
(31, 11, 10, 0, NULL, '2025-07-21 11:59:29'),
(32, 21, 10, 0, NULL, '2025-07-21 11:59:29'),
(33, 2, 11, 0, NULL, '2025-07-21 12:04:52'),
(34, 3, 11, 0, NULL, '2025-07-21 12:04:52'),
(35, 4, 11, 0, NULL, '2025-07-21 12:04:52'),
(36, 7, 11, 0, NULL, '2025-07-21 12:04:52'),
(37, 8, 11, 0, NULL, '2025-07-21 12:04:52'),
(38, 11, 11, 0, NULL, '2025-07-21 12:04:52'),
(39, 21, 11, 0, NULL, '2025-07-21 12:04:52');

-- --------------------------------------------------------

--
-- Table structure for table `user_preferences`
--

CREATE TABLE `user_preferences` (
  `user_id` int(11) NOT NULL,
  `default_role` enum('donor','recipient') DEFAULT NULL,
  `dashboard_theme` varchar(50) DEFAULT 'blood-red',
  `email_notifications` tinyint(1) DEFAULT 1,
  `sms_notifications` tinyint(1) DEFAULT 0,
  `push_notifications` tinyint(1) DEFAULT 1,
  `language` varchar(10) DEFAULT 'en',
  `timezone` varchar(50) DEFAULT 'UTC',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_preferences`
--

INSERT INTO `user_preferences` (`user_id`, `default_role`, `dashboard_theme`, `email_notifications`, `sms_notifications`, `push_notifications`, `language`, `timezone`, `created_at`, `updated_at`) VALUES
(2, 'donor', 'blood-red', 1, 0, 1, 'en', 'UTC', '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(3, 'donor', 'blood-red', 1, 0, 1, 'en', 'UTC', '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(4, 'donor', 'blood-red', 1, 0, 1, 'en', 'UTC', '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(5, 'recipient', 'blood-red', 1, 0, 1, 'en', 'UTC', '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(6, 'recipient', 'blood-red', 1, 0, 1, 'en', 'UTC', '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(7, 'donor', 'blood-red', 1, 0, 1, 'en', 'UTC', '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(8, 'donor', 'blood-red', 1, 0, 1, 'en', 'UTC', '2025-07-15 12:51:46', '2025-07-15 12:51:46');

-- --------------------------------------------------------

--
-- Table structure for table `user_roles`
--

CREATE TABLE `user_roles` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role_type` enum('donor','recipient') NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `activated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `deactivated_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_roles`
--

INSERT INTO `user_roles` (`id`, `user_id`, `role_type`, `is_active`, `activated_at`, `deactivated_at`, `created_at`, `updated_at`) VALUES
(1, 2, 'donor', 1, '2025-07-15 11:57:17', NULL, '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(2, 3, 'donor', 1, '2025-07-15 11:57:17', NULL, '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(3, 4, 'donor', 1, '2025-07-15 11:57:17', NULL, '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(4, 7, 'donor', 1, '2025-07-15 12:27:00', NULL, '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(5, 8, 'donor', 1, '2025-07-15 12:29:47', NULL, '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(8, 5, 'recipient', 1, '2025-07-15 11:57:17', NULL, '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(9, 6, 'recipient', 1, '2025-07-15 11:57:17', NULL, '2025-07-15 12:51:46', '2025-07-15 12:51:46'),
(19, 21, 'donor', 1, '2025-07-21 10:24:25', NULL, '2025-07-21 10:24:25', '2025-07-21 10:24:25'),
(20, 21, 'recipient', 1, '2025-07-21 10:25:56', NULL, '2025-07-21 10:25:56', '2025-07-21 10:25:56'),
(21, 8, 'recipient', 1, '2025-07-21 10:44:29', NULL, '2025-07-21 10:44:29', '2025-07-21 10:44:29');

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `session_id` varchar(255) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_sessions`
--

INSERT INTO `user_sessions` (`id`, `user_id`, `session_id`, `ip_address`, `user_agent`, `is_active`, `last_activity`, `created_at`, `expires_at`) VALUES
(1, 1, 'ic8uaq7rakciukut17a1tot36d', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-15 14:19:06', '2025-07-15 14:03:48', '2025-07-16 14:03:48'),
(2, 1, '828aujf091ljdmkfo6500utm4q', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-16 21:47:44', '2025-07-16 21:47:39', '2025-07-17 21:47:39'),
(6, 1, 'l7kl9hhtl9j92keqdljqh22e4k', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-16 22:07:11', '2025-07-16 22:07:00', '2025-07-17 22:07:00'),
(7, 1, 'ih2iopt7sb4tf7kflppgtr5bhh', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-16 22:10:14', '2025-07-16 22:07:36', '2025-07-17 22:07:36'),
(8, 10, 'bkhp4dgib473fjiitfmn0ubr5k', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-16 22:44:28', '2025-07-16 22:12:27', '2025-07-17 22:12:27'),
(9, 10, '8jb1fdf91kmd24qrsjvgovhpjc', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-16 23:13:20', '2025-07-16 22:42:38', '2025-07-17 22:42:38'),
(10, 10, 'evgtt78dr5bakfhkqsgjd9nqrh', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-16 23:15:54', '2025-07-16 23:13:14', '2025-07-17 23:13:14'),
(11, 10, 'bd9hc28jup732vlua3epuc677o', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-16 23:47:04', '2025-07-16 23:25:31', '2025-07-17 23:25:31'),
(12, 10, 'p79729bvbk1vqvknhspi798nok', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 00:02:29', '2025-07-16 23:46:49', '2025-07-17 23:46:49'),
(13, 8, '65sthk0pv2e1gjdu3j6f6ugqc2', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 00:03:39', '2025-07-17 00:03:04', '2025-07-18 00:03:04'),
(14, 10, 'pjeugg3mn09gp64d2vii183h3o', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 00:38:30', '2025-07-17 00:03:44', '2025-07-18 00:03:44'),
(15, 10, '8gid52ggrtd152qh7u836g3qpk', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 01:03:01', '2025-07-17 00:35:02', '2025-07-18 00:35:02'),
(16, 10, '205j0grmirgdau344tpnjvphtl', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 01:23:24', '2025-07-17 00:46:38', '2025-07-18 00:46:38'),
(17, 10, 'mkuh1cfnpbedstmqlrn9dc77b0', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 01:39:45', '2025-07-17 01:06:16', '2025-07-18 01:06:16'),
(18, 10, '9tj1nu6epelnk5emssnajv76sq', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 02:24:05', '2025-07-17 01:22:57', '2025-07-18 01:22:57'),
(19, 10, 'p8vb1oplkaveibht9p0udm9uiv', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 02:24:05', '2025-07-17 01:36:20', '2025-07-18 01:36:20'),
(20, 1, 'uolsrhqefhiua172mqnv2f2t57', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 01:54:58', '2025-07-17 01:53:35', '2025-07-18 01:53:35'),
(21, 8, '8e0cksof13hmquqnbb3cn532lt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 04:08:38', '2025-07-17 02:03:15', '2025-07-18 02:03:15'),
(22, 10, '8r2b3bmfiu0tircsqbbn6ds43v', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 02:42:20', '2025-07-17 02:06:26', '2025-07-18 02:06:26'),
(23, 1, '95dn0hnekf81530dj1vrv5kq98', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 10:50:59', '2025-07-17 02:27:57', '2025-07-18 02:27:57'),
(24, 10, 'cjioi8scok2g6ml5lprvgiqcpo', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 03:16:11', '2025-07-17 02:41:04', '2025-07-18 02:41:04'),
(25, 1, 'ehum6re1udpjlv4gi7850u5n8l', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 10:50:59', '2025-07-17 02:58:46', '2025-07-18 02:58:46'),
(26, 10, 'nhje9p76pmjaek22sldv65v025', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 03:50:22', '2025-07-17 03:12:26', '2025-07-18 03:12:26'),
(27, 1, 'rhgs5ng8el1pnjcqcej2q0ttkv', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 10:50:59', '2025-07-17 03:29:28', '2025-07-18 03:29:28'),
(28, 10, '2i5vm49vsb2kb36u2fsk571fte', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 04:02:03', '2025-07-17 03:44:36', '2025-07-18 03:44:36'),
(29, 10, '94pqmjtciekc495c14ot1r3p87', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 04:08:23', '2025-07-17 04:07:39', '2025-07-18 04:07:39'),
(30, 8, 'mb2urvi7tcsp4drcqd2vlpn5k2', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-17 04:09:19', '2025-07-17 04:08:34', '2025-07-18 04:08:34'),
(31, 8, 'jrliu9oaal77okt8jr8fle180c', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-19 06:45:03', '2025-07-19 06:43:04', '2025-07-20 06:43:04'),
(32, 10, 'qfufamc8hhda1fjm6s6fs88re9', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-19 06:47:49', '2025-07-19 06:45:11', '2025-07-20 06:45:11'),
(33, 8, 'bu5q1g10dcoop8ftckef2u2d6n', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-19 06:48:19', '2025-07-19 06:48:04', '2025-07-20 06:48:04'),
(34, 10, 'nthqc5jb647ug9nkvbaoc75mh5', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-19 06:49:03', '2025-07-19 06:48:26', '2025-07-20 06:48:26'),
(35, 8, 'ol4mn6i8nb1fffjuugg2q4mn5t', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-19 06:54:41', '2025-07-19 06:50:37', '2025-07-20 06:50:37'),
(36, 10, 'rmbpi4vs82gd9gsrprma4hrkhd', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-19 06:53:54', '2025-07-19 06:52:22', '2025-07-20 06:52:22'),
(37, 8, '66hgp61vv1afnjqkl5uh960jqc', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-19 06:56:06', '2025-07-19 06:54:03', '2025-07-20 06:54:03'),
(38, 10, 'jv6ai2fjsk24d61mhvtoj7ggrh', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-19 06:58:06', '2025-07-19 06:56:12', '2025-07-20 06:56:12'),
(39, 10, 'fk8rrv85fr2864vbhpkget8pki', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-19 07:02:07', '2025-07-19 06:58:21', '2025-07-20 06:58:21'),
(40, 12, 'acug26oht9al0mg53sffq20cjf', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 09:31:27', '2025-07-21 09:26:05', '2025-07-22 09:26:05'),
(41, 12, 'mnodpjtk7mhorto2acdb1251rd', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 10:06:08', '2025-07-21 09:38:12', '2025-07-22 09:38:12'),
(42, 12, '5afihs2rib4tupj7mimhg1mcet', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 10:06:08', '2025-07-21 10:06:08', '2025-07-22 10:06:08'),
(43, 10, 'n6si1sakbvq3ckesvjol9didf9', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 10:20:00', '2025-07-21 10:13:07', '2025-07-22 10:13:07'),
(44, 21, 'lonrlulhu5oe3lu7eq1r91c75l', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 11:23:43', '2025-07-21 10:22:27', '2025-07-22 10:22:27'),
(45, 11, 'p6mj6em29omeq7ro1jqn71rd4m', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 10:42:34', '2025-07-21 10:41:42', '2025-07-22 10:41:42'),
(46, 8, 'nn6ng7nsif9qk07bmfd3nj0cgr', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 11:57:15', '2025-07-21 10:42:48', '2025-07-22 10:42:48'),
(47, 1, 'o9f61aia7j7qsjlenbnj7887m4', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 10:50:59', '2025-07-21 10:48:24', '2025-07-22 10:48:24'),
(48, 21, 'milc1lj16fahp84feg0ec3ls3b', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 11:23:43', '2025-07-21 10:51:34', '2025-07-22 10:51:34'),
(49, 21, 'f2jh8fvim58lhqrelbtiljdbrt', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 11:23:43', '2025-07-21 10:59:57', '2025-07-22 10:59:57'),
(50, 1, 'd4vso4q1hr9n49e339t4ammm0q', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 11:37:21', '2025-07-21 11:24:37', '2025-07-22 11:24:37'),
(51, 1, 'vrclr122981hesck6s971btvjp', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 11:43:51', '2025-07-21 11:38:56', '2025-07-22 11:38:56'),
(52, 8, '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 11:57:15', '2025-07-21 11:41:24', '2025-07-22 11:41:24'),
(53, 8, 'q4q1rmva3apsr929p7aql2nerh', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 11:57:15', '2025-07-21 11:44:15', '2025-07-22 11:44:15'),
(54, 1, 'cbf99bk6e4c3di6iqmcov5j4kl', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 11:59:52', '2025-07-21 11:57:56', '2025-07-22 11:57:56'),
(55, 8, 'le1o0qmdat30do1p7t0q3aukt3', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 12:02:51', '2025-07-21 12:01:48', '2025-07-22 12:01:48'),
(56, 1, '3ml731emee6ptnhrf479ha2143', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 12:05:19', '2025-07-21 12:03:25', '2025-07-22 12:03:25'),
(57, 8, 'bhcb2feod2v8dul403rumvanmd', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-21 12:08:26', '2025-07-21 12:07:21', '2025-07-22 12:07:21');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `blood_requests`
--
ALTER TABLE `blood_requests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by_admin` (`created_by_admin`),
  ADD KEY `idx_recipient` (`recipient_id`),
  ADD KEY `idx_blood_type` (`blood_type_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_urgency` (`urgency_level`),
  ADD KEY `idx_required_date` (`required_by_date`);

--
-- Indexes for table `blood_types`
--
ALTER TABLE `blood_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `type` (`type`);

--
-- Indexes for table `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_sender` (`sender_id`),
  ADD KEY `idx_receiver` (`receiver_id`),
  ADD KEY `idx_conversation` (`sender_id`,`receiver_id`),
  ADD KEY `idx_sent_at` (`sent_at`),
  ADD KEY `idx_unread_messages` (`receiver_id`,`is_read`,`sent_at`),
  ADD KEY `idx_user_messages` (`sender_id`,`sent_at`),
  ADD KEY `idx_message_status` (`is_read`,`read_at`);

--
-- Indexes for table `donations`
--
ALTER TABLE `donations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `request_id` (`request_id`),
  ADD KEY `idx_donor` (`donor_id`),
  ADD KEY `idx_blood_type` (`blood_type_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_donation_date` (`donation_date`);

--
-- Indexes for table `donation_centers`
--
ALTER TABLE `donation_centers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_city` (`city`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_name` (`name`);

--
-- Indexes for table `donor_profiles`
--
ALTER TABLE `donor_profiles`
  ADD PRIMARY KEY (`user_id`),
  ADD KEY `idx_blood_type` (`blood_type_id`),
  ADD KEY `idx_eligibility` (`eligibility_status`),
  ADD KEY `idx_last_donation` (`last_donation_date`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_target_audience` (`target_audience`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_expires` (`expires_at`);

--
-- Indexes for table `rate_limits`
--
ALTER TABLE `rate_limits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_identifier` (`identifier`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `recipient_profiles`
--
ALTER TABLE `recipient_profiles`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `system_announcements`
--
ALTER TABLE `system_announcements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_pinned` (`is_pinned`),
  ADD KEY `idx_expires` (`expires_at`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_level` (`level`),
  ADD KEY `idx_event` (`event`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_user_type` (`user_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_users_unified` (`is_unified_user`),
  ADD KEY `idx_users_primary_role` (`primary_role`),
  ADD KEY `idx_users_registration_source` (`registration_source`),
  ADD KEY `idx_users_online` (`is_online`),
  ADD KEY `idx_users_last_activity` (`last_activity`),
  ADD KEY `idx_users_last_seen` (`last_seen`);

--
-- Indexes for table `user_announcement_views`
--
ALTER TABLE `user_announcement_views`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_announcement` (`user_id`,`announcement_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_announcement_id` (`announcement_id`);

--
-- Indexes for table `user_notifications`
--
ALTER TABLE `user_notifications`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_notification` (`user_id`,`notification_id`),
  ADD KEY `notification_id` (`notification_id`),
  ADD KEY `idx_user_read` (`user_id`,`is_read`);

--
-- Indexes for table `user_preferences`
--
ALTER TABLE `user_preferences`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `user_roles`
--
ALTER TABLE `user_roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_role` (`user_id`,`role_type`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_role_type` (`role_type`),
  ADD KEY `idx_active` (`is_active`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_session` (`session_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_last_activity` (`last_activity`),
  ADD KEY `idx_expires` (`expires_at`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `blood_requests`
--
ALTER TABLE `blood_requests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `blood_types`
--
ALTER TABLE `blood_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `chat_messages`
--
ALTER TABLE `chat_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `donations`
--
ALTER TABLE `donations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `donation_centers`
--
ALTER TABLE `donation_centers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `password_resets`
--
ALTER TABLE `password_resets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `rate_limits`
--
ALTER TABLE `rate_limits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `system_announcements`
--
ALTER TABLE `system_announcements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `system_logs`
--
ALTER TABLE `system_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `user_announcement_views`
--
ALTER TABLE `user_announcement_views`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_notifications`
--
ALTER TABLE `user_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `user_roles`
--
ALTER TABLE `user_roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `user_sessions`
--
ALTER TABLE `user_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=58;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `blood_requests`
--
ALTER TABLE `blood_requests`
  ADD CONSTRAINT `blood_requests_ibfk_1` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `blood_requests_ibfk_2` FOREIGN KEY (`blood_type_id`) REFERENCES `blood_types` (`id`),
  ADD CONSTRAINT `blood_requests_ibfk_3` FOREIGN KEY (`created_by_admin`) REFERENCES `users` (`id`);

--
-- Constraints for table `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD CONSTRAINT `chat_messages_ibfk_1` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `chat_messages_ibfk_2` FOREIGN KEY (`receiver_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `donations`
--
ALTER TABLE `donations`
  ADD CONSTRAINT `donations_ibfk_1` FOREIGN KEY (`donor_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `donations_ibfk_2` FOREIGN KEY (`blood_type_id`) REFERENCES `blood_types` (`id`),
  ADD CONSTRAINT `donations_ibfk_3` FOREIGN KEY (`request_id`) REFERENCES `blood_requests` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `donor_profiles`
--
ALTER TABLE `donor_profiles`
  ADD CONSTRAINT `donor_profiles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `donor_profiles_ibfk_2` FOREIGN KEY (`blood_type_id`) REFERENCES `blood_types` (`id`);

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD CONSTRAINT `password_resets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `recipient_profiles`
--
ALTER TABLE `recipient_profiles`
  ADD CONSTRAINT `recipient_profiles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `system_announcements`
--
ALTER TABLE `system_announcements`
  ADD CONSTRAINT `system_announcements_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD CONSTRAINT `system_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `user_announcement_views`
--
ALTER TABLE `user_announcement_views`
  ADD CONSTRAINT `user_announcement_views_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_announcement_views_ibfk_2` FOREIGN KEY (`announcement_id`) REFERENCES `system_announcements` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_notifications`
--
ALTER TABLE `user_notifications`
  ADD CONSTRAINT `user_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_notifications_ibfk_2` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_preferences`
--
ALTER TABLE `user_preferences`
  ADD CONSTRAINT `user_preferences_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_roles`
--
ALTER TABLE `user_roles`
  ADD CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
