/**
 * Online Status Management
 * Blood Donation Management System
 */

class OnlineStatusManager {
    constructor() {
        this.updateInterval = 30000; // 30 seconds
        this.heartbeatInterval = 60000; // 1 minute
        this.isActive = true;
        this.lastActivity = Date.now();
        
        this.init();
    }
    
    init() {
        // Start periodic updates
        this.startHeartbeat();
        this.startStatusUpdates();
        
        // Track user activity
        this.trackActivity();
        
        // Handle page visibility changes
        this.handleVisibilityChange();
        
        // Handle beforeunload to set offline
        this.handlePageUnload();
    }
    
    startHeartbeat() {
        // Send heartbeat to server to maintain online status
        setInterval(() => {
            if (this.isActive) {
                this.sendHeartbeat();
            }
        }, this.heartbeatInterval);
    }
    
    startStatusUpdates() {
        // Update online status indicators on the page
        setInterval(() => {
            this.updateOnlineStatusIndicators();
        }, this.updateInterval);
    }
    
    trackActivity() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.lastActivity = Date.now();
                this.isActive = true;
            }, { passive: true });
        });
        
        // Check for inactivity
        setInterval(() => {
            const timeSinceLastActivity = Date.now() - this.lastActivity;
            if (timeSinceLastActivity > 300000) { // 5 minutes
                this.isActive = false;
            }
        }, 60000); // Check every minute
    }
    
    handleVisibilityChange() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.isActive = false;
            } else {
                this.isActive = true;
                this.lastActivity = Date.now();
                this.sendHeartbeat();
            }
        });
    }
    
    handlePageUnload() {
        window.addEventListener('beforeunload', () => {
            // Send synchronous request to set user offline
            navigator.sendBeacon('/blood donation system/api/set-offline.php', 
                JSON.stringify({ action: 'set_offline' }));
        });
    }
    
    async sendHeartbeat() {
        try {
            const response = await fetch('/blood donation system/api/heartbeat.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    timestamp: Date.now(),
                    active: this.isActive
                })
            });
            
            if (!response.ok) {
                console.warn('Heartbeat failed:', response.status);
            }
        } catch (error) {
            console.warn('Heartbeat error:', error);
        }
    }
    
    async updateOnlineStatusIndicators() {
        // Get all user elements that need status updates
        const userElements = document.querySelectorAll('[data-user-id]');
        
        if (userElements.length === 0) return;
        
        const userIds = Array.from(userElements).map(el => el.dataset.userId);
        
        try {
            const response = await fetch('/blood donation system/api/get-online-status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ user_ids: userIds })
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.updateStatusElements(data.statuses);
                }
            }
        } catch (error) {
            console.warn('Status update error:', error);
        }
    }
    
    updateStatusElements(statuses) {
        Object.entries(statuses).forEach(([userId, status]) => {
            const elements = document.querySelectorAll(`[data-user-id="${userId}"]`);

            elements.forEach(element => {
                const statusBadge = element.querySelector('.status-badge');
                const statusIndicator = element.querySelector('.status-indicator');
                const activeIndicator = element.querySelector('.active-indicator');

                if (statusBadge) {
                    if (status.is_active) {
                        statusBadge.className = 'badge bg-success status-badge';
                        statusBadge.innerHTML = 'Active';
                        statusBadge.title = 'Active';
                    } else {
                        statusBadge.className = 'badge bg-secondary status-badge';
                        statusBadge.innerHTML = 'Inactive';
                        statusBadge.title = 'Inactive';
                    }
                }

                // Update status indicator (red/green dots)
                if (statusIndicator) {
                    statusIndicator.classList.remove('active', 'inactive');
                    if (status.is_active) {
                        statusIndicator.classList.add('active');
                    } else {
                        statusIndicator.classList.add('inactive');
                    }
                }

                // Legacy support for active-indicator class
                if (activeIndicator) {
                    if (status.is_active) {
                        activeIndicator.classList.add('active');
                    } else {
                        activeIndicator.classList.remove('active');
                    }
                }
            });
        });
    }
    
    // Public method to manually update a user's status
    updateUserStatus(userId, isActive) {
        const elements = document.querySelectorAll(`[data-user-id="${userId}"]`);

        elements.forEach(element => {
            const statusBadge = element.querySelector('.status-badge');

            if (statusBadge) {
                if (isActive) {
                    statusBadge.className = 'badge bg-success status-badge';
                    statusBadge.innerHTML = '<i class="fas fa-circle"></i> Active';
                    statusBadge.title = 'Active';
                } else {
                    statusBadge.className = 'badge bg-secondary status-badge';
                    statusBadge.innerHTML = '<i class="fas fa-circle"></i> Inactive';
                    statusBadge.title = 'Inactive';
                }
            }
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.onlineStatusManager = new OnlineStatusManager();
});

// Add CSS for active indicators
const style = document.createElement('style');
style.textContent = `
    .active-indicator {
        position: relative;
    }

    .active-indicator.active::after {
        content: '';
        position: absolute;
        top: -2px;
        right: -2px;
        width: 12px;
        height: 12px;
        background: #28a745;
        border: 2px solid white;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.1); opacity: 0.7; }
        100% { transform: scale(1); opacity: 1; }
    }

    .status-badge {
        font-size: 0.7rem !important;
        padding: 0.25em 0.5em !important;
    }
`;
document.head.appendChild(style);
