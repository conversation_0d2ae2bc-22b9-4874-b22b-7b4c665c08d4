<?php
/**
 * Run Profile Management Migration
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();

// Only allow running from command line or admin access
if (php_sapi_name() !== 'cli') {
    // Check if user is admin when running from web
    if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
        die('Access denied. Admin privileges required.');
    }
}

echo "=== Blood Donation System - Profile Management Migration ===\n";
echo "Setting up database schema for comprehensive profile management...\n\n";

try {
    $db = Database::getInstance();
    
    // Read the SQL file
    $sqlFile = __DIR__ . '/profile_management_migration.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    if ($sql === false) {
        throw new Exception("Failed to read SQL file");
    }
    
    echo "Executing profile management migration...\n";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', preg_split('/;(?=(?:[^\']*\'[^\']*\')*[^\']*$)/', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt) && !preg_match('/^\s*USE\s+/', $stmt);
        }
    );
    
    $executedCount = 0;
    $skippedCount = 0;
    
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $db->getConnection()->exec($statement);
                $executedCount++;
                echo ".";
            } catch (PDOException $e) {
                // Some statements might fail if they already exist, which is okay
                if (strpos($e->getMessage(), 'already exists') !== false || 
                    strpos($e->getMessage(), 'Duplicate') !== false ||
                    strpos($e->getMessage(), 'Unknown column') !== false ||
                    strpos($e->getMessage(), 'Duplicate key') !== false) {
                    $skippedCount++;
                    echo "s"; // skipped
                } else {
                    echo "\nError executing statement: " . $e->getMessage() . "\n";
                    echo "Statement: " . substr($statement, 0, 100) . "...\n";
                    // Continue with other statements
                    $skippedCount++;
                    echo "s";
                }
            }
        }
    }
    
    echo "\n\nProfile management migration completed!\n";
    echo "Executed $executedCount SQL statements.\n";
    echo "Skipped $skippedCount statements (already exist or had issues).\n\n";
    
    // Create upload directories
    $uploadDirs = [
        '../uploads/',
        '../uploads/profiles/',
        '../uploads/profiles/thumbnails/'
    ];
    
    foreach ($uploadDirs as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "Created directory: $dir\n";
            } else {
                echo "Warning: Could not create directory: $dir\n";
            }
        } else {
            echo "Directory already exists: $dir\n";
        }
    }
    
    echo "\nProfile management system is now ready!\n\n";
    
    echo "Features enabled:\n";
    echo "✅ Personal information management\n";
    echo "✅ Secure password changes\n";
    echo "✅ Profile photo uploads\n";
    echo "✅ Role-specific profile management\n";
    echo "✅ User preferences and privacy settings\n";
    echo "✅ Comprehensive audit logging\n";
    echo "✅ CSRF protection\n";
    echo "✅ File upload security\n\n";
    
    // Log the migration
    try {
        logEvent('INFO', 'Profile management migration completed', [
            'executed_statements' => $executedCount,
            'skipped_statements' => $skippedCount
        ]);
    } catch (Exception $logError) {
        // Ignore logging errors
    }
    
} catch (Exception $e) {
    echo "\nProfile management migration failed: " . $e->getMessage() . "\n";
    
    // Log the error
    try {
        logEvent('ERROR', 'Profile management migration failed', [
            'error' => $e->getMessage()
        ]);
    } catch (Exception $logError) {
        // Ignore logging errors during migration
    }
    
    exit(1);
}

if (php_sapi_name() !== 'cli') {
    echo "<br><br><a href='../admin/'>Return to Admin Panel</a> | ";
    echo "<a href='../dashboard/profile.php'>Test Profile Management</a>";
}
?>
