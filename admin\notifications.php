<?php
/**
 * Admin Notifications Management
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../classes/Notification.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('notifications.php', 'Invalid security token', 'error');
    }
    
    $action = $_POST['action'] ?? '';
    $adminId = getCurrentUser()['id'];
    
    switch ($action) {
        case 'create':
            $notificationData = [
                'title' => sanitizeInput($_POST['title'] ?? ''),
                'message' => sanitizeInput($_POST['message'] ?? ''),
                'target_audience' => sanitizeInput($_POST['target_audience'] ?? ''),
                'created_by' => $adminId,
                'is_active' => true
            ];
            
            $validation = validateNotification($notificationData);
            
            if ($validation->isValid) {
                try {
                    $notification = Notification::create($notificationData);
                    redirectWithMessage('notifications.php', 'Notification created successfully', 'success');
                } catch (Exception $e) {
                    redirectWithMessage('notifications.php', 'Failed to create notification: ' . $e->getMessage(), 'error');
                }
            } else {
                $errors = [];
                foreach ($validation->getErrors() as $field => $fieldErrors) {
                    $errors = array_merge($errors, $fieldErrors);
                }
                redirectWithMessage('notifications.php', implode(', ', $errors), 'error');
            }
            break;
            
        case 'toggle_status':
            $notificationId = (int)($_POST['notification_id'] ?? 0);
            if ($notificationId > 0) {
                $notification = new Notification($notificationId);
                if ($notification->isActive()) {
                    $notification->deactivate();
                    redirectWithMessage('notifications.php', 'Notification deactivated', 'success');
                } else {
                    $notification->activate();
                    redirectWithMessage('notifications.php', 'Notification activated', 'success');
                }
            }
            break;
            
        case 'delete':
            $notificationId = (int)($_POST['notification_id'] ?? 0);
            if ($notificationId > 0) {
                $notification = new Notification($notificationId);
                $notification->delete();
                redirectWithMessage('notifications.php', 'Notification deleted', 'success');
            }
            break;
    }
}

// Get filters
$filters = [
    'target_audience' => sanitizeInput($_GET['target_audience'] ?? ''),
    'search' => sanitizeInput($_GET['search'] ?? '')
];

// Only add is_active filter if it's explicitly set
if (isset($_GET['is_active']) && $_GET['is_active'] !== '') {
    $filters['is_active'] = (bool)$_GET['is_active'];
}

$page = (int)($_GET['page'] ?? 1);

// Get notifications with pagination
$result = Notification::getAll($page, RECORDS_PER_PAGE, $filters);
$notifications = $result['notifications'];
$pagination = $result['pagination'];

// Get flash message
$flash = getFlashMessage();
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.php">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>

                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5><i class="fas fa-bell"></i> Notifications Management</h5>
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createNotificationModal">
                                    <i class="fas fa-plus"></i> Create Notification
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <!-- Filters -->
                        <form method="GET" class="row g-3 mb-4">
                            <div class="col-md-3">
                                <label class="form-label">Target Audience</label>
                                <select name="target_audience" class="form-select">
                                    <option value="">All Audiences</option>
                                    <option value="all" <?php echo $filters['target_audience'] === 'all' ? 'selected' : ''; ?>>All Users</option>
                                    <option value="donors" <?php echo $filters['target_audience'] === 'donors' ? 'selected' : ''; ?>>Donors</option>
                                    <option value="recipients" <?php echo $filters['target_audience'] === 'recipients' ? 'selected' : ''; ?>>Recipients</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <select name="is_active" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="1" <?php echo (isset($filters['is_active']) && $filters['is_active'] === true) ? 'selected' : ''; ?>>Active</option>
                                    <option value="0" <?php echo (isset($filters['is_active']) && $filters['is_active'] === false) ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-5">
                                <label class="form-label">Search</label>
                                <input type="text" name="search" class="form-control" placeholder="Title or message..." 
                                       value="<?php echo htmlspecialchars($filters['search']); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Notifications Table -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Target Audience</th>
                                        <th>Created By</th>
                                        <th>Created At</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($notifications)): ?>
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">No notifications found</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($notifications as $notification): ?>
                                            <tr>
                                                <td><?php echo $notification['id']; ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($notification['title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo htmlspecialchars(substr($notification['message'], 0, 100)); ?>
                                                        <?php if (strlen($notification['message']) > 100): ?>...<?php endif; ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $notification['target_audience'] === 'all' ? 'primary' : 
                                                            ($notification['target_audience'] === 'donors' ? 'success' : 'info'); 
                                                    ?>">
                                                        <i class="fas fa-<?php 
                                                            echo $notification['target_audience'] === 'all' ? 'users' : 
                                                                ($notification['target_audience'] === 'donors' ? 'heart' : 'hand-holding-medical'); 
                                                        ?>"></i>
                                                        <?php echo ucfirst($notification['target_audience']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($notification['created_by_first_name'] . ' ' . $notification['created_by_last_name']); ?>
                                                </td>
                                                <td><?php echo formatDate($notification['created_at'], DISPLAY_DATETIME_FORMAT); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $notification['is_active'] ? 'success' : 'secondary'; ?>">
                                                        <?php echo $notification['is_active'] ? 'Active' : 'Inactive'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="viewNotification(<?php echo $notification['id']; ?>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-<?php echo $notification['is_active'] ? 'warning' : 'success'; ?>" 
                                                                onclick="toggleNotificationStatus(<?php echo $notification['id']; ?>)">
                                                            <i class="fas fa-<?php echo $notification['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" onclick="deleteNotification(<?php echo $notification['id']; ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($pagination['total_pages'] > 1): ?>
                            <nav aria-label="Notifications pagination">
                                <?php echo generatePaginationHTML($pagination, 'notifications.php?' . http_build_query($filters)); ?>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Notification Modal -->
    <div class="modal fade" id="createNotificationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="modal-header">
                        <h5 class="modal-title">Create New Notification</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Title *</label>
                            <input type="text" name="title" class="form-control" required maxlength="200">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Message *</label>
                            <textarea name="message" class="form-control" rows="4" required></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Target Audience *</label>
                            <select name="target_audience" class="form-select" required>
                                <option value="">Select Audience</option>
                                <option value="all">All Users</option>
                                <option value="donors">Donors Only</option>
                                <option value="recipients">Recipients Only</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Notification</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Action Form (Hidden) -->
    <form id="actionForm" method="POST" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
        <input type="hidden" name="action" id="actionType">
        <input type="hidden" name="notification_id" id="actionNotificationId">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        function viewNotification(notificationId) {
            window.location.href = 'notification-details.php?id=' + notificationId;
        }

        function toggleNotificationStatus(notificationId) {
            document.getElementById('actionType').value = 'toggle_status';
            document.getElementById('actionNotificationId').value = notificationId;
            document.getElementById('actionForm').submit();
        }

        function deleteNotification(notificationId) {
            if (confirm('Are you sure you want to delete this notification? This action cannot be undone.')) {
                document.getElementById('actionType').value = 'delete';
                document.getElementById('actionNotificationId').value = notificationId;
                document.getElementById('actionForm').submit();
            }
        }
    </script>
</body>
</html>
