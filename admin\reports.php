<?php
/**
 * Admin Reports
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();

// Get report type
$reportType = isset($_GET['type']) ? sanitizeInput($_GET['type']) : 'donations';
$startDate = isset($_GET['start_date']) ? sanitizeInput($_GET['start_date']) : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? sanitizeInput($_GET['end_date']) : date('Y-m-d');

// Generate report data based on type
$reportData = [];
$chartData = [];

switch ($reportType) {
    case 'donations':
        // Donations by blood type
        $donationsByBloodType = $db->fetchAll(
            "SELECT bt.type, COUNT(d.id) as count, SUM(d.units_donated) as units
            FROM donations d
            JOIN blood_types bt ON d.blood_type_id = bt.id
            WHERE d.status = ? AND d.donation_date BETWEEN ? AND ?
            GROUP BY bt.type
            ORDER BY units DESC",
            [DONATION_STATUS_COMPLETED, $startDate, $endDate]
        );
        
        $reportData['by_blood_type'] = $donationsByBloodType;
        
        // Donations by date
        $donationsByDate = $db->fetchAll(
            "SELECT DATE(d.donation_date) as date, COUNT(d.id) as count, SUM(d.units_donated) as units
            FROM donations d
            WHERE d.status = ? AND d.donation_date BETWEEN ? AND ?
            GROUP BY DATE(d.donation_date)
            ORDER BY date",
            [DONATION_STATUS_COMPLETED, $startDate, $endDate]
        );
        
        $reportData['by_date'] = $donationsByDate;
        
        // Prepare chart data
        $chartLabels = [];
        $chartValues = [];
        
        foreach ($donationsByBloodType as $item) {
            $chartLabels[] = $item['type'];
            $chartValues[] = $item['units'];
        }
        
        $chartData = [
            'labels' => json_encode($chartLabels),
            'values' => json_encode($chartValues)
        ];
        
        break;
        
    case 'requests':
        // Requests by blood type
        $requestsByBloodType = $db->fetchAll(
            "SELECT bt.type, COUNT(br.id) as count, SUM(br.units_needed) as units
            FROM blood_requests br
            JOIN blood_types bt ON br.blood_type_id = bt.id
            WHERE br.created_at BETWEEN ? AND ?
            GROUP BY bt.type
            ORDER BY units DESC",
            [$startDate . ' 00:00:00', $endDate . ' 23:59:59']
        );
        
        $reportData['by_blood_type'] = $requestsByBloodType;
        
        // Requests by status
        $requestsByStatus = $db->fetchAll(
            "SELECT br.status, COUNT(br.id) as count
            FROM blood_requests br
            WHERE br.created_at BETWEEN ? AND ?
            GROUP BY br.status",
            [$startDate . ' 00:00:00', $endDate . ' 23:59:59']
        );
        
        $reportData['by_status'] = $requestsByStatus;
        
        // Requests by urgency
        $requestsByUrgency = $db->fetchAll(
            "SELECT br.urgency_level, COUNT(br.id) as count
            FROM blood_requests br
            WHERE br.created_at BETWEEN ? AND ?
            GROUP BY br.urgency_level
            ORDER BY FIELD(br.urgency_level, 'critical', 'high', 'medium', 'low')",
            [$startDate . ' 00:00:00', $endDate . ' 23:59:59']
        );
        
        $reportData['by_urgency'] = $requestsByUrgency;
        
        // Prepare chart data
        $chartLabels = [];
        $chartValues = [];
        
        foreach ($requestsByBloodType as $item) {
            $chartLabels[] = $item['type'];
            $chartValues[] = $item['units'];
        }
        
        $chartData = [
            'labels' => json_encode($chartLabels),
            'values' => json_encode($chartValues)
        ];
        
        break;
        
    case 'users':
        // Users by type
        $usersByType = $db->fetchAll(
            "SELECT user_type, COUNT(id) as count
            FROM users
            WHERE created_at BETWEEN ? AND ?
            GROUP BY user_type",
            [$startDate . ' 00:00:00', $endDate . ' 23:59:59']
        );
        
        $reportData['by_type'] = $usersByType;
        
        // Users by registration date
        $usersByDate = $db->fetchAll(
            "SELECT DATE(created_at) as date, COUNT(id) as count
            FROM users
            WHERE created_at BETWEEN ? AND ?
            GROUP BY DATE(created_at)
            ORDER BY date",
            [$startDate . ' 00:00:00', $endDate . ' 23:59:59']
        );
        
        $reportData['by_date'] = $usersByDate;
        
        // Prepare chart data
        $chartLabels = [];
        $chartValues = [];
        
        foreach ($usersByType as $item) {
            $chartLabels[] = ucfirst($item['user_type']);
            $chartValues[] = $item['count'];
        }
        
        $chartData = [
            'labels' => json_encode($chartLabels),
            'values' => json_encode($chartValues)
        ];
        
        break;
        
    case 'inventory':
        // Current blood inventory
        $inventory = $db->fetchAll(
            "SELECT bt.type, bt.id,
            COUNT(d.id) as total_donations,
            SUM(CASE WHEN d.status = ? THEN d.units_donated ELSE 0 END) as available_units,
            COUNT(CASE WHEN d.donation_date >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND d.status = ? THEN 1 END) as recent_donations
            FROM blood_types bt
            LEFT JOIN donations d ON bt.id = d.blood_type_id
            GROUP BY bt.id, bt.type
            ORDER BY bt.type",
            [DONATION_STATUS_COMPLETED, DONATION_STATUS_COMPLETED]
        );
        
        $reportData['inventory'] = $inventory;
        
        // Prepare chart data
        $chartLabels = [];
        $chartValues = [];
        
        foreach ($inventory as $item) {
            $chartLabels[] = $item['type'];
            $chartValues[] = $item['available_units'];
        }
        
        $chartData = [
            'labels' => json_encode($chartLabels),
            'values' => json_encode($chartValues)
        ];
        
        break;
}

// Get flash message
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Enhanced Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark admin-navbar">
        <div class="container-fluid px-4">
            <a class="navbar-brand fw-bold" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="navbar-brand-icon me-2">
                        <i class="fas fa-tint"></i>
                    </div>
                    <div>
                        <div class="brand-title"><?php echo APP_NAME; ?></div>
                        <small class="brand-subtitle">Administrator Panel</small>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>

                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="fas fa-chart-bar"></i> Reports</h2>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-muted small">
                    <i class="fas fa-info-circle"></i> Reports are view-only for security
                </div>
            </div>
        </div>

        <!-- Report Type Selection -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> Report Options</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="reports.php" class="row g-3">
                    <div class="col-md-3">
                        <label for="type" class="form-label">Report Type</label>
                        <select class="form-select" id="type" name="type" onchange="this.form.submit()">
                            <option value="donations" <?php echo $reportType === 'donations' ? 'selected' : ''; ?>>Donations Report</option>
                            <option value="requests" <?php echo $reportType === 'requests' ? 'selected' : ''; ?>>Blood Requests Report</option>
                            <option value="users" <?php echo $reportType === 'users' ? 'selected' : ''; ?>>Users Report</option>
                            <option value="inventory" <?php echo $reportType === 'inventory' ? 'selected' : ''; ?>>Blood Inventory Report</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                    </div>
                    
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> Generate Report
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Report Content -->
        <div class="card">
            <div class="card-header">
                <h5>
                    <?php
                    switch ($reportType) {
                        case 'donations':
                            echo '<i class="fas fa-heart"></i> Donations Report';
                            break;
                        case 'requests':
                            echo '<i class="fas fa-hand-holding-medical"></i> Blood Requests Report';
                            break;
                        case 'users':
                            echo '<i class="fas fa-users"></i> Users Report';
                            break;
                        case 'inventory':
                            echo '<i class="fas fa-warehouse"></i> Blood Inventory Report';
                            break;
                    }
                    ?>
                    <small class="text-muted ms-2">
                        <?php echo formatDate($startDate) . ' to ' . formatDate($endDate); ?>
                    </small>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Chart -->
                    <div class="col-md-6 mb-4">
                        <canvas id="reportChart" width="400" height="300"></canvas>
                    </div>
                    
                    <!-- Report Data -->
                    <div class="col-md-6">
                        <?php if ($reportType === 'donations'): ?>
                            <h5>Donations by Blood Type</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Blood Type</th>
                                            <th>Donations</th>
                                            <th>Units</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reportData['by_blood_type'] as $item): ?>
                                            <tr>
                                                <td><span class="badge bg-danger"><?php echo $item['type']; ?></span></td>
                                                <td><?php echo $item['count']; ?></td>
                                                <td><?php echo $item['units']; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                        <?php elseif ($reportType === 'requests'): ?>
                            <h5>Requests by Blood Type</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Blood Type</th>
                                            <th>Requests</th>
                                            <th>Units Needed</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reportData['by_blood_type'] as $item): ?>
                                            <tr>
                                                <td><span class="badge bg-danger"><?php echo $item['type']; ?></span></td>
                                                <td><?php echo $item['count']; ?></td>
                                                <td><?php echo $item['units']; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <h5 class="mt-4">Requests by Status</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Status</th>
                                            <th>Count</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reportData['by_status'] as $item): ?>
                                            <tr>
                                                <td>
                                                    <?php 
                                                        $statusClass = '';
                                                        switch ($item['status']) {
                                                            case REQUEST_STATUS_PENDING:
                                                                $statusClass = 'bg-warning';
                                                                break;
                                                            case REQUEST_STATUS_APPROVED:
                                                                $statusClass = 'bg-primary';
                                                                break;
                                                            case REQUEST_STATUS_FULFILLED:
                                                                $statusClass = 'bg-success';
                                                                break;
                                                            case REQUEST_STATUS_CANCELLED:
                                                                $statusClass = 'bg-danger';
                                                                break;
                                                        }
                                                    ?>
                                                    <span class="badge <?php echo $statusClass; ?>">
                                                        <?php echo REQUEST_STATUSES[$item['status']]; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo $item['count']; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                        <?php elseif ($reportType === 'users'): ?>
                            <h5>Users by Type</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>User Type</th>
                                            <th>Count</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reportData['by_type'] as $item): ?>
                                            <tr>
                                                <td>
                                                    <?php 
                                                        $typeClass = '';
                                                        switch ($item['user_type']) {
                                                            case USER_TYPE_ADMIN:
                                                                $typeClass = 'bg-dark';
                                                                break;
                                                            case USER_TYPE_DONOR:
                                                                $typeClass = 'bg-success';
                                                                break;
                                                            case USER_TYPE_RECIPIENT:
                                                                $typeClass = 'bg-info';
                                                                break;
                                                        }
                                                    ?>
                                                    <span class="badge <?php echo $typeClass; ?>">
                                                        <?php echo ucfirst($item['user_type']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo $item['count']; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                        <?php elseif ($reportType === 'inventory'): ?>
                            <h5>Current Blood Inventory</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Blood Type</th>
                                            <th>Available Units</th>
                                            <th>Recent Donations</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reportData['inventory'] as $item): ?>
                                            <tr>
                                                <td><span class="badge bg-danger"><?php echo $item['type']; ?></span></td>
                                                <td><?php echo $item['available_units']; ?></td>
                                                <td><?php echo $item['recent_donations']; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export functionality removed for security reasons -->

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize chart
        const ctx = document.getElementById('reportChart').getContext('2d');
        const reportChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: <?php echo $chartData['labels']; ?>,
                datasets: [{
                    label: '<?php echo ucfirst($reportType); ?>',
                    data: <?php echo $chartData['values']; ?>,
                    backgroundColor: [
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(25, 135, 84, 0.7)',
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(102, 16, 242, 0.7)',
                        'rgba(52, 58, 64, 0.7)'
                    ],
                    borderColor: [
                        'rgba(220, 53, 69, 1)',
                        'rgba(25, 135, 84, 1)',
                        'rgba(13, 110, 253, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(111, 66, 193, 1)',
                        'rgba(23, 162, 184, 1)',
                        'rgba(102, 16, 242, 1)',
                        'rgba(52, 58, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html> 