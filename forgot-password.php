<?php
/**
 * Forgot Password Page
 * Blood Donation Management System
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

// Start session
if (!startSecureSession()) {
    redirectWithMessage('login.php', ERROR_MESSAGES['SESSION_EXPIRED'], 'error');
}

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    switch ($user['user_type']) {
        case USER_TYPE_ADMIN:
            header('Location: admin/');
            break;
        case USER_TYPE_DONOR:
            header('Location: donor/');
            break;
        case USER_TYPE_RECIPIENT:
            header('Location: recipient/');
            break;
    }
    exit;
}

// Redirect to the new multi-step reset process
header('Location: multi-step-reset.php');
exit;
?>
