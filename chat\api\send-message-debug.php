<?php
/**
 * Send Chat Message API - Debug Version
 * Blood Donation Management System
 */

require_once '../../config/constants.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Start session and check authentication
startSecureSession();

$debug = [];
$debug['step'] = 'Starting';

if (!isLoggedIn()) {
    $debug['error'] = 'Not logged in';
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required', 'debug' => $debug]);
    exit;
}

$debug['step'] = 'Authentication passed';

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $debug['error'] = 'Wrong method: ' . $_SERVER['REQUEST_METHOD'];
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed', 'debug' => $debug]);
    exit;
}

$debug['step'] = 'POST method confirmed';

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$debug['raw_input'] = file_get_contents('php://input');
$debug['parsed_input'] = $input;

if (!$input) {
    $debug['error'] = 'Invalid JSON';
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input', 'debug' => $debug]);
    exit;
}

$debug['step'] = 'JSON parsed successfully';

// Check if MAX_MESSAGE_LENGTH is defined
if (!defined('MAX_MESSAGE_LENGTH')) {
    $debug['error'] = 'MAX_MESSAGE_LENGTH not defined';
    echo json_encode(['success' => false, 'message' => 'Configuration error: MAX_MESSAGE_LENGTH not defined', 'debug' => $debug]);
    exit;
}

$debug['max_message_length'] = MAX_MESSAGE_LENGTH;

// Validate CSRF token
$csrfToken = $input['csrf_token'] ?? '';
$debug['csrf_token_provided'] = !empty($csrfToken);
$debug['csrf_token_length'] = strlen($csrfToken);

if (!verifyCSRFToken($csrfToken)) {
    $debug['error'] = 'CSRF token verification failed';
    $debug['session_csrf_token'] = isset($_SESSION['csrf_token']) ? 'exists' : 'missing';
    $debug['session_csrf_time'] = isset($_SESSION['csrf_token_time']) ? $_SESSION['csrf_token_time'] : 'missing';
    $debug['current_time'] = time();
    if (isset($_SESSION['csrf_token_time'])) {
        $debug['token_age'] = time() - $_SESSION['csrf_token_time'];
        $debug['token_expired'] = (time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_EXPIRE;
    }
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid security token', 'debug' => $debug]);
    exit;
}

$debug['step'] = 'CSRF token verified';

$currentUser = getCurrentUser();
$debug['current_user'] = [
    'id' => $currentUser['id'],
    'username' => $currentUser['username'],
    'user_type' => $currentUser['user_type']
];

$receiverId = (int)($input['receiver_id'] ?? 0);
$message = trim($input['message'] ?? '');

$debug['receiver_id'] = $receiverId;
$debug['message_length'] = strlen($message);

// Validate input
if ($receiverId <= 0) {
    $debug['error'] = 'Invalid receiver ID';
    echo json_encode(['success' => false, 'message' => 'Invalid receiver ID', 'debug' => $debug]);
    exit;
}

if (empty($message)) {
    $debug['error'] = 'Empty message';
    echo json_encode(['success' => false, 'message' => 'Message cannot be empty', 'debug' => $debug]);
    exit;
}

if (strlen($message) > MAX_MESSAGE_LENGTH) {
    $debug['error'] = 'Message too long';
    echo json_encode(['success' => false, 'message' => 'Message too long', 'debug' => $debug]);
    exit;
}

$debug['step'] = 'Input validation passed';

// Check if receiver exists
try {
    $db = Database::getInstance();
    $receiver = $db->fetch("SELECT id, username, user_type, status FROM users WHERE id = ?", [$receiverId]);
    
    if (!$receiver) {
        $debug['error'] = 'Receiver not found';
        echo json_encode(['success' => false, 'message' => 'Receiver not found', 'debug' => $debug]);
        exit;
    }
    
    $debug['receiver'] = [
        'id' => $receiver['id'],
        'username' => $receiver['username'],
        'user_type' => $receiver['user_type'],
        'status' => $receiver['status']
    ];
    
} catch (Exception $e) {
    $debug['error'] = 'Database error checking receiver: ' . $e->getMessage();
    echo json_encode(['success' => false, 'message' => 'Database error', 'debug' => $debug]);
    exit;
}

$debug['step'] = 'Receiver verified';

// Check if user can chat with receiver
$canChat = canChatWith($currentUser['id'], $receiverId);
$debug['can_chat'] = $canChat;

if (!$canChat) {
    $debug['error'] = 'Chat permission denied';
    echo json_encode(['success' => false, 'message' => 'You cannot chat with this user', 'debug' => $debug]);
    exit;
}

$debug['step'] = 'Chat permission verified';

try {
    // Insert message
    $sql = "INSERT INTO chat_messages (sender_id, receiver_id, message) VALUES (?, ?, ?)";
    $result = $db->execute($sql, [$currentUser['id'], $receiverId, $message]);
    
    $messageId = $db->lastInsertId();
    $debug['message_id'] = $messageId;
    $debug['step'] = 'Message inserted successfully';
    
    // Log the chat message (if logEvent function exists)
    if (function_exists('logEvent')) {
        logEvent('INFO', 'Chat message sent', [
            'sender_id' => $currentUser['id'],
            'receiver_id' => $receiverId,
            'message_id' => $messageId
        ]);
        $debug['logged'] = true;
    } else {
        $debug['logged'] = false;
        $debug['log_error'] = 'logEvent function not found';
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Message sent successfully',
        'message_id' => $messageId,
        'debug' => $debug
    ]);
    
} catch (Exception $e) {
    $debug['error'] = 'Database error inserting message: ' . $e->getMessage();
    $debug['sql_error'] = $e->getCode();
    
    // Log the error (if logEvent function exists)
    if (function_exists('logEvent')) {
        logEvent('ERROR', 'Chat message send failed', [
            'sender_id' => $currentUser['id'],
            'receiver_id' => $receiverId,
            'error' => $e->getMessage()
        ]);
    }
    
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to send message', 'debug' => $debug]);
}
?>
