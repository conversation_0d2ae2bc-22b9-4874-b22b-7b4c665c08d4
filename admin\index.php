<?php
/**
 * Admin Dashboard
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';
require_once '../classes/UnifiedUser.php';
require_once '../classes/BloodRequest.php';
require_once '../classes/Notification.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

// The requirePermission function already handles admin authentication
// No need for additional checks that might cause issues

$db = Database::getInstance();

// Get dashboard statistics
$stats = [];

// Total users - Updated for unified system
try {
    $userStats = $db->fetch("SELECT
        COUNT(*) as total_users,
        SUM(CASE WHEN user_type = 'unified' OR user_type = ? THEN 1 ELSE 0 END) as total_donors,
        SUM(CASE WHEN user_type = 'unified' OR user_type = ? THEN 1 ELSE 0 END) as total_recipients,
        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN is_unified_user = TRUE THEN 1 ELSE 0 END) as unified_users
        FROM users", [USER_TYPE_DONOR, USER_TYPE_RECIPIENT, USER_STATUS_ACTIVE]);

    // Get role statistics for unified users (with error handling)
    try {
        $roleStats = $db->fetch("SELECT
            COUNT(CASE WHEN role_type = 'donor' AND is_active = TRUE THEN 1 END) as active_donors,
            COUNT(CASE WHEN role_type = 'recipient' AND is_active = TRUE THEN 1 END) as active_recipients,
            COUNT(DISTINCT user_id) as users_with_roles
            FROM user_roles WHERE is_active = TRUE");
        $userStats = array_merge($userStats, $roleStats);
    } catch (Exception $e) {
        // If user_roles table doesn't exist, set defaults
        $userStats['active_donors'] = 0;
        $userStats['active_recipients'] = 0;
        $userStats['users_with_roles'] = 0;
    }
} catch (Exception $e) {
    // Fallback if there's any database error
    $userStats = [
        'total_users' => 0,
        'total_donors' => 0,
        'total_recipients' => 0,
        'active_users' => 0,
        'unified_users' => 0,
        'active_donors' => 0,
        'active_recipients' => 0,
        'users_with_roles' => 0
    ];
}

$stats['users'] = $userStats;

// Blood requests
$requestStats = BloodRequest::getStatistics();
$stats['requests'] = $requestStats;

// Donations
$donationStats = $db->fetch("SELECT 
    COUNT(*) as total_donations,
    SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as completed_donations,
    SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as scheduled_donations,
    SUM(CASE WHEN status = ? THEN units_donated ELSE 0 END) as total_units_donated
    FROM donations", [DONATION_STATUS_COMPLETED, DONATION_STATUS_SCHEDULED, DONATION_STATUS_COMPLETED]);

$stats['donations'] = $donationStats;

// Blood inventory by type
$bloodInventory = $db->fetchAll("SELECT 
    bt.type,
    bt.id,
    COUNT(d.id) as total_donations,
    SUM(CASE WHEN d.status = ? THEN d.units_donated ELSE 0 END) as available_units,
    COUNT(CASE WHEN d.donation_date >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND d.status = ? THEN 1 END) as recent_donations
    FROM blood_types bt
    LEFT JOIN donations d ON bt.id = d.blood_type_id
    GROUP BY bt.id, bt.type
    ORDER BY bt.type", [DONATION_STATUS_COMPLETED, DONATION_STATUS_COMPLETED]);

$stats['inventory'] = $bloodInventory;

// Recent activities
$recentActivities = $db->fetchAll("
    (SELECT 'donation' as type, d.id, d.created_at as activity_date, 
            CONCAT(u.first_name, ' ', u.last_name) as user_name,
            bt.type as blood_type, d.status, d.units_donated as units
     FROM donations d
     JOIN users u ON d.donor_id = u.id
     JOIN blood_types bt ON d.blood_type_id = bt.id
     ORDER BY d.created_at DESC LIMIT 5)
    UNION ALL
    (SELECT 'request' as type, br.id, br.created_at as activity_date,
            CONCAT(u.first_name, ' ', u.last_name) as user_name,
            bt.type as blood_type, br.status, br.units_needed as units
     FROM blood_requests br
     JOIN users u ON br.recipient_id = u.id
     JOIN blood_types bt ON br.blood_type_id = bt.id
     ORDER BY br.created_at DESC LIMIT 5)
    ORDER BY activity_date DESC LIMIT 10");

// Urgent requests
$urgentRequests = BloodRequest::getUrgentRequests(5);

// Get flash message
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/responsive.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.php">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>

                </ul>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0" style="background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 8px 32px rgba(139,0,0,0.3); border-radius: 1rem;">
                    <div class="card-body text-white p-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-white bg-opacity-20 rounded-circle p-3 me-3">
                                        <i class="fas fa-tint" style="font-size: 2rem; color: white;"></i>
                                    </div>
                                    <div>
                                        <h1 class="h2 mb-1 fw-bold">Welcome back, <?php echo getCurrentUser()['first_name']; ?>!</h1>
                                        <p class="mb-0 opacity-75">Blood Donation System Administrator</p>
                                    </div>
                                </div>

                                <p class="lead mb-4 opacity-90">
                                    Manage your blood donation and request system efficiently.
                                    Monitor activities, manage users, and ensure system security.
                                </p>

                                <div class="row g-3">
                                    <div class="col-md-3 col-6">
                                        <div class="bg-white bg-opacity-10 rounded p-3 text-center">
                                            <div class="h3 mb-1 fw-bold"><?php echo number_format($stats['users']['total_users']); ?></div>
                                            <small class="opacity-75">Total Users</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <div class="bg-white bg-opacity-10 rounded p-3 text-center">
                                            <div class="h3 mb-1 fw-bold"><?php echo number_format($stats['donations']['total_donations']); ?></div>
                                            <small class="opacity-75">Donations</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <div class="bg-white bg-opacity-10 rounded p-3 text-center">
                                            <div class="h3 mb-1 fw-bold"><?php echo number_format($stats['requests']['total_requests']); ?></div>
                                            <small class="opacity-75">Requests</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <div class="bg-white bg-opacity-10 rounded p-3 text-center">
                                            <div class="h3 mb-1 fw-bold"><?php echo number_format($stats['donations']['total_units_donated']); ?></div>
                                            <small class="opacity-75">Blood Units</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="position-relative">
                                    <div class="bg-white bg-opacity-10 rounded-circle mx-auto d-flex align-items-center justify-content-center" style="width: 150px; height: 150px;">
                                        <i class="fas fa-heartbeat" style="font-size: 4rem; color: rgba(255,255,255,0.9);"></i>
                                    </div>
                                    <div class="mt-3">
                                        <div class="badge bg-white text-danger px-3 py-2">
                                            <i class="fas fa-shield-alt me-1"></i>
                                            System Secure
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Statistics -->
        <div class="row mb-4 g-4">
            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm" style="border-left: 4px solid #8B0000 !important;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-2">
                                        <i class="fas fa-users text-danger"></i>
                                    </div>
                                    <h6 class="card-title mb-0 text-danger fw-bold">Total Users</h6>
                                </div>
                                <h2 class="mb-1 fw-bold text-dark"><?php echo number_format($stats['users']['total_users']); ?></h2>
                                <small class="text-muted">
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    Active: <?php echo number_format($stats['users']['active_users']); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm" style="border-left: 4px solid #DC143C !important;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-2">
                                        <i class="fas fa-heart text-danger"></i>
                                    </div>
                                    <h6 class="card-title mb-0 text-danger fw-bold">Total Donations</h6>
                                </div>
                                <h2 class="mb-1 fw-bold text-dark"><?php echo number_format($stats['donations']['total_donations']); ?></h2>
                                <small class="text-muted">
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    Completed: <?php echo number_format($stats['donations']['completed_donations']); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm" style="border-left: 4px solid #B22222 !important;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-2">
                                        <i class="fas fa-hand-holding-medical text-danger"></i>
                                    </div>
                                    <h6 class="card-title mb-0 text-danger fw-bold">Blood Requests</h6>
                                </div>
                                <h2 class="mb-1 fw-bold text-dark"><?php echo number_format($stats['requests']['total_requests']); ?></h2>
                                <small class="text-muted">
                                    <i class="fas fa-clock text-warning me-1"></i>
                                    Pending: <?php echo number_format($stats['requests']['pending_requests']); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm" style="border-left: 4px solid #800000 !important;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-2">
                                        <i class="fas fa-tint text-danger"></i>
                                    </div>
                                    <h6 class="card-title mb-0 text-danger fw-bold">Blood Units</h6>
                                </div>
                                <h2 class="mb-1 fw-bold text-dark"><?php echo number_format($stats['donations']['total_units_donated']); ?></h2>
                                <small class="text-muted">
                                    <i class="fas fa-warehouse text-info me-1"></i>
                                    Available units
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Panel -->
        <div class="row mb-4 g-4">
            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-danger text-white border-0">
                        <h6 class="mb-0 fw-bold">
                            <i class="fas fa-users me-2"></i>User Management
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="users.php" class="btn btn-danger btn-sm">
                                <i class="fas fa-users me-1"></i> Manage Users
                            </a>
                            <a href="users.php?status=pending" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-clock me-1"></i> Pending Users
                            </a>
                            <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-user-plus me-1"></i> Add User
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-danger text-white border-0">
                        <h6 class="mb-0 fw-bold">
                            <i class="fas fa-heart me-2"></i>Donations
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="donations.php" class="btn btn-danger btn-sm">
                                <i class="fas fa-heart me-1"></i> View Donations
                            </a>
                            <a href="donations.php?status=scheduled" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-calendar me-1"></i> Scheduled
                            </a>
                            <a href="donation-centers.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-hospital me-1"></i> Donation Centers
                            </a>
                            <a href="inventory.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-tint me-1"></i> Blood Inventory
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-danger text-white border-0">
                        <h6 class="mb-0 fw-bold">
                            <i class="fas fa-hand-holding-medical me-2"></i>Requests
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="requests.php" class="btn btn-danger btn-sm">
                                <i class="fas fa-hand-holding-medical me-1"></i> Blood Requests
                            </a>
                            <a href="requests.php?status=urgent" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-exclamation-triangle me-1"></i> Urgent
                            </a>
                            <a href="matching.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-search me-1"></i> Find Matches
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-danger text-white border-0">
                        <h6 class="mb-0 fw-bold">
                            <i class="fas fa-cog me-2"></i>System
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="announcements.php" class="btn btn-danger btn-sm">
                                <i class="fas fa-bullhorn me-1"></i> Announcements
                            </a>
                            <a href="notifications.php" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-bell me-1"></i> Notifications
                            </a>
                            <a href="logs.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-file-alt me-1"></i> Audit Logs
                            </a>
                            <a href="settings.php" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-cog me-1"></i> Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status & Announcements -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="admin-panel-section">
                    <div class="section-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-bullhorn"></i> System Announcements</h5>
                            <button class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#announcementModal">
                                <i class="fas fa-plus"></i> Create Announcement
                            </button>
                        </div>
                    </div>
                    <div class="section-body">
                        <?php
                        // Create announcements table if it doesn't exist
                        try {
                            $db->execute("CREATE TABLE IF NOT EXISTS system_announcements (
                                id INT PRIMARY KEY AUTO_INCREMENT,
                                title VARCHAR(255) NOT NULL,
                                content TEXT NOT NULL,
                                announcement_type ENUM('info', 'warning', 'urgent', 'success') DEFAULT 'info',
                                target_roles JSON,
                                is_active BOOLEAN DEFAULT TRUE,
                                is_pinned BOOLEAN DEFAULT FALSE,
                                expires_at DATETIME NULL,
                                created_by INT NOT NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                FOREIGN KEY (created_by) REFERENCES users(id)
                            )");
                        } catch (Exception $e) {
                            // Table might already exist
                        }

                        // Get recent announcements
                        $recentAnnouncements = [];
                        try {
                            $recentAnnouncements = $db->fetchAll("
                                SELECT a.*, u.first_name, u.last_name
                                FROM system_announcements a
                                JOIN users u ON a.created_by = u.id
                                WHERE a.is_active = 1 AND (a.expires_at IS NULL OR a.expires_at > NOW())
                                ORDER BY a.is_pinned DESC, a.created_at DESC
                                LIMIT 3
                            ");
                        } catch (Exception $e) {
                            // Table might not exist, continue without announcements
                        }
                        ?>

                        <?php if (empty($recentAnnouncements)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No active announcements</p>
                                <button class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#announcementModal">
                                    Create First Announcement
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recentAnnouncements as $announcement): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-start">
                                        <div class="ms-2 me-auto">
                                            <?php if ($announcement['is_pinned']): ?>
                                                <i class="fas fa-thumbtack text-warning me-1" title="Pinned"></i>
                                            <?php endif; ?>
                                            <div class="fw-bold"><?php echo htmlspecialchars($announcement['title']); ?></div>
                                            <small class="text-muted"><?php echo substr(htmlspecialchars($announcement['content']), 0, 100) . '...'; ?></small>
                                            <br><small class="text-muted">By <?php echo htmlspecialchars($announcement['first_name'] . ' ' . $announcement['last_name']); ?> - <?php echo date('M j, Y', strtotime($announcement['created_at'])); ?></small>
                                        </div>
                                        <span class="badge bg-<?php
                                            echo $announcement['announcement_type'] == 'urgent' ? 'danger' :
                                                ($announcement['announcement_type'] == 'warning' ? 'warning' :
                                                ($announcement['announcement_type'] == 'success' ? 'success' : 'info'));
                                        ?> rounded-pill">
                                            <?php echo ucfirst($announcement['announcement_type']); ?>
                                        </span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="announcements.php" class="btn btn-outline-danger btn-sm">View All Announcements</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="admin-panel-section">
                    <div class="section-header">
                        <h5><i class="fas fa-server"></i> System Status</h5>
                    </div>
                    <div class="section-body">
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span><i class="fas fa-database text-success"></i> Database</span>
                                    <span class="badge bg-success">Online</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span><i class="fas fa-users text-info"></i> Active Users</span>
                                    <span class="badge bg-info"><?php echo $stats['users']['active_users']; ?></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span><i class="fas fa-clock text-warning"></i> Pending Requests</span>
                                    <span class="badge bg-warning"><?php echo $stats['requests']['pending_requests'] ?? 0; ?></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span><i class="fas fa-exclamation-triangle text-danger"></i> Urgent Requests</span>
                                    <span class="badge bg-danger"><?php echo $stats['requests']['urgent_requests'] ?? 0; ?></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                    <span><i class="fas fa-shield-alt text-success"></i> Security Status</span>
                                    <span class="badge bg-success">Secure</span>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row g-2">
                            <div class="col-6">
                                <a href="logs.php" class="btn btn-outline-danger btn-sm w-100">
                                    <i class="fas fa-file-alt"></i> View Logs
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="settings.php" class="btn btn-outline-secondary btn-sm w-100">
                                    <i class="fas fa-cog"></i> Settings
                                </a>
                            </div>
                        </div>

                        <div class="text-center mt-3">
                            <small class="text-muted">Last updated: <?php echo date('Y-m-d H:i:s'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Blood Inventory and Recent Activities -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="admin-panel-section inventory-card">
                    <div class="section-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-tint"></i> Blood Inventory Status</h5>
                            <a href="inventory.php" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-eye"></i> View Full Inventory
                            </a>
                        </div>
                    </div>
                    <div class="section-body">
                        <div class="row g-3">
                            <?php foreach ($stats['inventory'] as $blood): ?>
                                <div class="col-md-3">
                                    <div class="card text-center border-0 bg-light">
                                        <div class="card-body py-3">
                                            <div class="blood-type-badge"><?php echo htmlspecialchars($blood['type']); ?></div>
                                            <h6 class="card-title"><?php echo number_format($blood['available_units']); ?> Units</h6>
                                            <p class="card-text">
                                                <small class="text-muted">
                                                    <?php echo number_format($blood['recent_donations']); ?> recent donations
                                                </small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="admin-panel-section">
                    <div class="section-header">
                        <h5><i class="fas fa-exclamation-triangle text-danger"></i> Priority Alerts</h5>
                    </div>
                    <div class="section-body">
                        <div class="alert alert-warning d-flex align-items-center mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>
                                <strong>Low Blood Stock</strong><br>
                                <small>O- blood type running low</small>
                            </div>
                        </div>

                        <div class="alert alert-info d-flex align-items-center mb-3">
                            <i class="fas fa-clock me-2"></i>
                            <div>
                                <strong>Pending Approvals</strong><br>
                                <small><?php echo $stats['requests']['pending_requests'] ?? 0; ?> requests awaiting review</small>
                            </div>
                        </div>

                        <div class="alert alert-success d-flex align-items-center mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            <div>
                                <strong>System Status</strong><br>
                                <small>All systems operational</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities & Analytics -->
        <div class="row">
            <div class="col-md-12">
                <div class="admin-panel-section activity-card">
                    <div class="section-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-history"></i> Recent System Activities</h5>
                            <div class="btn-group btn-group-sm">
                                <a href="reports.php" class="btn btn-outline-danger">
                                    <i class="fas fa-chart-bar"></i> View Reports
                                </a>
                                <a href="logs.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-file-alt"></i> System Logs
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="section-body">
                        <?php if (empty($recentActivities)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No recent activities to display</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Type</th>
                                            <th>User</th>
                                            <th>Blood Type</th>
                                            <th>Units</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentActivities as $activity): ?>
                                            <tr>
                                                <td>
                                                    <i class="fas fa-<?php echo $activity['type'] === 'donation' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                                                    <?php echo ucfirst($activity['type']); ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($activity['user_name']); ?></td>
                                                <td>
                                                    <span class="badge bg-danger"><?php echo htmlspecialchars($activity['blood_type']); ?></span>
                                                </td>
                                                <td><?php echo number_format($activity['units']); ?></td>
                                                <td>
                                                    <span class="badge status-<?php echo $activity['status']; ?>">
                                                        <?php echo ucfirst($activity['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatDate($activity['activity_date'], DISPLAY_DATETIME_FORMAT); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="users.php" method="POST">
                    <input type="hidden" name="action" value="create">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_type" class="form-label">User Type *</label>
                                    <select class="form-select" id="user_type" name="user_type" required>
                                        <option value="">Select user type...</option>
                                        <option value="<?php echo USER_TYPE_DONOR; ?>">Donor</option>
                                        <option value="<?php echo USER_TYPE_RECIPIENT; ?>">Recipient</option>
                                        <option value="<?php echo USER_TYPE_ADMIN; ?>">Administrator</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="blood_type_id" class="form-label">Blood Type</label>
                                    <select class="form-select" id="blood_type_id" name="blood_type_id">
                                        <option value="">Select blood type...</option>
                                        <?php
                                        $bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");
                                        foreach ($bloodTypes as $type):
                                        ?>
                                            <option value="<?php echo $type['id']; ?>"><?php echo htmlspecialchars($type['type']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password *</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Create User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Announcement Modal -->
    <div class="modal fade" id="announcementModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create System Announcement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="announcementForm" action="announcements.php" method="POST">
                    <input type="hidden" name="action" value="create">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="announcementTitle" class="form-label">Title *</label>
                            <input type="text" class="form-control" id="announcementTitle" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="announcementContent" class="form-label">Content *</label>
                            <textarea class="form-control" id="announcementContent" name="content" rows="4" required></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="announcementType" class="form-label">Type</label>
                                    <select class="form-select" id="announcementType" name="announcement_type">
                                        <option value="info">Info</option>
                                        <option value="warning">Warning</option>
                                        <option value="urgent">Urgent</option>
                                        <option value="success">Success</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expiresAt" class="form-label">Expires At (Optional)</label>
                                    <input type="datetime-local" class="form-control" id="expiresAt" name="expires_at">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Target Audience *</label>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetAll" name="target_roles[]" value="all" checked>
                                        <label class="form-check-label" for="targetAll">All Users</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetDonors" name="target_roles[]" value="donors">
                                        <label class="form-check-label" for="targetDonors">Donors</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetRecipients" name="target_roles[]" value="recipients">
                                        <label class="form-check-label" for="targetRecipients">Recipients</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetUnified" name="target_roles[]" value="unified">
                                        <label class="form-check-label" for="targetUnified">Unified Users</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isPinned" name="is_pinned" value="1">
                            <label class="form-check-label" for="isPinned">Pin this announcement</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Create Announcement</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // Auto-refresh dashboard every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);

        // Announcement management functions
        function editAnnouncement(id) {
            window.location.href = 'edit-announcement.php?id=' + id;
        }

        function toggleAnnouncement(id) {
            if (confirm('Are you sure you want to toggle this announcement status?')) {
                fetch('toggle-announcement.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
            }
        }

        function deleteAnnouncement(id) {
            if (confirm('Are you sure you want to delete this announcement? This action cannot be undone.')) {
                fetch('delete-announcement.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
            }
        }

        // Handle target audience selection
        document.getElementById('targetAll').addEventListener('change', function() {
            const otherCheckboxes = document.querySelectorAll('input[name="target_roles[]"]:not(#targetAll)');
            if (this.checked) {
                otherCheckboxes.forEach(cb => cb.checked = false);
            }
        });

        document.querySelectorAll('input[name="target_roles[]"]:not(#targetAll)').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    document.getElementById('targetAll').checked = false;
                }
            });
        });


    </script>

    <!-- Admin Dashboard JavaScript -->
    <script src="../assets/js/admin-dashboard.js"></script>
    <script src="../assets/js/mobile-navigation.js"></script>
    <script src="../assets/js/admin-mobile-navigation.js"></script>

    <!-- Add refresh button to dashboard -->
    <script>
        // Add refresh button to the welcome section
        document.addEventListener('DOMContentLoaded', function() {
            const welcomeCard = document.querySelector('.welcome-card .card-body');
            if (welcomeCard) {
                const refreshBtn = document.createElement('button');
                refreshBtn.id = 'refreshDashboard';
                refreshBtn.className = 'btn btn-outline-light btn-sm position-absolute';
                refreshBtn.style.cssText = 'top: 1rem; right: 1rem;';
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
                refreshBtn.title = 'Refresh dashboard statistics';

                welcomeCard.style.position = 'relative';
                welcomeCard.appendChild(refreshBtn);
            }

            // Add critical alerts container
            const container = document.querySelector('.container-fluid');
            if (container) {
                const alertsContainer = document.createElement('div');
                alertsContainer.id = 'criticalAlerts';
                alertsContainer.className = 'mb-3';
                container.insertBefore(alertsContainer, container.firstChild);
            }
        });
    </script>
</body>
</html>
