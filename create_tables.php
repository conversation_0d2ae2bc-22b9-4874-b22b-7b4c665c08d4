<?php
/**
 * Create Database Tables
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Creating Database Tables</h2>";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=blood_donation_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Connected to database</p>";
    
    // Create blood_types table
    $sql = "CREATE TABLE IF NOT EXISTS blood_types (
        id INT PRIMARY KEY AUTO_INCREMENT,
        type VARCHAR(5) NOT NULL UNIQUE,
        compatibility_info TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Blood types table created</p>";
    
    // Create users table with both password and password_hash columns
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NULL,
        password_hash VARCHAR(255) NULL,
        user_type ENUM('admin', 'donor', 'recipient', 'unified') NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        phone VARCHAR(20),
        address TEXT,
        profile_photo VARCHAR(255),
        status ENUM('active', 'suspended', 'pending') DEFAULT 'active',
        email_verified BOOLEAN DEFAULT FALSE,
        is_unified_user BOOLEAN DEFAULT FALSE,
        primary_role VARCHAR(20) NULL,
        registration_source VARCHAR(20) DEFAULT 'legacy',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_user_type (user_type),
        INDEX idx_status (status)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Users table created</p>";
    
    // Create user_roles table
    $sql = "CREATE TABLE IF NOT EXISTS user_roles (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        role_type ENUM('donor', 'recipient') NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deactivated_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_role (user_id, role_type),
        INDEX idx_user_id (user_id),
        INDEX idx_role_type (role_type),
        INDEX idx_active (is_active)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ User roles table created</p>";
    
    // Create donor_profiles table
    $sql = "CREATE TABLE IF NOT EXISTS donor_profiles (
        user_id INT PRIMARY KEY,
        blood_type_id INT NULL,
        weight DECIMAL(5,2) DEFAULT 0,
        birth_date DATE,
        last_donation_date DATE,
        medical_conditions TEXT,
        eligibility_status ENUM('eligible', 'ineligible', 'temporary_defer') DEFAULT 'eligible',
        total_donations INT DEFAULT 0,
        application_status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved',
        application_date TIMESTAMP NULL,
        approved_by INT NULL,
        approved_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (blood_type_id) REFERENCES blood_types(id),
        INDEX idx_blood_type (blood_type_id),
        INDEX idx_eligibility (eligibility_status),
        INDEX idx_last_donation (last_donation_date)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Donor profiles table created</p>";
    
    // Create recipient_profiles table
    $sql = "CREATE TABLE IF NOT EXISTS recipient_profiles (
        user_id INT PRIMARY KEY,
        medical_condition TEXT,
        emergency_contact VARCHAR(100),
        emergency_phone VARCHAR(20),
        doctor_name VARCHAR(100),
        doctor_contact VARCHAR(20),
        application_status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved',
        application_date TIMESTAMP NULL,
        approved_by INT NULL,
        approved_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Recipient profiles table created</p>";
    
    // Insert default blood types
    $bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    foreach ($bloodTypes as $type) {
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO blood_types (type) VALUES (?)");
            $stmt->execute([$type]);
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ Blood type insert warning: " . $e->getMessage() . "</p>";
        }
    }
    echo "<p style='color: green;'>✅ Default blood types inserted</p>";
    
    // Create default admin user
    try {
        $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, password, user_type, first_name, last_name, status, is_unified_user, registration_source, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute(['admin', '<EMAIL>', 'admin123', 'admin', 'System', 'Administrator', 'active', false, 'setup']);
        echo "<p style='color: green;'>✅ Default admin user created (username: admin, password: admin123)</p>";
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠️ Admin user warning: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>✅ All tables created successfully!</h3>";
    echo "<p><strong>You can now:</strong></p>";
    echo "<ul>";
    echo "<li>Try registering a new user</li>";
    echo "<li>Login with admin credentials (username: admin, password: admin123)</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?> 