-- Update Messaging Schema for Restricted Messaging System
-- Blood Donation Management System

USE blood_donation_system;

-- Add additional indexes for better messaging performance
ALTER TABLE chat_messages 
ADD INDEX idx_conversation_time (sender_id, receiver_id, sent_at),
ADD INDEX idx_unread_messages (receiver_id, is_read, sent_at),
ADD INDEX idx_user_messages (sender_id, sent_at),
ADD INDEX idx_message_status (is_read, read_at);

-- Add constraint to prevent self-messaging (additional safety)
ALTER TABLE chat_messages 
ADD CONSTRAINT chk_no_self_message CHECK (sender_id != receiver_id);

-- Create message_threads table for better conversation management
CREATE TABLE IF NOT EXISTS message_threads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    participant1_id INT NOT NULL,
    participant2_id INT NOT NULL,
    last_message_id INT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (participant1_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (participant2_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (last_message_id) REFERENCES chat_messages(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_conversation (participant1_id, participant2_id),
    INDEX idx_participant1 (participant1_id),
    INDEX idx_participant2 (participant2_id),
    INDEX idx_last_activity (last_activity),
    
    CONSTRAINT chk_different_participants CHECK (participant1_id != participant2_id),
    CONSTRAINT chk_participant_order CHECK (participant1_id < participant2_id)
);

-- Add thread_id to chat_messages for better organization
ALTER TABLE chat_messages 
ADD COLUMN thread_id INT NULL AFTER receiver_id,
ADD FOREIGN KEY (thread_id) REFERENCES message_threads(id) ON DELETE SET NULL,
ADD INDEX idx_thread_messages (thread_id, sent_at);

-- Create messaging_permissions table for fine-grained control
CREATE TABLE IF NOT EXISTS messaging_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    can_message_admins BOOLEAN DEFAULT TRUE,
    can_message_donors BOOLEAN DEFAULT FALSE,
    can_message_recipients BOOLEAN DEFAULT FALSE,
    can_message_all BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_permissions (user_id),
    INDEX idx_user_permissions (user_id)
);

-- Insert default permissions for existing users
INSERT IGNORE INTO messaging_permissions (user_id, can_message_admins, can_message_donors, can_message_recipients, can_message_all)
SELECT 
    id,
    TRUE as can_message_admins,
    CASE WHEN user_type = 'admin' THEN TRUE ELSE FALSE END as can_message_donors,
    CASE WHEN user_type = 'admin' THEN TRUE ELSE FALSE END as can_message_recipients,
    CASE WHEN user_type = 'admin' THEN TRUE ELSE FALSE END as can_message_all
FROM users 
WHERE status = 'active';

-- Create message_reports table for reporting inappropriate messages
CREATE TABLE IF NOT EXISTS message_reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    reported_by INT NOT NULL,
    reason ENUM('spam', 'harassment', 'inappropriate', 'other') NOT NULL,
    description TEXT,
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
    reviewed_by INT NULL,
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES chat_messages(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_message_reports (message_id),
    INDEX idx_reporter (reported_by),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Create message_attachments table for file attachments
CREATE TABLE IF NOT EXISTS message_attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES chat_messages(id) ON DELETE CASCADE,
    
    INDEX idx_message_attachments (message_id),
    INDEX idx_uploaded_at (uploaded_at)
);

-- Update existing chat_messages to populate thread_id
-- This will create threads for existing conversations
INSERT IGNORE INTO message_threads (participant1_id, participant2_id, last_message_id, last_activity)
SELECT 
    LEAST(sender_id, receiver_id) as participant1_id,
    GREATEST(sender_id, receiver_id) as participant2_id,
    MAX(id) as last_message_id,
    MAX(sent_at) as last_activity
FROM chat_messages
GROUP BY LEAST(sender_id, receiver_id), GREATEST(sender_id, receiver_id);

-- Update chat_messages with thread_id
UPDATE chat_messages cm
JOIN message_threads mt ON (
    (cm.sender_id = mt.participant1_id AND cm.receiver_id = mt.participant2_id) OR
    (cm.sender_id = mt.participant2_id AND cm.receiver_id = mt.participant1_id)
)
SET cm.thread_id = mt.id
WHERE cm.thread_id IS NULL;

-- Note: Trigger for automatic thread management can be added later if needed
-- For now, we'll handle thread management in the application code

-- Create view for easy conversation access
CREATE OR REPLACE VIEW conversation_summary AS
SELECT 
    mt.id as thread_id,
    mt.participant1_id,
    mt.participant2_id,
    u1.first_name as participant1_first_name,
    u1.last_name as participant1_last_name,
    u1.user_type as participant1_type,
    u2.first_name as participant2_first_name,
    u2.last_name as participant2_last_name,
    u2.user_type as participant2_type,
    cm.message as last_message,
    mt.last_activity,
    (SELECT COUNT(*) FROM chat_messages WHERE thread_id = mt.id) as message_count,
    (SELECT COUNT(*) FROM chat_messages WHERE thread_id = mt.id AND is_read = FALSE) as unread_count
FROM message_threads mt
JOIN users u1 ON mt.participant1_id = u1.id
JOIN users u2 ON mt.participant2_id = u2.id
LEFT JOIN chat_messages cm ON mt.last_message_id = cm.id
ORDER BY mt.last_activity DESC;
