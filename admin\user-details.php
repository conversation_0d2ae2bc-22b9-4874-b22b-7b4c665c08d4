<?php
/**
 * Admin User Details
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/UnifiedUser.php';
require_once '../classes/Donor.php';
require_once '../classes/Recipient.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();

// Get user ID
$userId = (int)($_GET['id'] ?? 0);
if ($userId <= 0) {
    redirectWithMessage('users.php', 'Invalid user ID', 'error');
}

// Load user data
try {
    $user = new UnifiedUser($userId);
    $userRoles = $user->getActiveRoles();

    // Build user data array from getter methods
    $userData = [
        'id' => $user->getId(),
        'username' => $user->getUsername(),
        'first_name' => $user->getFirstName(),
        'last_name' => $user->getLastName(),
        'email' => $user->getEmail(),
        'phone' => $user->getPhone(),
        'address' => $user->getAddress(),
        'profile_photo' => $user->getProfilePhoto(),
        'status' => $user->getStatus(),
        'user_type' => $user->getUserType(),
        'email_verified' => $user->isEmailVerified(),
        'last_login' => $user->getLastLogin(),
        'created_at' => $user->getCreatedAt()
    ];
} catch (Exception $e) {
    redirectWithMessage('users.php', 'User not found', 'error');
}

// Get role-specific data
$donorData = null;
$recipientData = null;
$donorStats = null;
$recipientStats = null;

if (in_array('donor', $userRoles)) {
    try {
        $donor = $user->getDonorInstance();
        if ($donor) {
            $donorData = [
                'blood_type' => $donor->getBloodType(),
                'weight' => $donor->getWeight(),
                'birth_date' => $donor->getBirthDate(),
                'last_donation_date' => $donor->getLastDonationDate(),
                'medical_conditions' => $donor->getMedicalConditions(),
                'eligibility_status' => $donor->getEligibilityStatus(),
                'total_donations' => $donor->getTotalDonations()
            ];
            $donorStats = $donor->getDonationStatistics();
        }
    } catch (Exception $e) {
        // Handle gracefully
    }
}

if (in_array('recipient', $userRoles)) {
    try {
        $recipient = $user->getRecipientInstance();
        if ($recipient) {
            $recipientData = [
                'medical_condition' => $recipient->getMedicalCondition(),
                'emergency_contact' => $recipient->getEmergencyContact(),
                'emergency_phone' => $recipient->getEmergencyPhone(),
                'doctor_name' => $recipient->getDoctorName(),
                'doctor_contact' => $recipient->getDoctorContact()
            ];
            $recipientStats = $recipient->getRequestStatistics();
        }
    } catch (Exception $e) {
        // Handle gracefully
    }
}

// Get recent activity
$recentDonations = [];
$recentRequests = [];

if ($donorData) {
    $recentDonations = $db->fetchAll("
        SELECT d.*, bt.type as blood_type, br.urgency_level
        FROM donations d
        JOIN blood_types bt ON d.blood_type_id = bt.id
        LEFT JOIN blood_requests br ON d.request_id = br.id
        WHERE d.donor_id = ?
        ORDER BY d.donation_date DESC
        LIMIT 5
    ", [$userId]);
}

if ($recipientData) {
    $recentRequests = $db->fetchAll("
        SELECT br.*, bt.type as blood_type
        FROM blood_requests br
        JOIN blood_types bt ON br.blood_type_id = bt.id
        WHERE br.recipient_id = ?
        ORDER BY br.created_at DESC
        LIMIT 5
    ", [$userId]);
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Details - <?php echo htmlspecialchars($userData['first_name'] . ' ' . $userData['last_name']); ?> - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="users.php">
                    <i class="fas fa-arrow-left"></i> Back to Users
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- User Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <?php if ($userData['profile_photo']): ?>
                                            <img src="../uploads/<?php echo htmlspecialchars($userData['profile_photo']); ?>"
                                                 class="rounded-circle" width="80" height="80">
                                        <?php else: ?>
                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 80px; height: 80px;">
                                                <i class="fas fa-user text-white fa-2x"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <h3 class="mb-1"><?php echo htmlspecialchars($userData['first_name'] . ' ' . $userData['last_name']); ?></h3>
                                        <p class="text-muted mb-2">@<?php echo htmlspecialchars($userData['username']); ?></p>
                                        <div class="d-flex flex-wrap gap-2">
                                            <span class="badge bg-<?php echo $userData['user_type'] === 'unified' ? 'primary' : ($userData['user_type'] === 'donor' ? 'success' : ($userData['user_type'] === 'admin' ? 'danger' : 'info')); ?>">
                                                <i class="fas fa-<?php echo $userData['user_type'] === 'unified' ? 'user-tag' : ($userData['user_type'] === 'donor' ? 'heart' : ($userData['user_type'] === 'admin' ? 'shield-alt' : 'hand-holding-medical')); ?>"></i>
                                                <?php echo ucfirst($userData['user_type']); ?>
                                            </span>
                                            <?php foreach ($userRoles as $role): ?>
                                                <span class="badge bg-<?php echo $role === 'donor' ? 'success' : 'info'; ?>">
                                                    <i class="fas fa-<?php echo $role === 'donor' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                                                    <?php echo ucfirst($role); ?>
                                                </span>
                                            <?php endforeach; ?>
                                            <span class="badge bg-<?php echo $userData['status'] === 'active' ? 'success' : ($userData['status'] === 'suspended' ? 'danger' : 'warning'); ?>">
                                                <i class="fas fa-<?php echo $userData['status'] === 'active' ? 'check-circle' : ($userData['status'] === 'suspended' ? 'ban' : 'clock'); ?>"></i>
                                                <?php echo ucfirst($userData['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <?php if ($userData['status'] === 'active'): ?>
                                        <button class="btn btn-warning" onclick="suspendUser(<?php echo $userId; ?>)">
                                            <i class="fas fa-ban"></i> Suspend
                                        </button>
                                    <?php else: ?>
                                        <button class="btn btn-success" onclick="activateUser(<?php echo $userId; ?>)">
                                            <i class="fas fa-check"></i> Activate
                                        </button>
                                    <?php endif; ?>
                                    <button class="btn btn-danger" onclick="deleteUser(<?php echo $userId; ?>)">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Information Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="userTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                                    <i class="fas fa-user"></i> Basic Info
                                </button>
                            </li>
                            <?php if ($donorData): ?>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="donor-tab" data-bs-toggle="tab" data-bs-target="#donor" type="button" role="tab">
                                    <i class="fas fa-heart"></i> Donor Profile
                                </button>
                            </li>
                            <?php endif; ?>
                            <?php if ($recipientData): ?>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="recipient-tab" data-bs-toggle="tab" data-bs-target="#recipient" type="button" role="tab">
                                    <i class="fas fa-hand-holding-medical"></i> Recipient Profile
                                </button>
                            </li>
                            <?php endif; ?>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                                    <i class="fas fa-history"></i> Recent Activity
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="userTabsContent">
                            <!-- Basic Information Tab -->
                            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-info-circle"></i> Personal Information</h6>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Full Name:</strong></td>
                                                <td><?php echo htmlspecialchars($userData['first_name'] . ' ' . $userData['last_name']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Username:</strong></td>
                                                <td><?php echo htmlspecialchars($userData['username']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Email:</strong></td>
                                                <td><?php echo htmlspecialchars($userData['email']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Phone:</strong></td>
                                                <td><?php echo htmlspecialchars($userData['phone'] ?: 'Not provided'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Address:</strong></td>
                                                <td><?php echo htmlspecialchars($userData['address'] ?: 'Not provided'); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-cog"></i> Account Information</h6>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>User ID:</strong></td>
                                                <td><?php echo $userData['id']; ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Account Type:</strong></td>
                                                <td><?php echo ucfirst($userData['user_type']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Status:</strong></td>
                                                <td><?php echo ucfirst($userData['status']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Email Verified:</strong></td>
                                                <td><?php echo $userData['email_verified'] ? 'Yes' : 'No'; ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Registered:</strong></td>
                                                <td><?php echo formatDate($userData['created_at']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Last Login:</strong></td>
                                                <td><?php echo $userData['last_login'] ? formatDate($userData['last_login']) : 'Never'; ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Activity Tab -->
                            <div class="tab-pane fade" id="activity" role="tabpanel">
                                <div class="row">
                                    <?php if (!empty($recentDonations)): ?>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-heart"></i> Recent Donations</h6>
                                        <div class="list-group">
                                            <?php foreach ($recentDonations as $donation): ?>
                                            <div class="list-group-item">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($donation['blood_type']); ?> - <?php echo $donation['units_donated']; ?> unit(s)</h6>
                                                    <small><?php echo formatDate($donation['donation_date']); ?></small>
                                                </div>
                                                <p class="mb-1">Location: <?php echo htmlspecialchars($donation['location']); ?></p>
                                                <small>Status: <span class="badge bg-<?php echo $donation['status'] === 'completed' ? 'success' : 'warning'; ?>"><?php echo ucfirst($donation['status']); ?></span></small>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if (!empty($recentRequests)): ?>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-hand-holding-medical"></i> Recent Requests</h6>
                                        <div class="list-group">
                                            <?php foreach ($recentRequests as $request): ?>
                                            <div class="list-group-item">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($request['blood_type']); ?> - <?php echo $request['units_needed']; ?> unit(s)</h6>
                                                    <small><?php echo formatDate($request['created_at']); ?></small>
                                                </div>
                                                <p class="mb-1">Urgency: <span class="badge bg-<?php echo $request['urgency_level'] === 'critical' ? 'danger' : ($request['urgency_level'] === 'high' ? 'warning' : 'info'); ?>"><?php echo ucfirst($request['urgency_level']); ?></span></p>
                                                <small>Status: <span class="badge bg-<?php echo $request['status'] === 'fulfilled' ? 'success' : 'warning'; ?>"><?php echo ucfirst($request['status']); ?></span></small>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if (empty($recentDonations) && empty($recentRequests)): ?>
                                    <div class="col-12 text-center text-muted py-4">
                                        <i class="fas fa-history fa-3x mb-3"></i>
                                        <p>No recent activity found</p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Form (Hidden) -->
    <form id="actionForm" method="POST" action="users.php" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
        <input type="hidden" name="action" id="actionType">
        <input type="hidden" name="user_id" id="actionUserId" value="<?php echo $userId; ?>">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function suspendUser(userId) {
            if (confirm('Are you sure you want to suspend this user?')) {
                document.getElementById('actionType').value = 'suspend';
                document.getElementById('actionForm').submit();
            }
        }

        function activateUser(userId) {
            if (confirm('Are you sure you want to activate this user?')) {
                document.getElementById('actionType').value = 'activate';
                document.getElementById('actionForm').submit();
            }
        }

        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                document.getElementById('actionType').value = 'delete';
                document.getElementById('actionForm').submit();
            }
        }
    </script>
</body>
</html>
