<?php
/**
 * Email Configuration
 * Blood Donation Management System
 */

// Email configuration constants - Configure these for production
define('SMTP_HOST', ''); // Configure your SMTP host
define('SMTP_PORT', 587);
define('SMTP_USERNAME', ''); // Configure your SMTP username
define('SMTP_PASSWORD', ''); // Configure your SMTP password
define('SMTP_ENCRYPTION', 'tls');

define('FROM_EMAIL', ''); // Configure your from email
define('FROM_NAME', 'Blood Donation System');
define('REPLY_TO_EMAIL', ''); // Configure your reply-to email

// Email templates directory
define('EMAIL_TEMPLATES_DIR', __DIR__ . '/../templates/email/');

/**
 * Email Service Class
 */
class EmailService {
    private $mailer;
    
    public function __construct() {
        // Try to load PHPMailer from Composer autoload or manual include
        if (!class_exists('\\PHPMailer\\PHPMailer\\PHPMailer')) {
            // Composer autoload
            $autoload = __DIR__ . '/../vendor/autoload.php';
            if (file_exists($autoload)) {
                require_once $autoload;
            }
        }

        if (!class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
            // Manual include fallback
            $phpsrc = __DIR__ . '/../vendor/phpmailer/src/PHPMailer.php';
            $smtpsrc = __DIR__ . '/../vendor/phpmailer/src/SMTP.php';
            $exsrc  = __DIR__ . '/../vendor/phpmailer/src/Exception.php';
            if (file_exists($phpsrc) && file_exists($smtpsrc) && file_exists($exsrc)) {
                require_once $phpsrc;
                require_once $smtpsrc;
                require_once $exsrc;
            }
        }

        if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
            $this->mailer = new \PHPMailer\PHPMailer\PHPMailer(true);
            $this->configureSMTP();
        } else {
            // Library not available – operate in silent mode
            $this->mailer = null;
            error_log('PHPMailer library not found. Emails will not be sent.');
        }
    }
    
    /**
     * Configure SMTP settings
     */
    private function configureSMTP() {
        try {
            $this->mailer->isSMTP();
            $this->mailer->Host = SMTP_HOST;
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = SMTP_USERNAME;
            $this->mailer->Password = SMTP_PASSWORD;
            $this->mailer->SMTPSecure = SMTP_ENCRYPTION;
            $this->mailer->Port = SMTP_PORT;
            
            $this->mailer->setFrom(FROM_EMAIL, FROM_NAME);
            $this->mailer->addReplyTo(REPLY_TO_EMAIL, FROM_NAME);
            
            $this->mailer->isHTML(true);
            $this->mailer->CharSet = 'UTF-8';
        } catch (Exception $e) {
            error_log("Email configuration failed: " . $e->getMessage());
        }
    }
    
    /**
     * Send email
     */
    public function sendEmail($to, $subject, $body, $altBody = '') {
        // If mailer is disabled just log and pretend success to avoid breaking user flow
        if ($this->mailer === null) {
            error_log("Email suppressed (PHPMailer not available): To=$to Subject=$subject");
            return true;
        }
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($to);
            
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            $this->mailer->AltBody = $altBody ?: strip_tags($body);
            
            return $this->mailer->send();
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send registration confirmation email
     */
    public function sendRegistrationConfirmation($email, $name, $userType) {
        $subject = "Welcome to Blood Donation System";
        $template = $this->loadTemplate('registration_confirmation', [
            'name' => $name,
            'user_type' => ucfirst($userType),
            'login_url' => $this->getBaseUrl() . '/login.php'
        ]);
        
        return $this->sendEmail($email, $subject, $template);
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordReset($username, $name, $token) {
        $subject = "Password Reset Request";
        $resetUrl = $this->getBaseUrl() . '/reset-password.php?token=' . $token;
        
        $template = $this->loadTemplate('password_reset', [
            'name' => $name,
            'username' => $username,
            'reset_url' => $resetUrl,
            'expiry_time' => '1 hour'
        ]);
        
        // Since we're removing email functionality, we'll just log the reset request
        // In a real implementation, you might want to send SMS or use another notification method
        logEvent('INFO', 'Password reset requested', [
            'username' => $username,
            'name' => $name,
            'token' => $token,
            'reset_url' => $resetUrl
        ]);
        
        return true; // Return true to indicate "success" even though no email is sent
    }
    
    /**
     * Send blood request notification
     */
    public function sendBloodRequestNotification($email, $name, $bloodType, $urgency) {
        $subject = "Urgent Blood Request - " . $bloodType;
        $template = $this->loadTemplate('blood_request_notification', [
            'name' => $name,
            'blood_type' => $bloodType,
            'urgency' => $urgency,
            'dashboard_url' => $this->getBaseUrl() . '/donor/'
        ]);
        
        return $this->sendEmail($email, $subject, $template);
    }
    
    /**
     * Load email template
     */
    private function loadTemplate($templateName, $variables = []) {
        $templateFile = EMAIL_TEMPLATES_DIR . $templateName . '.html';
        
        if (!file_exists($templateFile)) {
            return $this->getDefaultTemplate($templateName, $variables);
        }
        
        $template = file_get_contents($templateFile);
        
        // Replace variables in template
        foreach ($variables as $key => $value) {
            $template = str_replace('{{' . $key . '}}', $value, $template);
        }
        
        return $template;
    }
    
    /**
     * Get default email template if file doesn't exist
     */
    private function getDefaultTemplate($templateName, $variables) {
        $baseTemplate = '
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #d32f2f;">Blood Donation System</h2>
                {{content}}
                <hr style="margin: 20px 0;">
                <p style="font-size: 12px; color: #666;">
                    This is an automated message. Please do not reply to this email.
                </p>
            </div>
        </body>
        </html>';
        
        $content = '';
        switch ($templateName) {
            case 'registration_confirmation':
                $content = '<p>Dear ' . $variables['name'] . ',</p>
                           <p>Welcome to the Blood Donation System! Your ' . $variables['user_type'] . ' account has been created successfully.</p>
                           <p><a href="' . $variables['login_url'] . '" style="background: #d32f2f; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login Now</a></p>';
                break;
            case 'password_reset':
                $content = '<p>Dear ' . $variables['name'] . ',</p>
                           <p>You requested a password reset for username: <strong>' . $variables['username'] . '</strong></p>
                           <p>Click the link below to reset your password:</p>
                           <p><a href="' . $variables['reset_url'] . '" style="background: #d32f2f; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
                           <p>This link will expire in ' . $variables['expiry_time'] . '.</p>
                           <p><strong>Note:</strong> Since email functionality has been removed, please check the system logs or contact an administrator to complete the password reset process.</p>';
                break;
            case 'blood_request_notification':
                $content = '<p>Dear ' . $variables['name'] . ',</p>
                           <p>There is an urgent blood request for ' . $variables['blood_type'] . ' blood type.</p>
                           <p>Urgency Level: <strong>' . strtoupper($variables['urgency']) . '</strong></p>
                           <p><a href="' . $variables['dashboard_url'] . '" style="background: #d32f2f; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Details</a></p>';
                break;
        }
        
        return str_replace('{{content}}', $content, $baseTemplate);
    }
    
    /**
     * Get base URL
     */
    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $path = dirname($_SERVER['SCRIPT_NAME'] ?? '');
        return $protocol . '://' . $host . $path;
    }
}
?>
