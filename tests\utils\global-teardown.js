const DatabaseHelper = require('./database-helper');

async function globalTeardown() {
  console.log('🧹 Starting global test teardown...');
  
  const dbHelper = new DatabaseHelper();
  
  try {
    // Clean up test data if enabled
    if (process.env.TEST_CLEANUP_ENABLED !== 'false') {
      await dbHelper.connect();
      await dbHelper.cleanupTestUsers();
      console.log('✅ Test data cleaned up');
    } else {
      console.log('⚠️ Test cleanup disabled - test data preserved');
    }
    
    console.log('🎉 Global test teardown completed');
    
  } catch (error) {
    console.error('❌ Global test teardown failed:', error.message);
    // Don't throw error in teardown to avoid masking test failures
  } finally {
    await dbHelper.disconnect();
  }
}

module.exports = globalTeardown;
