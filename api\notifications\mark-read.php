<?php
/**
 * Mark Notification as Read API Endpoint
 * Blood Donation Management System
 */

require_once '../../config/constants.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';
require_once '../../classes/Notification.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session and check authentication
startSecureSession();

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Authentication required'
    ]);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit;
}

$currentUser = getCurrentUser();

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate input
    if (!isset($input['notification_id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Notification ID is required'
        ]);
        exit;
    }
    
    $notificationId = (int)$input['notification_id'];
    
    if ($notificationId <= 0) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid notification ID'
        ]);
        exit;
    }
    
    // Mark notification as read
    $success = Notification::markAsRead($currentUser['id'], $notificationId);
    
    if ($success) {
        // Get updated unread count
        $unreadCount = Notification::getUnreadCount($currentUser['id']);
        
        echo json_encode([
            'success' => true,
            'unread_count' => $unreadCount,
            'message' => 'Notification marked as read'
        ]);
        
        // Log the action
        logEvent('INFO', 'Notification marked as read', [
            'user_id' => $currentUser['id'],
            'notification_id' => $notificationId
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to mark notification as read'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error'
    ]);
    
    // Log the error
    logEvent('ERROR', 'Mark notification as read API error', [
        'user_id' => $currentUser['id'],
        'error' => $e->getMessage()
    ]);
}
?>
