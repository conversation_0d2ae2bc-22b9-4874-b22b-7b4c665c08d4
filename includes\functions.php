<?php
/**
 * Utility Functions
 * Blood Donation Management System
 */

require_once __DIR__ . '/../config/constants.php';

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate password strength
 */
function validatePassword($password) {
    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        return false;
    }
    
    // Check for at least one uppercase, one lowercase, one number
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/', $password)) {
        return false;
    }
    
    return true;
}

/**
 * Generate secure random token
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token']) || 
        !isset($_SESSION['csrf_token_time']) || 
        (time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_EXPIRE) {
        
        $_SESSION['csrf_token'] = generateToken();
        $_SESSION['csrf_token_time'] = time();
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    if (!isset($_SESSION['csrf_token']) || 
        !isset($_SESSION['csrf_token_time']) ||
        (time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_EXPIRE) {
        return false;
    }
    
    return hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Format date for display
 */
function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
    if (empty($date)) return '';
    
    $dateObj = DateTime::createFromFormat(DATETIME_FORMAT, $date);
    if (!$dateObj) {
        $dateObj = DateTime::createFromFormat(DATE_FORMAT, $date);
    }
    
    return $dateObj ? $dateObj->format($format) : '';
}

/**
 * Format date and time for display (for chat messages)
 */
function formatDateTime($dateTime, $format = 'M j, Y g:i A') {
    if (empty($dateTime)) return '';

    try {
        $dateObj = new DateTime($dateTime);
        return $dateObj->format($format);
    } catch (Exception $e) {
        return $dateTime; // Return original if formatting fails
    }
}

/**
 * Calculate age from birth date
 */
function calculateAge($birthDate) {
    $birth = new DateTime($birthDate);
    $today = new DateTime();
    return $birth->diff($today)->y;
}

/**
 * Check if donor is eligible for donation
 */
function isDonorEligible($lastDonationDate, $weight, $age) {
    // Check age
    if ($age < MIN_DONOR_AGE || $age > MAX_DONOR_AGE) {
        return false;
    }
    
    // Check weight
    if ($weight < MIN_DONOR_WEIGHT) {
        return false;
    }
    
    // Check last donation date
    if (!empty($lastDonationDate)) {
        $lastDonation = new DateTime($lastDonationDate);
        $today = new DateTime();
        $daysSinceLastDonation = $lastDonation->diff($today)->days;
        
        if ($daysSinceLastDonation < MIN_DONATION_INTERVAL) {
            return false;
        }
    }
    
    return true;
}

/**
 * Get compatible blood types for recipient
 */
function getCompatibleBloodTypes($recipientBloodType) {
    $compatible = [];
    
    foreach (BLOOD_COMPATIBILITY as $donorType => $canDonateTo) {
        if (in_array($recipientBloodType, $canDonateTo)) {
            $compatible[] = $donorType;
        }
    }
    
    return $compatible;
}

/**
 * Log system events
 */
function logEvent($level, $message, $context = []) {
    $logFile = LOG_DIR . 'system_' . date('Y-m-d') . '.log';
    $timestamp = date(DATETIME_FORMAT);
    $contextStr = !empty($context) ? json_encode($context) : '';
    
    $logEntry = "[{$timestamp}] {$level}: {$message}";
    if ($contextStr) {
        $logEntry .= " Context: {$contextStr}";
    }
    $logEntry .= PHP_EOL;
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Send JSON response
 */
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: $url");
    exit;
}

/**
 * Get flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
        
        return ['message' => $message, 'type' => $type];
    }
    
    return null;
}

/**
 * Upload file
 */
function uploadFile($file, $allowedTypes, $uploadDir = null) {
    if (!$uploadDir) {
        $uploadDir = UPLOAD_DIR;
    }
    
    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error: ' . $file['error']);
    }
    
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new Exception(ERROR_MESSAGES['FILE_TOO_LARGE']);
    }
    
    // Check file type
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, $allowedTypes)) {
        throw new Exception(ERROR_MESSAGES['INVALID_FILE_TYPE']);
    }
    
    // Generate unique filename
    $fileName = generateToken(16) . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        throw new Exception(ERROR_MESSAGES['UPLOAD_FAILED']);
    }
    
    return $fileName;
}

/**
 * Delete file
 */
function deleteFile($fileName, $uploadDir = null) {
    if (!$uploadDir) {
        $uploadDir = UPLOAD_DIR;
    }
    
    $filePath = $uploadDir . $fileName;
    if (file_exists($filePath)) {
        return unlink($filePath);
    }
    
    return false;
}

/**
 * Paginate results
 */
function paginate($totalRecords, $currentPage = 1, $recordsPerPage = RECORDS_PER_PAGE) {
    $totalPages = ceil($totalRecords / $recordsPerPage);
    $currentPage = max(1, min($currentPage, $totalPages));
    $offset = ($currentPage - 1) * $recordsPerPage;
    
    return [
        'total_records' => $totalRecords,
        'total_pages' => $totalPages,
        'current_page' => $currentPage,
        'records_per_page' => $recordsPerPage,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages,
        'previous_page' => $currentPage > 1 ? $currentPage - 1 : null,
        'next_page' => $currentPage < $totalPages ? $currentPage + 1 : null
    ];
}

/**
 * Generate pagination HTML
 */
function generatePaginationHTML($pagination, $baseUrl) {
    if ($pagination['total_pages'] <= 1) {
        return '';
    }
    
    $html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';
    
    // Previous button
    if ($pagination['has_previous']) {
        $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . $pagination['previous_page'] . '">Previous</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><span class="page-link">Previous</span></li>';
    }
    
    // Page numbers
    $startPage = max(1, $pagination['current_page'] - floor(MAX_PAGINATION_LINKS / 2));
    $endPage = min($pagination['total_pages'], $startPage + MAX_PAGINATION_LINKS - 1);
    
    for ($i = $startPage; $i <= $endPage; $i++) {
        if ($i == $pagination['current_page']) {
            $html .= '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
        } else {
            $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . $i . '">' . $i . '</a></li>';
        }
    }
    
    // Next button
    if ($pagination['has_next']) {
        $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . $pagination['next_page'] . '">Next</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><span class="page-link">Next</span></li>';
    }
    
    $html .= '</ul></nav>';
    
    return $html;
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    // Consider user as logged in if the session key exists, even if the value is 0 (admin)
    $loggedIn = isset($_SESSION['user_id']);

    // Update online status if user is logged in
    if ($loggedIn && $_SESSION['user_id'] > 0) {
        updateUserOnlineStatus($_SESSION['user_id']);
    }

    return $loggedIn;
}

/**
 * Get current user
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'username' => $_SESSION['username'] ?? '',
        'email' => $_SESSION['email'] ?? '',
        'user_type' => $_SESSION['user_type'] ?? '',
        'first_name' => $_SESSION['first_name'] ?? '',
        'last_name' => $_SESSION['last_name'] ?? ''
    ];
}

/**
 * Check user permission (Updated for unified system)
 */
function hasPermission($requiredType) {
    $user = getCurrentUser();
    if (!$user) {
        return false;
    }

    if ($user['user_type'] === USER_TYPE_ADMIN) {
        return true; // Admin has all permissions
    }

    // For unified users, check their roles
    if ($user['user_type'] === 'unified') {
        try {
            $db = Database::getInstance();

            // Get user roles
            $roles = $db->fetchAll("SELECT role_type FROM user_roles WHERE user_id = ? AND is_active = TRUE", [$user['id']]);
            $userRoles = array_column($roles, 'role_type');

            // Check if user has the required role
            return in_array($requiredType, $userRoles);
        } catch (Exception $e) {
            // Log error and deny permission if database query fails
            error_log("Permission check failed for user {$user['id']}: " . $e->getMessage());
            return false;
        }
    }

    // Legacy user types
    return $user['user_type'] === $requiredType;
}

/**
 * Require login
 */
function requireLogin($redirectUrl = '/login.php') {
    if (!isLoggedIn()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Check if unified user has specific role
 */
function hasRole($userId, $roleType) {
    $db = Database::getInstance();

    $role = $db->fetch("SELECT id FROM user_roles WHERE user_id = ? AND role_type = ? AND is_active = TRUE",
        [$userId, $roleType]);

    return !empty($role);
}

/**
 * Get user's active roles
 */
function getUserRoles($userId) {
    $db = Database::getInstance();

    $roles = $db->fetchAll("SELECT role_type FROM user_roles WHERE user_id = ? AND is_active = TRUE", [$userId]);

    return array_column($roles, 'role_type');
}



/**
 * Require permission (Updated for unified system)
 */
function requirePermission($requiredType, $redirectUrl = '/login.php') {
    if (!isLoggedIn()) {
        header("Location: $redirectUrl");
        exit;
    }

    if (!hasPermission($requiredType)) {
        redirectWithMessage($redirectUrl, ERROR_MESSAGES['ACCESS_DENIED'], 'error');
    }
}

/**
 * Require unified user access (for dashboard and unified areas)
 */
function requireUnifiedAccess($redirectUrl = '/login.php') {
    if (!isLoggedIn()) {
        header("Location: $redirectUrl");
        exit;
    }

    $user = getCurrentUser();

    // Admin always has access
    if ($user['user_type'] === USER_TYPE_ADMIN) {
        return;
    }

    // Unified users have access
    if ($user['user_type'] === 'unified') {
        return;
    }

    // Legacy users should be redirected to upgrade
    redirectWithMessage($redirectUrl, 'Please upgrade your account to access the unified dashboard.', 'info');
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Get time ago string
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < ********) return floor($time/2592000) . ' months ago';

    return floor($time/********) . ' years ago';
}

/**
 * Chat System Functions
 */

/**
 * Check if user can chat with another user (Restricted messaging system)
 *
 * Rules:
 * - Regular users (donors/recipients) can ONLY message administrators
 * - Regular users cannot see, contact, or message other regular users
 * - Administrators can message and reply to ALL users
 */
function canChatWith($userId, $targetUserId) {
    if ($userId == $targetUserId) {
        return false;
    }

    $db = Database::getInstance();

    // Get both users with their roles
    $sql = "SELECT u.id, u.user_type, u.status, u.is_unified_user,
                   GROUP_CONCAT(ur.role_type) as roles
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = TRUE
            WHERE u.id IN (?, ?) AND u.status = ?
            GROUP BY u.id, u.user_type, u.status, u.is_unified_user";

    $users = $db->fetchAll($sql, [$userId, $targetUserId, USER_STATUS_ACTIVE]);

    if (count($users) !== 2) {
        return false;
    }

    // Identify the users
    $user1 = null;
    $user2 = null;

    foreach ($users as $user) {
        if ($user['id'] == $userId) {
            $user1 = $user;
        } else {
            $user2 = $user;
        }
    }

    // RESTRICTED MESSAGING RULES:

    // Rule 1: Administrators can message anyone
    if ($user1['user_type'] === USER_TYPE_ADMIN) {
        return true;
    }

    // Rule 2: Regular users can ONLY message administrators
    if ($user1['user_type'] !== USER_TYPE_ADMIN && $user2['user_type'] === USER_TYPE_ADMIN) {
        return true;
    }

    // Rule 3: Regular users CANNOT message other regular users
    if ($user1['user_type'] !== USER_TYPE_ADMIN && $user2['user_type'] !== USER_TYPE_ADMIN) {
        return false;
    }

    // Default: deny access
    return false;
}

/**
 * Get list of users that the current user can message (Restricted messaging system)
 *
 * Rules:
 * - Regular users can only see and message administrators
 * - Administrators can see and message all users
 */
function getMessagableUsers($userId, $search = '', $limit = 50, $offset = 0) {
    $db = Database::getInstance();

    // Get current user info
    $currentUser = $db->fetch("SELECT user_type, is_unified_user FROM users WHERE id = ?", [$userId]);

    if (!$currentUser) {
        return [];
    }

    $whereConditions = ["u.status = 'active'", "u.id != ?"];
    $params = [$userId];

    // Apply messaging restrictions
    if ($currentUser['user_type'] === USER_TYPE_ADMIN) {
        // Administrators can see all users - no additional restrictions
    } else {
        // Regular users can ONLY see administrators
        $whereConditions[] = "u.user_type = ?";
        $params[] = USER_TYPE_ADMIN;
    }

    // Add search filter if provided
    if (!empty($search)) {
        $whereConditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.username LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }

    $whereClause = implode(' AND ', $whereConditions);

    // Get users
    $sql = "SELECT u.id, u.username, u.first_name, u.last_name, u.user_type, u.profile_photo,
                   u.is_unified_user, u.is_online, u.last_activity, u.last_seen,
                   GROUP_CONCAT(DISTINCT ur.role_type) as roles,
                   u.created_at
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = TRUE
            WHERE $whereClause
            GROUP BY u.id, u.username, u.first_name, u.last_name, u.user_type, u.profile_photo,
                     u.is_unified_user, u.is_online, u.last_activity, u.last_seen, u.created_at
            ORDER BY u.user_type = 'admin' DESC, u.is_online DESC, u.first_name, u.last_name
            LIMIT ? OFFSET ?";

    $params[] = $limit;
    $params[] = $offset;

    return $db->fetchAll($sql, $params);
}

/**
 * Get count of users that the current user can message
 */
function getMessagableUsersCount($userId, $search = '') {
    $db = Database::getInstance();

    // Get current user info
    $currentUser = $db->fetch("SELECT user_type FROM users WHERE id = ?", [$userId]);

    if (!$currentUser) {
        return 0;
    }

    $whereConditions = ["u.status = 'active'", "u.id != ?"];
    $params = [$userId];

    // Apply messaging restrictions
    if ($currentUser['user_type'] === USER_TYPE_ADMIN) {
        // Administrators can see all users - no additional restrictions
    } else {
        // Regular users can ONLY see administrators
        $whereConditions[] = "u.user_type = ?";
        $params[] = USER_TYPE_ADMIN;
    }

    // Add search filter if provided
    if (!empty($search)) {
        $whereConditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.username LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }

    $whereClause = implode(' AND ', $whereConditions);

    $sql = "SELECT COUNT(DISTINCT u.id) as total FROM users u WHERE $whereClause";
    $result = $db->fetch($sql, $params);

    return $result['total'];
}

/**
 * Get chat messages between two users
 */
function getChatMessages($userId, $otherUserId, $limit = 50) {
    $db = Database::getInstance();

    $sql = "SELECT cm.*,
                   s.first_name as sender_first_name, s.last_name as sender_last_name,
                   r.first_name as receiver_first_name, r.last_name as receiver_last_name
            FROM chat_messages cm
            JOIN users s ON cm.sender_id = s.id
            JOIN users r ON cm.receiver_id = r.id
            WHERE (cm.sender_id = ? AND cm.receiver_id = ?)
               OR (cm.sender_id = ? AND cm.receiver_id = ?)
            ORDER BY cm.sent_at ASC
            LIMIT ?";

    return $db->fetchAll($sql, [$userId, $otherUserId, $otherUserId, $userId, $limit]);
}

/**
 * Get user's chat conversations (Restricted messaging system)
 * Only shows conversations that the user is allowed to have based on messaging restrictions
 */
function getUserConversations($userId) {
    $db = Database::getInstance();

    // Get current user info to apply restrictions
    $currentUser = $db->fetch("SELECT user_type FROM users WHERE id = ?", [$userId]);

    if (!$currentUser) {
        return [];
    }

    $sql = "SELECT DISTINCT
                CASE
                    WHEN cm.sender_id = ? THEN cm.receiver_id
                    ELSE cm.sender_id
                END as user_id,
                u.first_name, u.last_name, u.user_type, u.profile_photo, u.is_unified_user,
                u.is_online, u.last_activity, u.last_seen,
                GROUP_CONCAT(DISTINCT ur.role_type) as roles,
                (SELECT message FROM chat_messages cm2
                 WHERE (cm2.sender_id = ? AND cm2.receiver_id = user_id)
                    OR (cm2.sender_id = user_id AND cm2.receiver_id = ?)
                 ORDER BY cm2.sent_at DESC LIMIT 1) as last_message,
                (SELECT sent_at FROM chat_messages cm3
                 WHERE (cm3.sender_id = ? AND cm3.receiver_id = user_id)
                    OR (cm3.sender_id = user_id AND cm3.receiver_id = ?)
                 ORDER BY cm3.sent_at DESC LIMIT 1) as last_message_time,
                (SELECT COUNT(*) FROM chat_messages cm4
                 WHERE cm4.sender_id = user_id AND cm4.receiver_id = ?
                   AND cm4.is_read = 0) as unread_count
            FROM chat_messages cm
            JOIN users u ON u.id = CASE
                WHEN cm.sender_id = ? THEN cm.receiver_id
                ELSE cm.sender_id
            END
            LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = TRUE
            WHERE (cm.sender_id = ? OR cm.receiver_id = ?)
              AND u.status = 'active'";

    // Apply messaging restrictions to conversations
    if ($currentUser['user_type'] !== USER_TYPE_ADMIN) {
        // Regular users can only see conversations with administrators
        $sql .= " AND u.user_type = '" . USER_TYPE_ADMIN . "'";
    }

    $sql .= " GROUP BY user_id, u.first_name, u.last_name, u.user_type, u.profile_photo, u.is_unified_user, u.is_online, u.last_activity, u.last_seen
              ORDER BY last_message_time DESC";

    return $db->fetchAll($sql, [
        $userId, $userId, $userId, $userId, $userId, $userId, $userId, $userId, $userId
    ]);
}

/**
 * Mark messages as read
 */
function markMessagesAsRead($userId, $senderId) {
    $db = Database::getInstance();

    $sql = "UPDATE chat_messages
            SET is_read = 1, read_at = NOW()
            WHERE receiver_id = ? AND sender_id = ? AND is_read = 0";

    return $db->execute($sql, [$userId, $senderId]);
}

/**
 * Get unread message count for user (Restricted messaging system)
 * Only counts messages from users that the current user is allowed to chat with
 */
function getUnreadMessageCount($userId) {
    $db = Database::getInstance();

    // Get current user info to apply restrictions
    $currentUser = $db->fetch("SELECT user_type FROM users WHERE id = ?", [$userId]);

    if (!$currentUser) {
        return 0;
    }

    $sql = "SELECT COUNT(*) as count
            FROM chat_messages cm
            JOIN users u ON cm.sender_id = u.id
            WHERE cm.receiver_id = ? AND cm.is_read = 0 AND u.status = 'active'";

    $params = [$userId];

    // Apply messaging restrictions
    if ($currentUser['user_type'] !== USER_TYPE_ADMIN) {
        // Regular users can only receive messages from administrators
        $sql .= " AND u.user_type = ?";
        $params[] = USER_TYPE_ADMIN;
    }

    $result = $db->fetch($sql, $params);
    return $result['count'];
}

/**
 * Update user online status
 */
function updateUserOnlineStatus($userId) {
    try {
        $db = Database::getInstance();

        // Update user's online status and last activity
        $db->execute("UPDATE users SET is_online = TRUE, last_activity = CURRENT_TIMESTAMP WHERE id = ?", [$userId]);

        // Update or create session record
        $sessionId = session_id();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // Check if session exists
        $existingSession = $db->fetch("SELECT id FROM user_sessions WHERE session_id = ?", [$sessionId]);

        if ($existingSession) {
            // Update existing session
            $db->execute("UPDATE user_sessions SET last_activity = CURRENT_TIMESTAMP, is_active = TRUE WHERE session_id = ?", [$sessionId]);
        } else {
            // Create new session record
            $db->execute("INSERT INTO user_sessions (user_id, session_id, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?, DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 24 HOUR))",
                [$userId, $sessionId, $ipAddress, $userAgent]);
        }

        // Clean up old offline users (haven't been active for 5 minutes)
        $db->execute("UPDATE users SET is_online = FALSE, last_seen = last_activity WHERE last_activity < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 5 MINUTE) AND is_online = TRUE");

        return true;
    } catch (Exception $e) {
        error_log("Error updating online status: " . $e->getMessage());
        return false;
    }
}

/**
 * Set user offline
 */
function setUserOffline($userId) {
    try {
        $db = Database::getInstance();

        // Set user offline
        $db->execute("UPDATE users SET is_online = FALSE, last_seen = CURRENT_TIMESTAMP WHERE id = ?", [$userId]);

        // Deactivate user sessions
        $db->execute("UPDATE user_sessions SET is_active = FALSE WHERE user_id = ? AND is_active = TRUE", [$userId]);

        return true;
    } catch (Exception $e) {
        error_log("Error setting user offline: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if user is active
 */
function isUserActive($userId) {
    $db = Database::getInstance();

    $result = $db->fetch("SELECT is_online, last_activity FROM users WHERE id = ?", [$userId]);

    if (!$result) {
        return false;
    }

    // Check if user is marked online and last activity was within 5 minutes
    if ($result['is_online'] && $result['last_activity']) {
        $lastActivity = new DateTime($result['last_activity']);
        $now = new DateTime();
        $diff = $now->diff($lastActivity);

        // If last activity was more than 5 minutes ago, consider inactive
        if ($diff->i >= 5 || $diff->h > 0 || $diff->d > 0) {
            // Update user to offline
            setUserOffline($userId);
            return false;
        }

        return true;
    }

    return false;
}

/**
 * Get user's status (Active/Inactive)
 */
function getUserStatus($userId) {
    $db = Database::getInstance();

    $result = $db->fetch("SELECT is_online, last_activity FROM users WHERE id = ?", [$userId]);

    if (!$result) {
        return 'Inactive';
    }

    if ($result['is_online'] && $result['last_activity']) {
        $lastActivity = new DateTime($result['last_activity']);
        $now = new DateTime();
        $diff = $now->diff($lastActivity);

        // If last activity was within 5 minutes, consider active
        if ($diff->i < 5 && $diff->h == 0 && $diff->d == 0) {
            return 'Active';
        }
    }

    return 'Inactive';
}
?>
