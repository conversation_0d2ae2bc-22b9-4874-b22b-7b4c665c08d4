<?php
/**
 * Run Unified System Migration
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=blood_donation_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Read migration file
    $migrationFile = 'database/migrate_to_unified_system.sql';
    if (file_exists($migrationFile)) {
        $sql = file_get_contents($migrationFile);
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(--|\/\*|\s*$)/', $statement)) {
                try {
                    $pdo->exec($statement);
                    echo "Executed: " . substr($statement, 0, 50) . "...\n";
                } catch (PDOException $e) {
                    echo "Warning: " . $e->getMessage() . "\n";
                }
            }
        }
        echo "Migration completed successfully!\n";
    } else {
        echo "Migration file not found: $migrationFile\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?> 