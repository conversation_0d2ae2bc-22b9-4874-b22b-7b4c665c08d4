-- Audit and Security Logging Tables
-- Blood Donation Management System

-- Create admin audit logs table
CREATE TABLE IF NOT EXISTS admin_audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    details <PERSON><PERSON><PERSON> NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
);

-- Create security logs table
CREATE TABLE IF NOT EXISTS security_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    event VARCHAR(100) NOT NULL,
    severity ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
    details <PERSON><PERSON><PERSON> NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_event (event),
    INDEX idx_severity (severity),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
);

-- Create system settings table if not exists
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key)
);

-- Insert default security settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('session_timeout', '3600', 'Session timeout in seconds'),
('max_login_attempts', '5', 'Maximum login attempts before lockout'),
('lockout_duration', '900', 'Account lockout duration in seconds'),
('require_email_verification', '0', 'Require email verification for new accounts'),
('enable_audit_logging', '1', 'Enable audit logging for admin actions'),
('password_min_length', '8', 'Minimum password length'),
('password_require_uppercase', '1', 'Require uppercase letters in passwords'),
('password_require_lowercase', '1', 'Require lowercase letters in passwords'),
('password_require_numbers', '1', 'Require numbers in passwords'),
('password_require_symbols', '0', 'Require symbols in passwords'),
('maintenance_mode', '0', 'System maintenance mode'),
('auto_approve_donations', '0', 'Auto-approve eligible donations'),
('send_donation_reminders', '1', 'Send donation reminder emails'),
('min_donation_interval', '56', 'Minimum days between donations'),
('min_donor_weight', '50', 'Minimum donor weight in kg'),
('min_donor_age', '18', 'Minimum donor age'),
('max_donor_age', '65', 'Maximum donor age')
ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- Create admin permissions table
CREATE TABLE IF NOT EXISTS admin_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    permission VARCHAR(50) NOT NULL,
    granted_by INT NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_permission (user_id, permission),
    INDEX idx_user_id (user_id),
    INDEX idx_permission (permission),
    INDEX idx_active (is_active)
);

-- Insert default admin permissions for existing admin users
INSERT INTO admin_permissions (user_id, permission, granted_by, granted_at)
SELECT 
    u.id,
    'FULL_ACCESS',
    u.id,
    NOW()
FROM users u 
WHERE u.user_type = 'admin' 
AND NOT EXISTS (
    SELECT 1 FROM admin_permissions ap 
    WHERE ap.user_id = u.id AND ap.permission = 'FULL_ACCESS'
);

-- Create login attempts tracking table
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    identifier VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    success BOOLEAN DEFAULT FALSE,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_identifier (identifier),
    INDEX idx_ip_address (ip_address),
    INDEX idx_attempted_at (attempted_at),
    INDEX idx_success (success)
);

-- Create password reset tokens table if not exists
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    ip_address VARCHAR(45) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);

-- Create session management table
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity),
    INDEX idx_expires_at (expires_at),
    INDEX idx_active (is_active)
);

-- Add triggers for audit logging
DELIMITER $$

-- Trigger for user updates
CREATE TRIGGER IF NOT EXISTS audit_user_updates
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status OR OLD.user_type != NEW.user_type THEN
        INSERT INTO admin_audit_logs (user_id, action, details, ip_address, user_agent)
        VALUES (
            NEW.id,
            'USER_UPDATED',
            JSON_OBJECT(
                'old_status', OLD.status,
                'new_status', NEW.status,
                'old_user_type', OLD.user_type,
                'new_user_type', NEW.user_type,
                'updated_by', @current_admin_id
            ),
            @current_ip_address,
            @current_user_agent
        );
    END IF;
END$$

-- Trigger for user deletions
CREATE TRIGGER IF NOT EXISTS audit_user_deletions
BEFORE DELETE ON users
FOR EACH ROW
BEGIN
    INSERT INTO admin_audit_logs (user_id, action, details, ip_address, user_agent)
    VALUES (
        OLD.id,
        'USER_DELETED',
        JSON_OBJECT(
            'deleted_user_id', OLD.id,
            'deleted_username', OLD.username,
            'deleted_email', OLD.email,
            'deleted_by', @current_admin_id
        ),
        @current_ip_address,
        @current_user_agent
    );
END$$

DELIMITER ;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_composite ON admin_audit_logs (user_id, action, created_at);
CREATE INDEX IF NOT EXISTS idx_security_logs_composite ON security_logs (severity, event, created_at);
CREATE INDEX IF NOT EXISTS idx_login_attempts_composite ON login_attempts (identifier, attempted_at, success);

-- Create view for recent admin activities
CREATE OR REPLACE VIEW recent_admin_activities AS
SELECT 
    al.id,
    al.action,
    al.details,
    al.ip_address,
    al.created_at,
    u.first_name,
    u.last_name,
    u.username,
    u.email
FROM admin_audit_logs al
LEFT JOIN users u ON al.user_id = u.id
WHERE al.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY al.created_at DESC;

-- Create view for security dashboard
CREATE OR REPLACE VIEW security_dashboard AS
SELECT 
    (SELECT COUNT(*) FROM admin_audit_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as admin_actions_24h,
    (SELECT COUNT(*) FROM security_logs WHERE severity IN ('ERROR', 'CRITICAL') AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as security_incidents_24h,
    (SELECT COUNT(*) FROM login_attempts WHERE success = FALSE AND attempted_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as failed_logins_24h,
    (SELECT COUNT(DISTINCT ip_address) FROM login_attempts WHERE attempted_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as unique_ips_24h,
    (SELECT COUNT(*) FROM user_sessions WHERE is_active = TRUE) as active_sessions;
