{"name": "salin-role-system-tests", "version": "1.0.0", "description": "Playwright tests for SaLin unified role system", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "test:role-selection": "playwright test specs/02-role-selection.spec.js", "test:role-switching": "playwright test specs/03-role-switching.spec.js", "test:donor": "playwright test specs/04-donor-functionality.spec.js", "test:recipient": "playwright test specs/05-recipient-functionality.spec.js", "test:integration": "playwright test specs/06-integration-tests.spec.js", "test:bugs": "playwright test specs/07-bug-detection.spec.js"}, "keywords": ["playwright", "testing", "blood-donation", "role-system"], "author": "SaLin Development Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0", "mysql2": "^3.6.5", "dotenv": "^16.3.1"}, "dependencies": {"faker": "^6.6.6"}}