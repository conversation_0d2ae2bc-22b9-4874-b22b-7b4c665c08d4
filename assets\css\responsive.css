/**
 * Mobile-First Responsive Design
 * Blood Donation Management System
 * 
 * This file implements comprehensive responsive design following mobile-first principles
 * Base styles for mobile (320px+), then scale up for larger screens
 */

/* ==========================================================================
   MOBILE-FIRST RESPONSIVE DESIGN
   ========================================================================== */

/* Base Mobile Styles (320px - 767px) */
/* All styles here are the base for mobile devices */

/* Typography Responsive Scale */
html {
    font-size: 14px; /* Base font size for mobile */
}

@media (min-width: 768px) {
    html {
        font-size: 16px; /* Larger base font for tablets and up */
    }
}

@media (min-width: 1024px) {
    html {
        font-size: 18px; /* Largest base font for desktop */
    }
}

/* Responsive Typography */
h1 { font-size: 1.75rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

@media (min-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    h5 { font-size: 1.125rem; }
    h6 { font-size: 1rem; }
}

@media (min-width: 1024px) {
    h1 { font-size: 2.25rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.75rem; }
    h4 { font-size: 1.5rem; }
    h5 { font-size: 1.25rem; }
    h6 { font-size: 1.125rem; }
}

/* ==========================================================================
   ENHANCED MOBILE NAVIGATION
   ========================================================================== */

/* Mobile Navigation - Enhanced */
.navbar {
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-brand i {
    font-size: 1.5rem;
}

/* Enhanced Mobile Toggler */
.navbar-toggler {
    padding: 0.5rem 0.75rem;
    font-size: 1.25rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    z-index: 10000 !important;
    position: relative;
}

.navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.8);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
    outline: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Mobile Navigation Menu */
.navbar-collapse {
    transition: all 0.3s ease-in-out;
}

.navbar-nav {
    margin-top: 1rem;
    padding: 1rem 0;
    background: rgba(220, 53, 69, 0.95);
    border-radius: 0.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.navbar-nav .nav-item {
    margin-bottom: 0.5rem;
}

.navbar-nav .nav-link {
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    margin: 0.25rem 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9) !important;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border: 1px solid transparent;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #fff !important;
    transform: translateX(5px);
    border-color: rgba(255, 255, 255, 0.3);
}

.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: #fff !important;
    border-color: rgba(255, 255, 255, 0.5);
}

.navbar-nav .nav-link i {
    font-size: 1.1rem;
    width: 1.5rem;
    text-align: center;
}

/* Mobile Navigation Button Styles */
.navbar-nav .btn {
    margin: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.navbar-nav .btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: rgba(255, 255, 255, 0.9);
    background: transparent;
}

.navbar-nav .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    border-color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Mobile Navigation Animation */
.navbar-collapse.collapsing {
    transition: height 0.35s ease;
}

.navbar-collapse.show {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Navigation Overlay */
@media (max-width: 767.98px) {
    .navbar-collapse {
        position: fixed !important;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 9999 !important;
        background: rgba(220, 53, 69, 0.98);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        max-height: calc(100vh - 100%);
        overflow-y: auto;
    }

    .navbar-nav {
        margin-top: 0;
        padding: 1rem;
        background: transparent;
        box-shadow: none;
    }

    /* Ensure navbar itself has proper z-index */
    .navbar {
        z-index: 9998 !important;
        position: relative;
    }

    /* Create overlay backdrop when menu is open */
    .navbar-collapse.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
        backdrop-filter: blur(2px);
    }

    /* Ensure mobile navigation has highest priority */
    .navbar-collapse.show {
        z-index: 9999 !important;
    }

    /* Prevent other elements from interfering when menu is open */
    .navbar-collapse.show ~ * {
        pointer-events: none;
    }

    /* Re-enable pointer events for navigation items */
    .navbar-collapse.show .navbar-nav,
    .navbar-collapse.show .navbar-nav * {
        pointer-events: auto;
    }

    /* Fix for Bootstrap modal and other high z-index elements */
    .modal,
    .modal-backdrop,
    .tooltip,
    .popover {
        z-index: 9990 !important;
    }
}

/* ==========================================================================
   ENHANCED MOBILE NAVIGATION FEATURES
   ========================================================================== */

/* Scroll-based navbar styling */
.navbar-scrolled {
    background: rgba(220, 53, 69, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Navigation open state */
.navbar.navigation-open {
    background: rgba(220, 53, 69, 0.98) !important;
    backdrop-filter: blur(15px);
}

.navbar.navigation-open .navbar-toggler {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.8);
}

/* Enhanced focus states for accessibility */
.navbar-nav .nav-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
    border-radius: 0.5rem;
}

.navbar-toggler:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

/* Mobile navigation overlay improvements */
@media (max-width: 767.98px) {
    .navbar-collapse.show {
        animation: slideDownEnhanced 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    @keyframes slideDownEnhanced {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
    
    /* Improved mobile navigation backdrop */
    .navbar-collapse {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.98) 0%, rgba(185, 28, 28, 0.98) 100%);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    /* Enhanced mobile navigation items */
    .navbar-nav .nav-item {
        opacity: 0;
        transform: translateX(-30px);
        animation: slideInLeft 0.5s ease forwards;
    }
    
    .navbar-nav .nav-item:nth-child(1) { animation-delay: 0.1s; }
    .navbar-nav .nav-item:nth-child(2) { animation-delay: 0.2s; }
    .navbar-nav .nav-item:nth-child(3) { animation-delay: 0.3s; }
    .navbar-nav .nav-item:nth-child(4) { animation-delay: 0.4s; }
    .navbar-nav .nav-item:nth-child(5) { animation-delay: 0.5s; }
    
    @keyframes slideInLeft {
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    /* Mobile navigation button enhancements */
    .navbar-nav .btn {
        position: relative;
        overflow: hidden;
    }
    
    .navbar-nav .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    
    .navbar-nav .btn:hover::before {
        left: 100%;
    }
}

/* ==========================================================================
   TABLET NAVIGATION (768px - 1023px)
   ========================================================================== */

@media (min-width: 768px) {
    .navbar {
        padding: 1rem 2rem;
    }
    
    .navbar-brand {
        font-size: 1.5rem;
    }
    
    .navbar-brand i {
        font-size: 1.75rem;
    }
    
    .navbar-nav {
        margin-top: 0;
        padding: 0;
        background: transparent;
        box-shadow: none;
        flex-direction: row;
        align-items: center;
    }
    
    .navbar-nav .nav-item {
        margin-bottom: 0;
        margin-right: 0.5rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        margin: 0;
        font-size: 0.95rem;
        border-radius: 0.375rem;
        border: 1px solid transparent;
    }
    
    .navbar-nav .nav-link:hover {
        transform: translateY(-2px);
    }
    
    .navbar-nav .nav-link i {
        font-size: 1rem;
        width: auto;
    }
    
    .navbar-nav .btn {
        margin: 0 0 0 0.5rem;
        padding: 0.5rem 1rem;
    }
}

/* ==========================================================================
   DESKTOP NAVIGATION (1024px+)
   ========================================================================== */

@media (min-width: 1024px) {
    .navbar {
        padding: 1rem 3rem;
    }
    
    .navbar-brand {
        font-size: 1.75rem;
    }
    
    .navbar-brand i {
        font-size: 2rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
    }
    
    .navbar-nav .nav-link i {
        font-size: 1.1rem;
    }
    
    .navbar-nav .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
    }
}

/* ==========================================================================
   EXTRA LARGE SCREENS (1200px+)
   ========================================================================== */

@media (min-width: 1200px) {
    .navbar {
        padding: 1.25rem 4rem;
    }
    
    .navbar-brand {
        font-size: 2rem;
    }
    
    .navbar-nav .nav-link {
        padding: 1rem 1.5rem;
        font-size: 1.1rem;
    }
}

/* ==========================================================================
   LAYOUT RESPONSIVE GRID
   ========================================================================== */

/* Mobile-first container */
.container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
}

@media (min-width: 768px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

@media (min-width: 1024px) {
    .container-fluid {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

/* Responsive Grid System */
.row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
}

.row > * {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

@media (min-width: 768px) {
    .row {
        margin-left: -1rem;
        margin-right: -1rem;
    }
    
    .row > * {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* ==========================================================================
   CARDS RESPONSIVE
   ========================================================================== */

/* Mobile Cards */
.card {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    background-color: rgba(0, 0, 0, 0.03);
}

.card-body {
    padding: 1rem;
}

.card-footer {
    padding: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    background-color: rgba(0, 0, 0, 0.03);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.card-text {
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Tablet Cards */
@media (min-width: 768px) {
    .card {
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        padding: 1.25rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    .card-footer {
        padding: 1.25rem;
    }
    
    .card-title {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }
    
    .card-text {
        font-size: 1rem;
    }
}

/* Desktop Cards */
@media (min-width: 1024px) {
    .card {
        margin-bottom: 2rem;
    }
    
    .card-header {
        padding: 1.5rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .card-footer {
        padding: 1.5rem;
    }
    
    .card-title {
        font-size: 1.375rem;
        margin-bottom: 1.25rem;
    }
    
    .card-text {
        font-size: 1.05rem;
    }
}

/* ==========================================================================
   FORMS RESPONSIVE
   ========================================================================== */

/* Mobile Forms */
.form-control {
    font-size: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    line-height: 1.5;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(139, 0, 0, 0.25);
}

.form-label {
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

.form-text {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-select {
    font-size: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    line-height: 1.5;
}

.form-check {
    margin-bottom: 0.75rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
}

.form-check-label {
    font-size: 0.95rem;
    padding-left: 0.5rem;
}

/* Tablet Forms */
@media (min-width: 768px) {
    .form-control {
        font-size: 1rem;
        padding: 0.875rem 1.125rem;
    }
    
    .form-label {
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }
    
    .form-select {
        font-size: 1rem;
        padding: 0.875rem 1.125rem;
    }
    
    .form-check-label {
        font-size: 1rem;
        padding-left: 0.75rem;
    }
}

/* Desktop Forms */
@media (min-width: 1024px) {
    .form-control {
        font-size: 1.05rem;
        padding: 1rem 1.25rem;
    }
    
    .form-label {
        font-size: 1.05rem;
        margin-bottom: 1rem;
    }
    
    .form-select {
        font-size: 1.05rem;
        padding: 1rem 1.25rem;
    }
    
    .form-check-label {
        font-size: 1.05rem;
        padding-left: 1rem;
    }
}

/* ==========================================================================
   BUTTONS RESPONSIVE
   ========================================================================== */

/* Mobile Buttons */
.btn {
    font-size: 0.95rem;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

.btn-lg {
    font-size: 1.125rem;
    padding: 1rem 2rem;
}

.btn-block {
    display: block;
    width: 100%;
}

/* Tablet Buttons */
@media (min-width: 768px) {
    .btn {
        font-size: 1rem;
        padding: 0.875rem 1.75rem;
    }
    
    .btn-sm {
        font-size: 0.95rem;
        padding: 0.625rem 1.25rem;
    }
    
    .btn-lg {
        font-size: 1.25rem;
        padding: 1.125rem 2.25rem;
    }
}

/* Desktop Buttons */
@media (min-width: 1024px) {
    .btn {
        font-size: 1.05rem;
        padding: 1rem 2rem;
    }
    
    .btn-sm {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }
    
    .btn-lg {
        font-size: 1.375rem;
        padding: 1.25rem 2.5rem;
    }
}

/* ==========================================================================
   TABLES RESPONSIVE
   ========================================================================== */

/* Mobile Tables */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 1rem;
}

.table {
    font-size: 0.875rem;
    margin-bottom: 0;
}

.table th,
.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table th {
    font-weight: 600;
    font-size: 0.875rem;
    background-color: rgba(0, 0, 0, 0.05);
}

/* Tablet Tables */
@media (min-width: 768px) {
    .table {
        font-size: 0.95rem;
    }
    
    .table th,
    .table td {
        padding: 0.875rem 0.75rem;
    }
    
    .table th {
        font-size: 0.95rem;
    }
}

/* Desktop Tables */
@media (min-width: 1024px) {
    .table {
        font-size: 1rem;
    }
    
    .table th,
    .table td {
        padding: 1rem;
    }
    
    .table th {
        font-size: 1rem;
    }
}

/* ==========================================================================
   DASHBOARD RESPONSIVE
   ========================================================================== */

/* Mobile Dashboard */
.dashboard-card {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dashboard-card .card-body {
    padding: 1rem;
}

.dashboard-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.dashboard-card .card-text {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.dashboard-card .card-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Tablet Dashboard */
@media (min-width: 768px) {
    .dashboard-card {
        margin-bottom: 1.5rem;
    }
    
    .dashboard-card .card-body {
        padding: 1.25rem;
    }
    
    .dashboard-card .card-title {
        font-size: 1.125rem;
        margin-bottom: 1rem;
    }
    
    .dashboard-card .card-text {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }
    
    .dashboard-card .card-icon {
        font-size: 2.25rem;
        margin-bottom: 0.75rem;
    }
}

/* Desktop Dashboard */
@media (min-width: 1024px) {
    .dashboard-card {
        margin-bottom: 2rem;
    }
    
    .dashboard-card .card-body {
        padding: 1.5rem;
    }
    
    .dashboard-card .card-title {
        font-size: 1.25rem;
        margin-bottom: 1.25rem;
    }
    
    .dashboard-card .card-text {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .dashboard-card .card-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
}

/* ==========================================================================
   PROFILE PAGE RESPONSIVE
   ========================================================================== */

/* Mobile Profile */
.profile-photo {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 50%;
}

.profile-photo-preview {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 50%;
}

.nav-tabs {
    border-bottom: 2px solid #dc3545;
    margin-bottom: 1rem;
}

.nav-tabs .nav-link {
    border-radius: 0.5rem 0.5rem 0 0;
    margin-right: 0.25rem;
    color: #6c757d;
    border: 2px solid #dee2e6;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    font-weight: 500;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
}

.tab-content {
    background: #fff;
    border-radius: 0 0 0.75rem 0.75rem;
    padding: 1rem;
    border: 2px solid #dc3545;
}

/* Tablet Profile */
@media (min-width: 768px) {
    .profile-photo {
        width: 150px;
        height: 150px;
        border: 4px solid #fff;
    }
    
    .profile-photo-preview {
        width: 100px;
        height: 100px;
    }
    
    .nav-tabs .nav-link {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        margin-right: 0.5rem;
    }
    
    .tab-content {
        padding: 1.5rem;
    }
}

/* Desktop Profile */
@media (min-width: 1024px) {
    .profile-photo {
        width: 180px;
        height: 180px;
        border: 5px solid #fff;
    }
    
    .profile-photo-preview {
        width: 120px;
        height: 120px;
    }
    
    .nav-tabs .nav-link {
        padding: 1.25rem 2rem;
        font-size: 1.125rem;
        margin-right: 0.75rem;
    }
    
    .tab-content {
        padding: 2rem;
    }
}

/* ==========================================================================
   UTILITY CLASSES RESPONSIVE
   ========================================================================== */

/* Responsive Spacing */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

/* Responsive Display */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

@media (min-width: 768px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .d-md-inline { display: inline !important; }
    .d-md-inline-block { display: inline-block !important; }
}

@media (min-width: 1024px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
    .d-lg-inline { display: inline !important; }
    .d-lg-inline-block { display: inline-block !important; }
}

/* Responsive Text Alignment */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

@media (min-width: 768px) {
    .text-md-left { text-align: left !important; }
    .text-md-center { text-align: center !important; }
    .text-md-right { text-align: right !important; }
}

@media (min-width: 1024px) {
    .text-lg-left { text-align: left !important; }
    .text-lg-center { text-align: center !important; }
    .text-lg-right { text-align: right !important; }
}

/* ==========================================================================
   TOUCH OPTIMIZATIONS
   ========================================================================== */

/* Touch-friendly elements */
.btn, .nav-link, .form-control, .form-select {
    min-height: 44px; /* Apple's recommended minimum touch target size */
}

.form-check-input {
    min-width: 44px;
    min-height: 44px;
}

/* Touch-friendly spacing */
.navbar-nav .nav-link {
    margin-bottom: 0.5rem;
}

@media (min-width: 768px) {
    .navbar-nav .nav-link {
        margin-bottom: 0;
    }
}

/* ==========================================================================
   PRINT STYLES
   ========================================================================== */

@media print {
    .navbar,
    .btn,
    .nav-tabs,
    .card-footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .container-fluid {
        max-width: 100% !important;
        padding: 0 !important;
    }
}

/* ==========================================================================
   ACCESSIBILITY IMPROVEMENTS
   ========================================================================== */

/* Focus indicators */
.btn:focus,
.form-control:focus,
.form-select:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
} 

/* ==========================================================================
   ADMIN SCROLL BEHAVIOR ENHANCEMENTS
   ========================================================================== */

/* Admin navbar scroll styling */
.admin-navbar-scrolled {
    background: linear-gradient(135deg, rgba(139, 0, 0, 0.95) 0%, rgba(220, 20, 60, 0.95) 100%) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Admin navigation open state */
.admin-navbar.admin-navigation-open {
    background: linear-gradient(135deg, rgba(139, 0, 0, 0.98) 0%, rgba(220, 20, 60, 0.98) 100%) !important;
    backdrop-filter: blur(15px);
}

.admin-navbar.admin-navigation-open .navbar-toggler {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.8);
}

/* Enhanced dropdown positioning for all screen sizes */
.admin-dropdown {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    transform: none !important;
    margin-top: 0.5rem;
    z-index: 1001;
    min-width: 200px;
    background: #fff;
    border: none;
    border-radius: 1rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.admin-dropdown.show {
    animation: slideDownAdmin 0.3s ease-out;
}

@keyframes slideDownAdmin {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Ensure dropdown is visible on all devices */
.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* Enhanced admin profile styling for better visibility */
.admin-profile {
    position: relative;
    z-index: 1000;
}

.admin-profile:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

/* Mobile-specific admin dropdown enhancements */
@media (max-width: 767.98px) {
    .admin-dropdown {
        min-width: 180px;
        max-width: 250px;
        margin-top: 0.25rem;
    }
    
    .admin-dropdown .dropdown-item {
        padding: 1rem 1.25rem;
        font-size: 0.95rem;
    }
    
    .admin-dropdown .dropdown-header {
        padding: 0.75rem 1.25rem;
        font-size: 0.9rem;
    }
}

/* Tablet-specific admin dropdown enhancements */
@media (min-width: 768px) and (max-width: 1023px) {
    .admin-dropdown {
        min-width: 200px;
        max-width: 280px;
    }
    
    .admin-dropdown .dropdown-item {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}

/* Desktop-specific admin dropdown enhancements */
@media (min-width: 1024px) {
    .admin-dropdown {
        min-width: 220px;
        max-width: 300px;
    }
    
    .admin-dropdown .dropdown-item {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
}

/* ==========================================================================
   ADMIN MOBILE NAVIGATION ENHANCEMENTS
   ========================================================================== */

/* Admin Mobile Navigation - Enhanced */
.admin-navbar {
    transition: all 0.3s ease;
}

.admin-navbar .navbar-toggler {
    padding: 0.5rem 0.75rem;
    font-size: 1.25rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    z-index: 10000 !important;
    position: relative;
}

.admin-navbar .navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.8);
    color: #fff;
}

.admin-navbar .navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
    outline: none;
}

.admin-navbar .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Admin Mobile Navigation Menu */
.admin-navbar .navbar-collapse {
    transition: all 0.3s ease-in-out;
}

.admin-navbar .navbar-nav {
    margin-top: 1rem;
    padding: 1rem 0;
    background: rgba(139, 0, 0, 0.95);
    border-radius: 0.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.admin-navbar .navbar-nav .nav-item {
    margin-bottom: 0.5rem;
}

.admin-navbar .navbar-nav .nav-link {
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    margin: 0.25rem 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9) !important;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border: 1px solid transparent;
}

.admin-navbar .navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #fff !important;
    transform: translateX(5px);
    border-color: rgba(255, 255, 255, 0.3);
}

.admin-navbar .navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: #fff !important;
    border-color: rgba(255, 255, 255, 0.5);
}

.admin-navbar .navbar-nav .nav-link .nav-icon {
    font-size: 1.1rem;
    width: 1.5rem;
    text-align: center;
}

/* Admin Mobile Navigation Overlay */
@media (max-width: 767.98px) {
    .admin-navbar .navbar-collapse {
        position: fixed !important;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 9999 !important;
        background: linear-gradient(135deg, rgba(139, 0, 0, 0.98) 0%, rgba(220, 20, 60, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        max-height: calc(100vh - 100%);
        overflow-y: auto;
    }

    .admin-navbar .navbar-nav {
        margin-top: 0;
        padding: 1rem;
        background: transparent;
        box-shadow: none;
    }

    /* Ensure admin navbar itself has proper z-index */
    .admin-navbar {
        z-index: 9998 !important;
        position: relative;
    }

    /* Create overlay backdrop when admin menu is open */
    .admin-navbar .navbar-collapse.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
        backdrop-filter: blur(2px);
    }

    /* Ensure admin mobile navigation has highest priority */
    .admin-navbar .navbar-collapse.show {
        z-index: 9999 !important;
    }

    /* Prevent other elements from interfering when admin menu is open */
    .admin-navbar .navbar-collapse.show ~ * {
        pointer-events: none;
    }

    /* Re-enable pointer events for admin navigation items */
    .admin-navbar .navbar-collapse.show .navbar-nav,
    .admin-navbar .navbar-collapse.show .navbar-nav * {
        pointer-events: auto;
    }

    /* Enhanced mobile navigation items */
    .admin-navbar .navbar-nav .nav-item {
        opacity: 0;
        transform: translateX(-30px);
        animation: slideInLeft 0.5s ease forwards;
    }
    
    .admin-navbar .navbar-nav .nav-item:nth-child(1) { animation-delay: 0.1s; }
    .admin-navbar .navbar-nav .nav-item:nth-child(2) { animation-delay: 0.2s; }
    .admin-navbar .navbar-nav .nav-item:nth-child(3) { animation-delay: 0.3s; }
    .admin-navbar .navbar-nav .nav-item:nth-child(4) { animation-delay: 0.4s; }
    .admin-navbar .navbar-nav .nav-item:nth-child(5) { animation-delay: 0.5s; }
    .admin-navbar .navbar-nav .nav-item:nth-child(6) { animation-delay: 0.6s; }
    .admin-navbar .navbar-nav .nav-item:nth-child(7) { animation-delay: 0.7s; }
    .admin-navbar .navbar-nav .nav-item:nth-child(8) { animation-delay: 0.8s; }
    .admin-navbar .navbar-nav .nav-item:nth-child(9) { animation-delay: 0.9s; }
    
    @keyframes slideInLeft {
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
}

/* ==========================================================================
   ADMIN DROPDOWN MOBILE ENHANCEMENTS
   ========================================================================== */

/* Enhanced Admin Dropdown for Mobile */
.admin-dropdown {
    background: #fff;
    border: none;
    border-radius: 1rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    min-width: 200px;
}

.admin-dropdown .dropdown-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--blood-crimson) 100%);
    color: #fff;
    padding: 1rem 1.5rem;
    font-weight: 600;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-dropdown .dropdown-item {
    padding: 0.75rem 1.5rem;
    color: #333;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-dropdown .dropdown-item:hover {
    background-color: rgba(139, 0, 0, 0.05);
    color: var(--primary-color);
    transform: translateX(5px);
}

.admin-dropdown .dropdown-item.text-danger:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* Mobile Admin Profile Dropdown */
@media (max-width: 767.98px) {
    .admin-profile {
        padding: 0.75rem 1rem;
        border-radius: 2rem;
        background: rgba(255, 255, 255, 0.15);
        transition: all 0.3s ease;
        cursor: pointer;
        margin: 0.5rem;
    }
    
    .admin-profile:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
    }
    
    .admin-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid rgba(255, 255, 255, 0.3);
        margin-right: 0.5rem;
    }
    
    .admin-avatar i {
        font-size: 1rem;
        color: #fff;
    }
    
    .admin-info {
        display: flex;
        flex-direction: column;
    }
    
    .admin-name {
        font-weight: 600;
        color: #fff;
        margin: 0;
        font-size: 0.9rem;
    }
    
    /* Enhanced dropdown positioning for mobile */
    .admin-dropdown {
        position: absolute !important;
        top: 100% !important;
        right: 0 !important;
        left: auto !important;
        transform: none !important;
        margin-top: 0.5rem;
        z-index: 1001;
        min-width: 180px;
    }
    
    .admin-dropdown.show {
        animation: slideDownAdmin 0.3s ease-out;
    }
    
    @keyframes slideDownAdmin {
        from {
            opacity: 0;
            transform: translateY(-10px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
    
    /* Ensure dropdown is visible on mobile */
    .dropdown-menu-end {
        right: 0 !important;
        left: auto !important;
    }
}

/* ==========================================================================
   ADMIN TABLET NAVIGATION (768px - 1023px)
   ========================================================================== */

@media (min-width: 768px) {
    .admin-navbar {
        padding: 1rem 2rem;
    }
    
    .admin-navbar .navbar-brand {
        font-size: 1.5rem;
    }
    
    .admin-navbar .navbar-nav {
        margin-top: 0;
        padding: 0;
        background: transparent;
        box-shadow: none;
        flex-direction: row;
        align-items: center;
    }
    
    .admin-navbar .navbar-nav .nav-item {
        margin-bottom: 0;
        margin-right: 0.5rem;
    }
    
    .admin-navbar .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        margin: 0;
        font-size: 0.95rem;
        border-radius: 0.375rem;
        border: 1px solid transparent;
    }
    
    .admin-navbar .navbar-nav .nav-link:hover {
        transform: translateY(-2px);
    }
    
    .admin-navbar .navbar-nav .nav-link .nav-icon {
        font-size: 1rem;
        width: auto;
    }
    
    .admin-profile {
        padding: 0.5rem 1rem;
    }
    
    .admin-avatar {
        width: 40px;
        height: 40px;
    }
}

/* ==========================================================================
   ADMIN DESKTOP NAVIGATION (1024px+)
   ========================================================================== */

@media (min-width: 1024px) {
    .admin-navbar {
        padding: 1rem 3rem;
    }
    
    .admin-navbar .navbar-brand {
        font-size: 1.75rem;
    }
    
    .admin-navbar .navbar-nav .nav-link {
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
    }
    
    .admin-navbar .navbar-nav .nav-link .nav-icon {
        font-size: 1.1rem;
    }
    
    .admin-profile {
        padding: 0.75rem 1.25rem;
    }
    
    .admin-avatar {
        width: 45px;
        height: 45px;
    }
}

/* ==========================================================================
   ADMIN EXTRA LARGE SCREENS (1200px+)
   ========================================================================== */

@media (min-width: 1200px) {
    .admin-navbar {
        padding: 1.25rem 4rem;
    }
    
    .admin-navbar .navbar-brand {
        font-size: 2rem;
    }
    
    .admin-navbar .navbar-nav .nav-link {
        padding: 1rem 1.5rem;
        font-size: 1.1rem;
    }
    
    .admin-profile {
        padding: 1rem 1.5rem;
    }
    
    .admin-avatar {
        width: 50px;
        height: 50px;
    }
}

/* ==========================================================================
   ADMIN ACCESSIBILITY ENHANCEMENTS
   ========================================================================== */

/* Enhanced focus states for admin navigation */
.admin-navbar .nav-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
    border-radius: 0.5rem;
}

.admin-navbar .navbar-toggler:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

.admin-profile:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
    border-radius: 2rem;
}

/* High contrast mode support for admin */
@media (prefers-contrast: high) {
    .admin-navbar .nav-link {
        border: 2px solid currentColor;
    }
    
    .admin-navbar .nav-link:hover,
    .admin-navbar .nav-link.active {
        background: currentColor;
        color: #000 !important;
    }
    
    .admin-navbar .navbar-toggler {
        border-width: 3px;
    }
    
    .admin-profile {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support for admin */
@media (prefers-reduced-motion: reduce) {
    .admin-navbar .navbar-nav .nav-link,
    .admin-navbar .navbar-toggler,
    .admin-navbar .navbar-collapse,
    .admin-profile,
    .admin-dropdown {
        transition: none !important;
        animation: none !important;
    }
    
    .admin-navbar .navbar-nav .nav-item {
        animation: none !important;
    }
}

/* ==========================================================================
   ADMIN BRAND ENHANCEMENTS
   ========================================================================== */

/* Mobile Admin Brand */
@media (max-width: 767.98px) {
    .admin-navbar .navbar-brand {
        font-size: 1.25rem;
    }
    
    .brand-title {
        font-size: 1.25rem;
    }
    
    .brand-subtitle {
        font-size: 0.75rem;
    }
    
    .brand-icon {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 1rem;
        margin-right: 0.5rem;
    }
}

/* Tablet Admin Brand */
@media (min-width: 768px) and (max-width: 1023px) {
    .admin-navbar .navbar-brand {
        font-size: 1.5rem;
    }
    
    .brand-title {
        font-size: 1.5rem;
    }
    
    .brand-subtitle {
        font-size: 0.875rem;
    }
    
    .brand-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: 1.25rem;
        margin-right: 0.75rem;
    }
}

/* Desktop Admin Brand */
@media (min-width: 1024px) {
    .admin-navbar .navbar-brand {
        font-size: 1.75rem;
    }
    
    .brand-title {
        font-size: 1.75rem;
    }
    
    .brand-subtitle {
        font-size: 1rem;
    }
    
    .brand-icon {
        width: 45px;
        height: 45px;
        line-height: 45px;
        font-size: 1.5rem;
        margin-right: 1rem;
    }
} 