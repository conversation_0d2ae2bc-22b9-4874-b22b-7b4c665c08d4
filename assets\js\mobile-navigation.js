/**
 * Enhanced Mobile Navigation JavaScript
 * Blood Donation Management System
 * 
 * This file provides enhanced mobile navigation functionality including:
 * - Smooth animations and transitions
 * - Better touch interactions
 * - Accessibility improvements
 * - Navigation state management
 * - Responsive behavior enhancements
 */

class MobileNavigation {
    constructor() {
        this.navbar = null;
        this.toggler = null;
        this.collapse = null;
        this.navLinks = null;
        this.isOpen = false;
        this.touchStartY = 0;
        this.touchEndY = 0;
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupNavigation());
        } else {
            this.setupNavigation();
        }
    }
    
    setupNavigation() {
        this.navbar = document.querySelector('.navbar');
        this.toggler = document.querySelector('.navbar-toggler');
        this.collapse = document.querySelector('.navbar-collapse');
        this.navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        
        if (!this.navbar || !this.toggler || !this.collapse) {
            console.warn('Mobile Navigation: Required elements not found');
            return;
        }
        
        this.bindEvents();
        this.setupAccessibility();
        this.setupTouchGestures();
        this.setupScrollBehavior();
    }
    
    bindEvents() {
        // Toggler click event
        this.toggler.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleNavigation();
        });
        
        // Close navigation when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.navbar.contains(e.target)) {
                this.closeNavigation();
            }
        });
        
        // Handle navigation link clicks
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // Add click animation
                this.addClickAnimation(e.target);
                
                // Close mobile navigation after link click
                if (window.innerWidth < 768) {
                    setTimeout(() => this.closeNavigation(), 300);
                }
            });
        });
        
        // Handle window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
        
        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleResize(), 100);
        });
    }
    
    setupAccessibility() {
        // Add ARIA attributes
        this.toggler.setAttribute('aria-expanded', 'false');
        this.toggler.setAttribute('aria-label', 'Toggle navigation menu');
        
        // Add keyboard navigation
        this.toggler.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleNavigation();
            }
        });
        
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeNavigation();
            }
        });
        
        // Add focus management
        this.navLinks.forEach(link => {
            link.addEventListener('focus', () => {
                if (this.isOpen) {
                    link.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            });
        });
    }
    
    setupTouchGestures() {
        // Handle touch gestures for mobile
        if ('ontouchstart' in window) {
            this.navbar.addEventListener('touchstart', (e) => {
                this.touchStartY = e.touches[0].clientY;
            });
            
            this.navbar.addEventListener('touchend', (e) => {
                this.touchEndY = e.changedTouches[0].clientY;
                this.handleTouchGesture();
            });
        }
    }
    
    setupScrollBehavior() {
        let lastScrollTop = 0;
        let scrollThreshold = 10;
        
        window.addEventListener('scroll', this.throttle(() => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollDelta = Math.abs(scrollTop - lastScrollTop);
            
            if (scrollDelta > scrollThreshold) {
                if (scrollTop > lastScrollTop && this.isOpen) {
                    // Scrolling down - close navigation
                    this.closeNavigation();
                }
                
                // Add scroll-based navbar styling
                if (scrollTop > 50) {
                    this.navbar.classList.add('navbar-scrolled');
                } else {
                    this.navbar.classList.remove('navbar-scrolled');
                }
                
                lastScrollTop = scrollTop;
            }
        }, 100));
    }
    
    toggleNavigation() {
        if (this.isOpen) {
            this.closeNavigation();
        } else {
            this.openNavigation();
        }
    }
    
    openNavigation() {
        this.isOpen = true;
        this.toggler.setAttribute('aria-expanded', 'true');
        this.collapse.classList.add('show');
        this.navbar.classList.add('navigation-open');
        
        // Add body scroll lock
        document.body.style.overflow = 'hidden';
        
        // Focus first navigation link
        setTimeout(() => {
            const firstLink = this.collapse.querySelector('.nav-link');
            if (firstLink) {
                firstLink.focus();
            }
        }, 300);
        
        // Add entrance animation
        this.addEntranceAnimation();
    }
    
    closeNavigation() {
        this.isOpen = false;
        this.toggler.setAttribute('aria-expanded', 'false');
        this.collapse.classList.remove('show');
        this.navbar.classList.remove('navigation-open');
        
        // Restore body scroll
        document.body.style.overflow = '';
        
        // Add exit animation
        this.addExitAnimation();
    }
    
    addEntranceAnimation() {
        this.navLinks.forEach((link, index) => {
            link.style.opacity = '0';
            link.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                link.style.transition = 'all 0.3s ease';
                link.style.opacity = '1';
                link.style.transform = 'translateX(0)';
            }, index * 100);
        });
    }
    
    addExitAnimation() {
        this.navLinks.forEach((link, index) => {
            setTimeout(() => {
                link.style.opacity = '0';
                link.style.transform = 'translateX(-20px)';
            }, index * 50);
        });
    }
    
    addClickAnimation(element) {
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }
    
    handleTouchGesture() {
        const touchDistance = this.touchStartY - this.touchEndY;
        const minSwipeDistance = 50;
        
        if (Math.abs(touchDistance) > minSwipeDistance) {
            if (touchDistance > 0 && this.isOpen) {
                // Swipe up - close navigation
                this.closeNavigation();
            }
        }
    }
    
    handleResize() {
        if (window.innerWidth >= 768) {
            // Reset mobile-specific states on larger screens
            this.isOpen = false;
            this.toggler.setAttribute('aria-expanded', 'false');
            this.collapse.classList.remove('show');
            this.navbar.classList.remove('navigation-open');
            document.body.style.overflow = '';
            
            // Reset animations
            this.navLinks.forEach(link => {
                link.style.opacity = '';
                link.style.transform = '';
                link.style.transition = '';
            });
        }
    }
    
    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialize mobile navigation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new MobileNavigation();
});

// Export for potential use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileNavigation;
} 