<?php
/**
 * DonationCenter Class
 * Blood Donation Management System
 * 
 * Handles donation center management with archive functionality
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

class DonationCenter {
    private $db;
    private $id;
    private $name;
    private $address;
    private $city;
    private $phone;
    private $email;
    private $operatingHours;
    private $capacity;
    private $facilities;
    private $isActive;
    private $isArchived;
    private $archivedAt;
    private $archivedBy;
    private $createdAt;
    private $updatedAt;

    public function __construct($id = null) {
        $this->db = Database::getInstance();
        
        if ($id) {
            $this->id = $id;
            $this->loadFromDatabase();
        }
    }

    /**
     * Load donation center data from database
     */
    private function loadFromDatabase() {
        $sql = "SELECT * FROM donation_centers WHERE id = ?";
        $data = $this->db->fetch($sql, [$this->id]);
        
        if ($data) {
            $this->name = $data['name'];
            $this->address = $data['address'];
            $this->city = $data['city'];
            $this->phone = $data['phone'];
            $this->email = $data['email'];
            $this->operatingHours = $data['operating_hours'];
            $this->capacity = $data['capacity'];
            $this->facilities = json_decode($data['facilities'] ?? '[]', true);
            $this->isActive = (bool)$data['is_active'];
            $this->isArchived = (bool)($data['is_archived'] ?? false);
            $this->archivedAt = $data['archived_at'] ?? null;
            $this->archivedBy = $data['archived_by'] ?? null;
            $this->createdAt = $data['created_at'];
            $this->updatedAt = $data['updated_at'];
        }
    }

    /**
     * Archive donation center instead of deleting
     */
    public function archive($adminId, $reason = '') {
        // Check if center has scheduled donations
        $checkSql = "SELECT COUNT(*) as count FROM donations WHERE location LIKE ? AND status = 'scheduled'";
        $result = $this->db->fetch($checkSql, ["%{$this->name}%"]);
        
        if ($result['count'] > 0) {
            throw new Exception('Cannot archive center with scheduled donations. Please complete or cancel them first.');
        }

        $sql = "UPDATE donation_centers SET
                is_archived = TRUE,
                archived_at = NOW(),
                archived_by = ?,
                is_active = FALSE,
                updated_at = NOW()
                WHERE id = ?";

        $this->db->execute($sql, [
            $adminId,
            $this->id
        ]);

        $this->isArchived = true;
        $this->archivedAt = date(DATETIME_FORMAT);
        $this->archivedBy = $adminId;
        $this->isActive = false;

        logEvent('INFO', 'Donation center archived', [
            'center_id' => $this->id,
            'center_name' => $this->name,
            'admin_id' => $adminId,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * Restore archived donation center
     */
    public function restore($adminId, $reason = '') {
        $sql = "UPDATE donation_centers SET
                is_archived = FALSE,
                archived_at = NULL,
                archived_by = NULL,
                is_active = TRUE,
                updated_at = NOW()
                WHERE id = ?";

        $this->db->execute($sql, [
            $this->id
        ]);

        $this->isArchived = false;
        $this->archivedAt = null;
        $this->archivedBy = null;
        $this->isActive = true;

        logEvent('INFO', 'Donation center restored', [
            'center_id' => $this->id,
            'center_name' => $this->name,
            'admin_id' => $adminId,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * Get all donation centers with filtering options
     */
    public static function getAll($includeArchived = false, $filters = []) {
        $db = Database::getInstance();
        
        $whereClause = "WHERE 1=1";
        $params = [];

        // Exclude archived centers by default (temporarily disabled for debugging)
        // if (!$includeArchived) {
        //     $whereClause .= " AND (is_archived IS NULL OR is_archived = FALSE)";
        // }

        // Apply filters
        if (!empty($filters['city'])) {
            $whereClause .= " AND city = ?";
            $params[] = $filters['city'];
        }

        if (!empty($filters['active_only'])) {
            $whereClause .= " AND is_active = TRUE";
        }

        if (!empty($filters['search'])) {
            $whereClause .= " AND (name LIKE ? OR address LIKE ? OR city LIKE ?)";
            $searchTerm = "%{$filters['search']}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $sql = "SELECT * FROM donation_centers {$whereClause} ORDER BY city, name";
        
        return $db->fetchAll($sql, $params);
    }

    /**
     * Get archived donation centers
     */
    public static function getArchived() {
        $db = Database::getInstance();
        
        $sql = "SELECT dc.*, u.username as archived_by_username 
                FROM donation_centers dc
                LEFT JOIN users u ON dc.archived_by = u.id
                WHERE dc.is_archived = TRUE
                ORDER BY dc.archived_at DESC";
        
        return $db->fetchAll($sql);
    }

    /**
     * Create new donation center
     */
    public static function create($data) {
        $db = Database::getInstance();
        
        $sql = "INSERT INTO donation_centers (name, address, city, phone, email, operating_hours, capacity, facilities, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $db->execute($sql, [
            $data['name'],
            $data['address'],
            $data['city'],
            $data['phone'] ?? null,
            $data['email'] ?? null,
            $data['operating_hours'] ?? null,
            $data['capacity'] ?? 0,
            json_encode($data['facilities'] ?? []),
            $data['is_active'] ?? true
        ]);
        
        $centerId = $db->getLastInsertId();
        
        logEvent('INFO', 'Donation center created', [
            'center_id' => $centerId,
            'center_name' => $data['name']
        ]);
        
        return new self($centerId);
    }

    /**
     * Update donation center
     */
    public function update($data) {
        $sql = "UPDATE donation_centers SET 
                name = ?, address = ?, city = ?, phone = ?, email = ?, 
                operating_hours = ?, capacity = ?, facilities = ?, is_active = ?, 
                updated_at = NOW() 
                WHERE id = ?";
        
        $this->db->execute($sql, [
            $data['name'],
            $data['address'],
            $data['city'],
            $data['phone'] ?? null,
            $data['email'] ?? null,
            $data['operating_hours'] ?? null,
            $data['capacity'] ?? 0,
            json_encode($data['facilities'] ?? []),
            $data['is_active'] ?? true,
            $this->id
        ]);
        
        // Reload data
        $this->loadFromDatabase();
        
        logEvent('INFO', 'Donation center updated', [
            'center_id' => $this->id,
            'center_name' => $this->name
        ]);
        
        return true;
    }

    // Getters
    public function getId() { return $this->id; }
    public function getName() { return $this->name; }
    public function getAddress() { return $this->address; }
    public function getCity() { return $this->city; }
    public function getPhone() { return $this->phone; }
    public function getEmail() { return $this->email; }
    public function getOperatingHours() { return $this->operatingHours; }
    public function getCapacity() { return $this->capacity; }
    public function getFacilities() { return $this->facilities; }
    public function isActive() { return $this->isActive; }
    public function isArchived() { return $this->isArchived; }
    public function getArchivedAt() { return $this->archivedAt; }
    public function getArchivedBy() { return $this->archivedBy; }
    public function getCreatedAt() { return $this->createdAt; }
    public function getUpdatedAt() { return $this->updatedAt; }
}
?>
