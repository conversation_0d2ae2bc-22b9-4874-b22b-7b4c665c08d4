/**
 * Main Stylesheet
 * Blood Donation Management System
 */

/* Global Styles */
:root {
    --primary-color: #8B0000;
    --primary-dark: #660000;
    --primary-light: #DC143C;
    --blood-red: #8B0000;
    --blood-crimson: #DC143C;
    --blood-maroon: #800000;
    --blood-light: #FFB6C1;
    --secondary-color: #2196f3;
    --secondary-dark: #1976d2;
    --secondary-light: #64b5f6;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #8B0000;
    --info-color: #2196f3;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gray-color: #6c757d;
    --white-color: #ffffff;
    --black-color: #000000;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

/* Blood Theme Animations */
@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.1);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.1);
    }
    70% {
        transform: scale(1);
    }
}

@keyframes bloodDrop {
    0% {
        transform: translateY(-10px);
        opacity: 0.8;
    }
    50% {
        transform: translateY(0px);
        opacity: 1;
    }
    100% {
        transform: translateY(10px);
        opacity: 0.8;
    }
}

/* Blood themed elements */
.blood-gradient {
    background: linear-gradient(135deg, var(--blood-red) 0%, var(--blood-crimson) 50%, var(--blood-maroon) 100%) !important;
}

.blood-shadow {
    box-shadow: 0 6px 20px rgba(139, 0, 0, 0.3);
}

/* Override Bootstrap card backgrounds for blood theme */
.card.blood-theme {
    background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%) !important;
    border: 2px solid #8B0000 !important;
    box-shadow: 0 6px 20px rgba(139, 0, 0, 0.3) !important;
}

.card.blood-theme .card-body {
    color: #ffffff !important;
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(139, 0, 0, 0.3);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: #fff;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(139, 0, 0, 0.3);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    box-shadow: 0 0 0 0.2rem rgba(139, 0, 0, 0.25);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: #fff;
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    color: #fff;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: #fff;
}

.btn-danger:hover, .btn-danger:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: #fff;
}

.btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
    background-color: transparent;
}

.btn-outline-danger:hover, .btn-outline-danger:focus {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: #fff;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card-header {
    background-color: rgba(139, 0, 0, 0.05);
    border-bottom: 1px solid rgba(139, 0, 0, 0.1);
    border-radius: 10px 10px 0 0 !important;
}

.card-footer {
    background-color: rgba(139, 0, 0, 0.05);
    border-top: 1px solid rgba(139, 0, 0, 0.1);
    border-radius: 0 0 10px 10px !important;
}

.card-header.bg-danger {
    background-color: var(--danger-color) !important;
    color: #fff;
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(139, 0, 0, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.input-group-text {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-danger {
    background-color: rgba(139, 0, 0, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-info {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

.alert-warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

/* Navbar Styles */
.navbar-brand {
    font-weight: 700;
    color: #fff !important;
}

.navbar-dark {
    background-color: var(--primary-color) !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff !important;
}

.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .show > .nav-link {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

/* Badge Styles */
.badge-primary {
    background-color: var(--primary-color);
    color: #fff;
}

.badge-secondary {
    background-color: var(--secondary-color);
    color: #fff;
}

.badge-success {
    background-color: var(--success-color);
    color: #fff;
}

.badge-danger {
    background-color: var(--danger-color);
    color: #fff;
}

.badge-warning {
    background-color: var(--warning-color);
    color: #fff;
}

.badge-info {
    background-color: var(--info-color);
    color: #fff;
}

/* Table Styles */
.table-hover tbody tr:hover {
    background-color: rgba(139, 0, 0, 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(139, 0, 0, 0.02);
}

/* Blood Type Badge */
.blood-type-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 2rem;
    background: linear-gradient(135deg, var(--blood-red) 0%, var(--blood-crimson) 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(139, 0, 0, 0.3);
    animation: heartbeat 2s infinite;
}

/* Urgency Levels */
.urgency-low {
    background-color: #28a745;
    color: #fff;
}

.urgency-medium {
    background-color: #ffc107;
    color: #212529;
}

.urgency-high {
    background-color: #fd7e14;
    color: #fff;
}

.urgency-critical {
    background-color: #dc3545;
    color: #fff;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* Status Styles */
.status-pending {
    background-color: #ffc107;
    color: #212529;
}

.status-approved {
    background-color: #28a745;
    color: #fff;
}

.status-fulfilled {
    background-color: #17a2b8;
    color: #fff;
}

.status-cancelled {
    background-color: #6c757d;
    color: #fff;
}

.status-scheduled {
    background-color: #6610f2;
    color: #fff;
}

.status-completed {
    background-color: #28a745;
    color: #fff;
}

/* Dashboard Cards */
.dashboard-card {
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-body {
    padding: 2rem;
}

.dashboard-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

/* Admin Dashboard Specific Styles */
.admin-dashboard .welcome-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--blood-crimson) 100%);
    color: #fff;
}

.admin-dashboard .stats-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 2px solid var(--primary-color);
}

.admin-dashboard .stats-card .card-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.admin-dashboard .stats-card .card-text {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.admin-dashboard .activity-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.admin-dashboard .inventory-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

/* Dashboard Card Text and Icons */
.dashboard-card .card-text {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.dashboard-card .card-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Chat Container */
.chat-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1rem;
    background-color: #f8f9fa;
}

.chat-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 10px;
    max-width: 80%;
}

.chat-message-sender {
    background-color: var(--primary-color);
    color: #fff;
    margin-left: auto;
}

.chat-message-receiver {
    background-color: #e9ecef;
    color: #333;
}

.chat-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
    text-align: right;
}

/* Notification Styles */
.notification-item {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: rgba(139, 0, 0, 0.05);
}

.notification-item.unread {
    background-color: rgba(139, 0, 0, 0.1);
    border-left: 4px solid var(--primary-color);
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Footer */
.footer {
    background-color: var(--primary-color);
    color: #fff;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.footer a:hover {
    color: #fff;
    text-decoration: underline;
}

/* Fix gradient card visibility */
.card.bg-gradient {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* Add text shadow for better readability */
}
.card.bg-gradient .card-body {
    background: rgba(0, 0, 0, 0.1) !important; /* Semi-transparent dark overlay for better contrast */
    color: inherit;
}

/* User Status Indicators */
.user-avatar-container {
    position: relative;
    display: inline-block;
}

.user-status-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    z-index: 10;
}

.user-status-indicator.active {
    background-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

.user-status-indicator.inactive {
    background-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(139, 0, 0, 0.3);
    animation: pulse-red 2s infinite;
}

@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 2px rgba(139, 0, 0, 0.3);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(139, 0, 0, 0.1);
    }
    100% {
        box-shadow: 0 0 0 2px rgba(139, 0, 0, 0.3);
    }
}

/* Status Toggle Buttons */
.status-toggle {
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.status-toggle.active {
    background-color: #28a745;
    color: #fff;
}

.status-toggle.active:hover {
    background-color: #218838;
    transform: translateY(-1px);
}

.status-toggle.inactive {
    background-color: var(--primary-color);
    color: #fff;
}

.status-toggle.inactive:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

/* Admin Panel Sections */
.admin-panel-section {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.admin-panel-section .section-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--blood-crimson) 100%);
    color: #fff;
    padding: 1.5rem;
}

.admin-panel-section .section-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.25rem;
}

.admin-panel-section .section-body {
    padding: 2rem;
}

/* Stats Card */
.stats-card .card-text {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    animation: countUp 2s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card:hover .card-text {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

/* Blood Type Badge Enhanced */
.blood-type-badge {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 3rem;
    background: linear-gradient(135deg, var(--blood-red) 0%, var(--blood-crimson) 50%, var(--blood-maroon) 100%);
    color: #fff;
    box-shadow: 0 4px 15px rgba(139, 0, 0, 0.4);
    animation: heartbeat 2s infinite;
    position: relative;
    overflow: hidden;
}

.blood-type-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Activity Timeline */
.activity-timeline {
    position: relative;
    padding-left: 2rem;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), var(--blood-crimson));
}

.activity-item {
    position: relative;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-item::before {
    content: '';
    position: absolute;
    left: -1.5rem;
    top: 1.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 3px solid #fff;
    box-shadow: 0 0 0 3px var(--primary-color);
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 3px solid rgba(139, 0, 0, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Heartbeat Animation */
@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.1);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.1);
    }
    70% {
        transform: scale(1);
    }
}

/* Admin Welcome Card */
.admin-welcome-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--blood-crimson) 50%, var(--blood-maroon) 100%);
    color: #fff;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.admin-welcome-card .card-body {
    position: relative;
    z-index: 2;
    padding: 3rem;
}

.admin-welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

/* Enhanced Stats Card */
.stats-card-enhanced {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.stats-card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(139, 0, 0, 0.2);
}

.stats-card-enhanced .card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.stats-card-enhanced .stats-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(139, 0, 0, 0.1);
}

.stats-card-enhanced .stats-icon {
    font-size: 3.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    opacity: 0.8;
}

/* Quick Action Card */
.quick-action-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 2px solid var(--secondary-color);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.quick-action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.2);
}

.quick-action-card .card-header {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: #fff;
    border: none;
    padding: 1.5rem;
}

.quick-action-card .btn {
    border-radius: 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quick-action-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

/* Container Fluid */
.container-fluid {
    max-width: 100%;
    padding-left: 2rem;
    padding-right: 2rem;
}

/* Row with Gutters */
.row.g-4 > * {
    padding: 1rem;
}

/* Card Height */
.card.h-100 {
    height: 100% !important;
}

/* Text Colors */
.text-danger {
    color: var(--danger-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: #fff;
}

.btn-danger:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: #fff;
}

.btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
    background-color: transparent;
}

.btn-outline-danger:hover {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: #fff;
}

/* Admin Navbar */
.admin-navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--blood-crimson) 100%);
    box-shadow: 0 4px 15px rgba(139, 0, 0, 0.3);
    padding: 1rem 0;
}

.admin-navbar .navbar-brand {
    font-size: 1.75rem;
    font-weight: 700;
    color: #fff !important;
    text-decoration: none;
}

.admin-navbar .navbar-brand:hover {
    color: rgba(255, 255, 255, 0.9) !important;
}

.navbar-brand-icon {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    margin-right: 0.75rem;
    font-size: 1.25rem;
    color: #fff;
}

.brand-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #fff;
    margin: 0;
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-weight: 400;
}

.admin-navbar .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
    position: relative;
    overflow: hidden;
}

.admin-navbar .nav-link:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.admin-navbar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff !important;
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.nav-icon {
    font-size: 1.25rem;
    margin-right: 0.5rem;
    width: 1.5rem;
    text-align: center;
}

.nav-text {
    font-size: 0.95rem;
}

/* Admin Profile */
.admin-profile {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.admin-profile:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.admin-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.3);
    margin-right: 0.75rem;
}

.admin-avatar-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.3);
    margin-right: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.admin-name {
    font-weight: 600;
    color: #fff;
    margin: 0;
    font-size: 1rem;
}

.admin-role {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    text-transform: capitalize;
}

.admin-info {
    display: flex;
    flex-direction: column;
}

.admin-dropdown {
    background: #fff;
    border: none;
    border-radius: 1rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.admin-dropdown .dropdown-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--blood-crimson) 100%);
    color: #fff;
    padding: 1rem 1.5rem;
    font-weight: 600;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-dropdown .dropdown-item {
    padding: 0.75rem 1.5rem;
    color: #333;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f8f9fa;
}

.admin-dropdown .dropdown-item:hover {
    background-color: rgba(139, 0, 0, 0.05);
    color: var(--primary-color);
    transform: translateX(5px);
}

.admin-dropdown .dropdown-item.text-danger:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* Responsive Design - Import the new responsive CSS */
@import url('responsive.css');
