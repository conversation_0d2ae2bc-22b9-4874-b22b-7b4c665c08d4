<?php
/**
 * Get Online Status API
 * Blood Donation Management System
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../config/constants.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session
startSecureSession();

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $currentUser = getCurrentUser();
    
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    $userIds = $input['user_ids'] ?? [];
    
    if (empty($userIds) || !is_array($userIds)) {
        echo json_encode(['success' => false, 'message' => 'Invalid user IDs']);
        exit;
    }
    
    // Sanitize user IDs
    $userIds = array_map('intval', $userIds);
    $userIds = array_filter($userIds, function($id) { return $id > 0; });
    
    if (empty($userIds)) {
        echo json_encode(['success' => false, 'message' => 'No valid user IDs']);
        exit;
    }
    
    $db = Database::getInstance();
    
    // Get online status for requested users
    $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
    $sql = "SELECT id, is_online, last_activity, last_seen 
            FROM users 
            WHERE id IN ($placeholders)";
    
    $users = $db->fetchAll($sql, $userIds);
    
    $statuses = [];
    
    foreach ($users as $user) {
        $isActive = isUserActive($user['id']);
        $status = getUserStatus($user['id']);

        $statuses[$user['id']] = [
            'is_active' => $isActive,
            'status' => $status,
            'last_activity' => $user['last_activity']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'statuses' => $statuses,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log("Get online status error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error'
    ]);
}
?>
