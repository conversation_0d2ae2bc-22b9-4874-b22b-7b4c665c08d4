<?php
/**
 * Create Blood Request - Unified Dashboard
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../classes/UnifiedUser.php';
require_once '../classes/BloodRequest.php';

// Start session and check authentication
startSecureSession();
requireUnifiedAccess('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();
$unifiedUser = new UnifiedUser($currentUser['id']);

// Get current active role from session
$currentRole = $_SESSION['current_role'] ?? $unifiedUser->getPrimaryRole();

// Check if user has recipient role and if recipient is the current active role
if (!$unifiedUser->hasRole('recipient')) {
    redirectWithMessage('index.php', 'You need recipient role to access this page.', 'error');
}

if ($currentRole !== 'recipient') {
    redirectWithMessage('index.php', 'Only recipients can request blood. Please switch to recipient mode to access this feature.', 'error');
}

$errors = [];
$success = '';

// Get blood types
$bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        try {
            // Validate input
            $bloodTypeId = (int)($_POST['blood_type_id'] ?? 0);
            $unitsNeeded = (int)($_POST['units_needed'] ?? 0);
            $urgencyLevel = $_POST['urgency_level'] ?? '';
            $hospitalName = sanitizeInput($_POST['hospital_name'] ?? '');
            $hospitalAddress = sanitizeInput($_POST['hospital_address'] ?? '');
            $hospitalContact = sanitizeInput($_POST['hospital_contact'] ?? '');
            $requiredByDate = $_POST['required_by_date'] ?? '';

            // Basic validation
            if ($bloodTypeId <= 0) {
                $errors[] = 'Please select a blood type.';
            }
            if ($unitsNeeded <= 0 || $unitsNeeded > 10) {
                $errors[] = 'Units needed must be between 1 and 10.';
            }
            if (!in_array($urgencyLevel, ['low', 'medium', 'high', 'critical'])) {
                $errors[] = 'Please select a valid urgency level.';
            }
            if (empty($hospitalName)) {
                $errors[] = 'Hospital name is required.';
            }
            if (empty($hospitalAddress)) {
                $errors[] = 'Hospital address is required.';
            }
            if (empty($hospitalContact)) {
                $errors[] = 'Hospital contact is required.';
            }
            if (empty($requiredByDate)) {
                $errors[] = 'Required by date is required.';
            } elseif (strtotime($requiredByDate) < time()) {
                $errors[] = 'Required by date must be in the future.';
            }

            if (empty($errors)) {
                // Create blood request
                $sql = "INSERT INTO blood_requests (recipient_id, blood_type_id, units_needed, urgency_level,
                        hospital_name, hospital_address, hospital_contact, required_by_date, status, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";

                $db->execute($sql, [
                    $currentUser['id'],
                    $bloodTypeId,
                    $unitsNeeded,
                    $urgencyLevel,
                    $hospitalName,
                    $hospitalAddress,
                    $hospitalContact,
                    $requiredByDate
                ]);

                redirectWithMessage('requests.php', 'Blood request created successfully!', 'success');
            }
        } catch (Exception $e) {
            $errors[] = $e->getMessage();
        }
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/responsive.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg, #8B0000 0%, #DC143C 100%);">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="requests.php">
                    <i class="fas fa-arrow-left"></i> Back to My Requests
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-plus"></i> Create Blood Request</h4>
                        <p class="mb-0">Request blood for medical needs</p>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="blood_type_id" class="form-label">Blood Type *</label>
                                        <select class="form-select" id="blood_type_id" name="blood_type_id" required>
                                            <option value="">Select Blood Type</option>
                                            <?php foreach ($bloodTypes as $bloodType): ?>
                                                <option value="<?php echo $bloodType['id']; ?>" 
                                                    <?php echo (isset($_POST['blood_type_id']) && $_POST['blood_type_id'] == $bloodType['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($bloodType['type']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="units_needed" class="form-label">Units Needed *</label>
                                        <input type="number" class="form-control" id="units_needed" name="units_needed" 
                                               min="1" max="10" value="<?php echo htmlspecialchars($_POST['units_needed'] ?? ''); ?>" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="urgency_level" class="form-label">Urgency Level *</label>
                                        <select class="form-select" id="urgency_level" name="urgency_level" required>
                                            <option value="">Select Urgency</option>
                                            <option value="low" <?php echo (isset($_POST['urgency_level']) && $_POST['urgency_level'] === 'low') ? 'selected' : ''; ?>>Low</option>
                                            <option value="medium" <?php echo (isset($_POST['urgency_level']) && $_POST['urgency_level'] === 'medium') ? 'selected' : ''; ?>>Medium</option>
                                            <option value="high" <?php echo (isset($_POST['urgency_level']) && $_POST['urgency_level'] === 'high') ? 'selected' : ''; ?>>High</option>
                                            <option value="critical" <?php echo (isset($_POST['urgency_level']) && $_POST['urgency_level'] === 'critical') ? 'selected' : ''; ?>>Critical</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="required_by_date" class="form-label">Required By *</label>
                                        <input type="date" class="form-control" id="required_by_date" name="required_by_date"
                                               value="<?php echo htmlspecialchars($_POST['required_by_date'] ?? ''); ?>" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="hospital_name" class="form-label">Hospital Name *</label>
                                <input type="text" class="form-control" id="hospital_name" name="hospital_name"
                                       value="<?php echo htmlspecialchars($_POST['hospital_name'] ?? ''); ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="hospital_address" class="form-label">Hospital Address *</label>
                                <textarea class="form-control" id="hospital_address" name="hospital_address" rows="2" required><?php echo htmlspecialchars($_POST['hospital_address'] ?? ''); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="hospital_contact" class="form-label">Hospital Contact *</label>
                                <input type="tel" class="form-control" id="hospital_contact" name="hospital_contact"
                                       value="<?php echo htmlspecialchars($_POST['hospital_contact'] ?? ''); ?>" required>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="requests.php" class="btn btn-secondary me-md-2">Cancel</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Create Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
