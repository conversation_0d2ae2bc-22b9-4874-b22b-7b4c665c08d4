<?php
/**
 * Donor Dashboard
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/Donor.php';
require_once '../classes/BloodRequest.php';
require_once '../classes/Notification.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');

// Redirect unified users to the new dashboard
$currentUser = getCurrentUser();
if ($currentUser['user_type'] === 'unified') {
    redirectWithMessage('../dashboard/', 'Please use the unified dashboard for better experience.', 'info');
}

requirePermission(USER_TYPE_DONOR, '../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

// Load donor profile
$donor = new Donor($currentUser['id']);

// Get donor statistics
$donorStats = $donor->getDonationStatistics();

// Get upcoming donations
$upcomingDonations = $donor->getUpcomingDonations();

// Get recent notifications
$notificationResult = Notification::getUserNotifications($currentUser['id'], 1, 5);
$notifications = $notificationResult['notifications'];

// Get matching blood requests
$matchingRequestsResult = $donor->getMatchingBloodRequests(1, 5);
$matchingRequests = $matchingRequestsResult['requests'];

// Get recent donation history
$donationHistoryResult = $donor->getDonationHistory(1, 5);
$recentDonations = $donationHistoryResult['donations'];

// Check eligibility
$isEligible = $donor->isEligibleForDonation();
$daysUntilEligible = $donor->getDaysUntilEligible();

// Get flash message
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donor Dashboard - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 2px 10px rgba(139,0,0,0.3);">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?> - Donor
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart"></i> My Donations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical"></i> Blood Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.php">
                            <i class="fas fa-calendar"></i> My Scheduled Donations
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <?php if (Notification::getUnreadCount($currentUser['id']) > 0): ?>
                                <span class="badge bg-warning notification-badge"><?php echo Notification::getUnreadCount($currentUser['id']); ?></span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <li><h6 class="dropdown-header">Recent Notifications</h6></li>
                            <?php if (empty($notifications)): ?>
                                <li><span class="dropdown-item-text text-muted">No notifications</span></li>
                            <?php else: ?>
                                <?php foreach (array_slice($notifications, 0, 3) as $notification): ?>
                                    <li>
                                        <a class="dropdown-item <?php echo !$notification['is_read'] ? 'fw-bold' : ''; ?>" href="notifications.php">
                                            <div class="d-flex w-100 justify-content-between">
                                                <small><?php echo htmlspecialchars($notification['title']); ?></small>
                                                <small><?php echo timeAgo($notification['created_at']); ?></small>
                                            </div>
                                            <small class="text-muted"><?php echo htmlspecialchars(substr($notification['message'], 0, 50)); ?>...</small>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="notifications.php">View All Notifications</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo $currentUser['first_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card blood-theme">
                    <div class="card-body text-white" style="position: relative; overflow: hidden;">
                        <!-- Blood drop decorative elements -->
                        <div style="position: absolute; top: -10px; right: 20px; font-size: 60px; opacity: 0.1; color: #fff;">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div style="position: absolute; bottom: -15px; left: 30px; font-size: 40px; opacity: 0.1; color: #fff;">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3); font-weight: 700;">
                                    <i class="fas fa-heart" style="color: #FFB6C1; animation: heartbeat 1.5s ease-in-out infinite;"></i>
                                    Welcome back, Kevenzyrel!
                                </h2>
                                <p class="mb-2" style="font-size: 1.1em; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                                    Thank you for being a blood donor. Your contributions save lives!
                                </p>
                                <p class="mb-0" style="font-size: 1.1em;">
                                    <strong style="color: #FFE4E1;">Blood Type:</strong>
                                    <span class="badge" style="background: linear-gradient(45deg, #8B0000, #DC143C); color: white; font-size: 1em; padding: 8px 15px; border: 2px solid #FFB6C1; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">A+</span>
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <?php if ($isEligible): ?>
                                    <div class="alert mb-0" style="background: rgba(255,255,255,0.2); border: 2px solid #90EE90; color: white;">
                                        <i class="fas fa-check-circle" style="color: #90EE90;"></i> You are eligible to donate!
                                        <br>
                                        <a href="schedule.php" class="btn mt-2" style="background: linear-gradient(45deg, #FFB6C1, #FFF); color: #8B0000; border: 2px solid #FFB6C1; font-weight: bold; text-shadow: none;">
                                            <i class="fas fa-calendar-plus"></i> View My Donations
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="alert mb-0" style="background: rgba(255,255,255,0.2); border: 2px solid #FFD700; color: white;">
                                        <i class="fas fa-clock" style="color: #FFD700;"></i>
                                        <?php if ($daysUntilEligible > 0): ?>
                                            You can donate again in <?php echo $daysUntilEligible; ?> days
                                        <?php else: ?>
                                            Please check eligibility requirements
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 4px 15px rgba(139,0,0,0.2);">
                    <div class="card-body position-relative">
                        <h5 class="card-title">Total Donations</h5>
                        <p class="card-text"><?php echo number_format($donorStats['completed_donations']); ?></p>
                        <i class="fas fa-heart card-icon" style="animation: heartbeat 2s ease-in-out infinite;"></i>
                    </div>
                    <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                        <small>Completed donations</small>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #B22222 0%, #DC143C 100%); box-shadow: 0 4px 15px rgba(178,34,34,0.2);">
                    <div class="card-body position-relative">
                        <h5 class="card-title">Units Donated</h5>
                        <p class="card-text"><?php echo number_format($donorStats['total_units_donated']); ?></p>
                        <i class="fas fa-tint card-icon" style="animation: bloodDrop 3s ease-in-out infinite;"></i>
                    </div>
                    <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                        <small>Total units</small>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #800000 0%, #B22222 100%); box-shadow: 0 4px 15px rgba(128,0,0,0.2);">
                    <div class="card-body position-relative">
                        <h5 class="card-title">Scheduled</h5>
                        <p class="card-text"><?php echo number_format($donorStats['scheduled_donations']); ?></p>
                        <i class="fas fa-calendar card-icon"></i>
                    </div>
                    <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                        <small>Upcoming donations</small>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #A0522D 0%, #8B0000 100%); box-shadow: 0 4px 15px rgba(160,82,45,0.2);">
                    <div class="card-body position-relative">
                        <h5 class="card-title">Matching Requests</h5>
                        <p class="card-text"><?php echo number_format(count($matchingRequests)); ?></p>
                        <i class="fas fa-hand-holding-medical card-icon"></i>
                    </div>
                    <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                        <small>Available to help</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <!-- Upcoming Donations -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-calendar-alt"></i> Upcoming Donations</h5>
                            <a href="donations.php" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($upcomingDonations)): ?>
                            <p class="text-muted text-center">No upcoming donations scheduled.</p>
                            <div class="text-center">
                                <a href="schedule.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Contact Admin
                                </a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($upcomingDonations as $donation): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div>
                                        <strong><?php echo formatDate($donation['donation_date']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($donation['location']); ?>
                                        </small>
                                        <br>
                                        <span class="badge bg-info"><?php echo htmlspecialchars($donation['blood_type']); ?></span>
                                    </div>
                                    <div>
                                        <span class="badge status-<?php echo $donation['status']; ?>">
                                            <?php echo ucfirst($donation['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Matching Blood Requests -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-hand-holding-medical"></i> Matching Blood Requests</h5>
                            <a href="requests.php" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($matchingRequests)): ?>
                            <p class="text-muted text-center">No matching blood requests at the moment.</p>
                        <?php else: ?>
                            <?php foreach ($matchingRequests as $request): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div>
                                        <strong><?php echo htmlspecialchars($request['recipient_first_name'] . ' ' . $request['recipient_last_name']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-hospital"></i> <?php echo htmlspecialchars($request['hospital_name']); ?>
                                        </small>
                                        <br>
                                        <span class="badge bg-danger"><?php echo htmlspecialchars($request['blood_type']); ?></span>
                                        <span class="badge urgency-<?php echo $request['urgency_level']; ?>">
                                            <?php echo strtoupper($request['urgency_level']); ?>
                                        </span>
                                    </div>
                                    <div class="text-end">
                                        <strong><?php echo $request['units_needed']; ?> units</strong>
                                        <br>
                                        <small class="text-muted">
                                            Required by: <?php echo formatDate($request['required_by_date']); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Donations -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-history"></i> Recent Donation History</h5>
                            <a href="donations.php" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentDonations)): ?>
                            <p class="text-muted text-center">No donation history available.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Location</th>
                                            <th>Blood Type</th>
                                            <th>Units</th>
                                            <th>Status</th>
                                            <th>For Request</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentDonations as $donation): ?>
                                            <tr>
                                                <td><?php echo formatDate($donation['donation_date']); ?></td>
                                                <td><?php echo htmlspecialchars($donation['location']); ?></td>
                                                <td>
                                                    <span class="badge bg-danger"><?php echo htmlspecialchars($donation['blood_type']); ?></span>
                                                </td>
                                                <td><?php echo $donation['units_donated']; ?></td>
                                                <td>
                                                    <span class="badge status-<?php echo $donation['status']; ?>">
                                                        <?php echo ucfirst($donation['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($donation['request_id']): ?>
                                                        <small class="text-muted">
                                                            For: <?php echo htmlspecialchars($donation['recipient_first_name'] . ' ' . $donation['recipient_last_name']); ?>
                                                            <br>
                                                            Hospital: <?php echo htmlspecialchars($donation['hospital_name']); ?>
                                                        </small>
                                                    <?php else: ?>
                                                        <span class="text-muted">General donation</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // Auto-refresh dashboard every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
