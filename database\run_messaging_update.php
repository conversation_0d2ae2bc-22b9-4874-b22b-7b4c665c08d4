<?php
/**
 * Run Messaging System Database Update
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();

// Only allow running from command line or admin access
if (php_sapi_name() !== 'cli') {
    // Check if user is admin when running from web
    if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
        die('Access denied. Admin privileges required.');
    }
}

echo "=== Blood Donation System - Messaging Update ===\n";
echo "Updating database schema for restricted messaging system...\n\n";

try {
    $db = Database::getInstance();
    
    // Read the SQL file
    $sqlFile = __DIR__ . '/update_messaging_schema.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    if ($sql === false) {
        throw new Exception("Failed to read SQL file");
    }
    
    echo "Executing messaging schema update...\n";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', preg_split('/;(?=(?:[^\']*\'[^\']*\')*[^\']*$)/', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt) && !preg_match('/^\s*USE\s+/', $stmt);
        }
    );
    
    $executedCount = 0;
    $skippedCount = 0;
    
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $db->getConnection()->exec($statement);
                $executedCount++;
                echo ".";
            } catch (PDOException $e) {
                // Some statements might fail if they already exist, which is okay
                if (strpos($e->getMessage(), 'already exists') !== false || 
                    strpos($e->getMessage(), 'Duplicate') !== false ||
                    strpos($e->getMessage(), 'Unknown column') !== false ||
                    strpos($e->getMessage(), 'Duplicate key') !== false) {
                    $skippedCount++;
                    echo "s"; // skipped
                } else {
                    throw $e;
                }
            }
        }
    }
    
    echo "\n\nMessaging schema update completed successfully!\n";
    echo "Executed $executedCount SQL statements.\n";
    echo "Skipped $skippedCount statements (already exist).\n\n";
    
    // Verify the update
    echo "Verifying messaging schema update...\n";
    
    // Check if new tables exist
    $tables = ['message_threads', 'messaging_permissions', 'message_reports', 'message_attachments'];
    foreach ($tables as $table) {
        $exists = $db->fetch("SHOW TABLES LIKE ?", [$table]);
        if ($exists) {
            echo "✓ $table table created\n";
        } else {
            echo "✗ $table table missing\n";
        }
    }
    
    // Check if new columns exist
    $columns = $db->fetchAll("SHOW COLUMNS FROM chat_messages LIKE 'thread_id'");
    if (!empty($columns)) {
        echo "✓ thread_id column added to chat_messages\n";
    } else {
        echo "✗ thread_id column missing from chat_messages\n";
    }
    
    // Check if view exists
    $views = $db->fetchAll("SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_blood_donation_system = 'conversation_summary'");
    if (!empty($views)) {
        echo "✓ conversation_summary view created\n";
    } else {
        echo "✗ conversation_summary view missing\n";
    }
    
    // Check if trigger exists
    $triggers = $db->fetchAll("SHOW TRIGGERS LIKE 'chat_messages'");
    $triggerExists = false;
    foreach ($triggers as $trigger) {
        if ($trigger['Trigger'] === 'update_message_thread_after_insert') {
            $triggerExists = true;
            break;
        }
    }
    
    if ($triggerExists) {
        echo "✓ Message thread trigger created\n";
    } else {
        echo "✗ Message thread trigger missing\n";
    }
    
    echo "\nMessaging schema verification completed.\n";
    echo "The restricted messaging system database is now ready!\n\n";
    
    // Log the update
    logEvent('INFO', 'Messaging schema updated', [
        'executed_statements' => $executedCount,
        'skipped_statements' => $skippedCount
    ]);
    
} catch (Exception $e) {
    echo "\nMessaging schema update failed: " . $e->getMessage() . "\n";
    echo "Database may have been partially modified.\n";
    
    // Log the error
    try {
        logEvent('ERROR', 'Messaging schema update failed', [
            'error' => $e->getMessage()
        ]);
    } catch (Exception $logError) {
        // Ignore logging errors during update
    }
    
    exit(1);
}

if (php_sapi_name() !== 'cli') {
    echo "<br><br><a href='../admin/'>Return to Admin Panel</a>";
}
?>
