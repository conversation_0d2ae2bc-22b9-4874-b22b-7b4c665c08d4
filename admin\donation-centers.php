<?php
/**
 * Admin Donation Centers Management
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/DonationCenter.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();
$errors = [];
$success = '';
$adminId = getCurrentUser()['id'];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';

        try {
            switch ($action) {
                case 'add':
                    $data = [
                        'name' => sanitizeInput($_POST['name'] ?? ''),
                        'address' => sanitizeInput($_POST['address'] ?? ''),
                        'city' => sanitizeInput($_POST['city'] ?? ''),
                        'phone' => sanitizeInput($_POST['phone'] ?? ''),
                        'email' => sanitizeInput($_POST['email'] ?? ''),
                        'operating_hours' => sanitizeInput($_POST['operating_hours'] ?? ''),
                        'capacity' => (int)($_POST['capacity'] ?? 0),
                        'facilities' => $_POST['facilities'] ?? [],
                        'is_active' => isset($_POST['is_active'])
                    ];

                    if (empty($data['name']) || empty($data['address']) || empty($data['city'])) {
                        throw new Exception('Name, address, and city are required.');
                    }

                    DonationCenter::create($data);
                    $success = 'Donation center added successfully!';
                    break;
                    
                case 'edit':
                    $id = (int)($_POST['center_id'] ?? 0);
                    $data = [
                        'name' => sanitizeInput($_POST['name'] ?? ''),
                        'address' => sanitizeInput($_POST['address'] ?? ''),
                        'city' => sanitizeInput($_POST['city'] ?? ''),
                        'phone' => sanitizeInput($_POST['phone'] ?? ''),
                        'email' => sanitizeInput($_POST['email'] ?? ''),
                        'operating_hours' => sanitizeInput($_POST['operating_hours'] ?? ''),
                        'capacity' => (int)($_POST['capacity'] ?? 0),
                        'facilities' => $_POST['facilities'] ?? [],
                        'is_active' => isset($_POST['is_active'])
                    ];

                    if ($id <= 0 || empty($data['name']) || empty($data['address']) || empty($data['city'])) {
                        throw new Exception('Invalid data provided.');
                    }

                    $center = new DonationCenter($id);
                    $center->update($data);
                    $success = 'Donation center updated successfully!';
                    break;
                    
                case 'archive':
                    $id = (int)($_POST['center_id'] ?? 0);
                    $reason = sanitizeInput($_POST['reason'] ?? '');

                    if ($id <= 0) {
                        throw new Exception('Invalid center ID.');
                    }

                    if (empty($reason)) {
                        throw new Exception('Archive reason is required.');
                    }

                    $center = new DonationCenter($id);
                    $center->archive($adminId, $reason);
                    $success = 'Donation center archived successfully!';
                    break;

                case 'restore':
                    $id = (int)($_POST['center_id'] ?? 0);
                    $reason = sanitizeInput($_POST['reason'] ?? '');

                    if ($id <= 0) {
                        throw new Exception('Invalid center ID.');
                    }

                    $center = new DonationCenter($id);
                    $center->restore($adminId, $reason);
                    $success = 'Donation center restored successfully!';
                    break;
            }
        } catch (Exception $e) {
            $errors[] = $e->getMessage();
        }
    }
}

// Get filters
$filters = [
    'search' => $_GET['search'] ?? '',
    'city' => $_GET['city'] ?? '',
    'active_only' => isset($_GET['active_only'])
];

// Get all donation centers (excluding archived by default)
$centers = DonationCenter::getAll(false, $filters);

// Get cities for filter (temporarily without archive filter)
$cities = $db->fetchAll("SELECT DISTINCT city FROM donation_centers ORDER BY city");

$csrfToken = generateCSRFToken();
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donation Centers - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-tint"></i> <?php echo APP_NAME; ?> - Admin
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users"></i> Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart"></i> Donations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="donation-centers.php">
                            <i class="fas fa-hospital"></i> Donation Centers
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical"></i> Blood Requests
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="fas fa-hospital"></i> Donation Centers Management</h2>
                <p class="text-muted">Manage blood donation centers and locations where donors can donate blood.</p>
            </div>
            <div class="col-md-4 text-end">
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addCenterModal">
                    <i class="fas fa-plus"></i> Add Donation Center
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h6><i class="fas fa-filter"></i> Filter Centers</h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" placeholder="Name, address, city..."
                               value="<?php echo htmlspecialchars($filters['search']); ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">City</label>
                        <select name="city" class="form-select">
                            <option value="">All Cities</option>
                            <?php foreach ($cities as $city): ?>
                                <option value="<?php echo htmlspecialchars($city['city']); ?>"
                                        <?php echo $filters['city'] === $city['city'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($city['city']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" name="active_only" id="active_only"
                                   <?php echo $filters['active_only'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="active_only">
                                Active Centers Only
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Centers Table -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> Donation Centers (<?php echo count($centers); ?>)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($centers)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No donation centers found. Add your first donation center to get started.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Location</th>
                                    <th>Contact</th>
                                    <th>Capacity</th>
                                    <th>Facilities</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($centers as $center): ?>
                                    <tr>
                                        <td><?php echo $center['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($center['name']); ?></strong>
                                            <?php if ($center['operating_hours']): ?>
                                                <br><small class="text-muted"><i class="fas fa-clock"></i> <?php echo htmlspecialchars($center['operating_hours']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($center['city']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($center['address']); ?></small>
                                        </td>
                                        <td>
                                            <?php if ($center['phone']): ?>
                                                <i class="fas fa-phone"></i> <?php echo htmlspecialchars($center['phone']); ?>
                                            <?php endif; ?>
                                            <?php if ($center['email']): ?>
                                                <br><small class="text-muted"><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($center['email']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($center['capacity'] > 0): ?>
                                                <span class="badge bg-info"><?php echo $center['capacity']; ?> donors/day</span>
                                            <?php else: ?>
                                                <span class="text-muted">Not set</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $facilities = json_decode($center['facilities'] ?? '[]', true);
                                            if (!is_array($facilities)) $facilities = [];
                                            $facilityIcons = [
                                                'parking' => 'fa-parking',
                                                'wheelchair' => 'fa-wheelchair',
                                                'refreshments' => 'fa-coffee',
                                                'wifi' => 'fa-wifi',
                                                'ac' => 'fa-snowflake',
                                                'medical_staff' => 'fa-user-md',
                                                'mobile' => 'fa-truck'
                                            ];
                                            ?>
                                            <?php if (!empty($facilities)): ?>
                                                <?php foreach ($facilities as $facility): ?>
                                                    <?php if (isset($facilityIcons[$facility])): ?>
                                                        <i class="fas <?php echo $facilityIcons[$facility]; ?> text-primary me-1" title="<?php echo ucfirst(str_replace('_', ' ', $facility)); ?>"></i>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <span class="text-muted">None listed</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($center['is_active']): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" onclick="viewCenter(<?php echo $center['id']; ?>)" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#editModal<?php echo $center['id']; ?>" title="Edit Center">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#archiveModal<?php echo $center['id']; ?>" title="Archive Center">
                                                    <i class="fas fa-archive"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Center Modal -->
    <div class="modal fade" id="addCenterModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Donation Center</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    <input type="hidden" name="action" value="add">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Center Name *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">City *</label>
                                    <input type="text" class="form-control" name="city" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Address *</label>
                            <textarea class="form-control" name="address" rows="2" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Phone</label>
                                    <input type="text" class="form-control" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Operating Hours</label>
                                    <input type="text" class="form-control" name="operating_hours" placeholder="e.g., Mon-Fri 8AM-5PM">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Daily Capacity</label>
                                    <input type="number" class="form-control" name="capacity" min="0" placeholder="Number of donors per day">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Available Facilities</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="facilities[]" value="parking" id="parking">
                                        <label class="form-check-label" for="parking">Parking Available</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="facilities[]" value="wheelchair" id="wheelchair">
                                        <label class="form-check-label" for="wheelchair">Wheelchair Accessible</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="facilities[]" value="refreshments" id="refreshments">
                                        <label class="form-check-label" for="refreshments">Refreshments</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="facilities[]" value="wifi" id="wifi">
                                        <label class="form-check-label" for="wifi">Free WiFi</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="facilities[]" value="ac" id="ac">
                                        <label class="form-check-label" for="ac">Air Conditioning</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="facilities[]" value="medical_staff" id="medical_staff">
                                        <label class="form-check-label" for="medical_staff">24/7 Medical Staff</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                Active (Available for donations)
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Add Center</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit and Archive Modals for each center -->
    <?php foreach ($centers as $center): ?>
        <!-- Edit Modal -->
        <div class="modal fade" id="editModal<?php echo $center['id']; ?>" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Donation Center</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="center_id" value="<?php echo $center['id']; ?>">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Center Name *</label>
                                        <input type="text" class="form-control" name="name" value="<?php echo htmlspecialchars($center['name']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">City *</label>
                                        <input type="text" class="form-control" name="city" value="<?php echo htmlspecialchars($center['city']); ?>" required>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Address *</label>
                                <textarea class="form-control" name="address" rows="2" required><?php echo htmlspecialchars($center['address']); ?></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Phone</label>
                                        <input type="text" class="form-control" name="phone" value="<?php echo htmlspecialchars($center['phone']); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($center['email']); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Operating Hours</label>
                                        <input type="text" class="form-control" name="operating_hours" value="<?php echo htmlspecialchars($center['operating_hours']); ?>" placeholder="e.g., Mon-Fri 8AM-5PM">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Daily Capacity</label>
                                        <input type="number" class="form-control" name="capacity" min="0" value="<?php echo $center['capacity']; ?>" placeholder="Number of donors per day">
                                    </div>
                                </div>
                            </div>
                            <?php
                            $facilities = json_decode($center['facilities'] ?? '[]', true);
                            if (!is_array($facilities)) $facilities = [];
                            ?>
                            <div class="mb-3">
                                <label class="form-label">Available Facilities</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="facilities[]" value="parking" id="edit_parking_<?php echo $center['id']; ?>" <?php echo in_array('parking', $facilities) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="edit_parking_<?php echo $center['id']; ?>">Parking Available</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="facilities[]" value="wheelchair" id="edit_wheelchair_<?php echo $center['id']; ?>" <?php echo in_array('wheelchair', $facilities) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="edit_wheelchair_<?php echo $center['id']; ?>">Wheelchair Accessible</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="facilities[]" value="refreshments" id="edit_refreshments_<?php echo $center['id']; ?>" <?php echo in_array('refreshments', $facilities) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="edit_refreshments_<?php echo $center['id']; ?>">Refreshments</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="facilities[]" value="wifi" id="edit_wifi_<?php echo $center['id']; ?>" <?php echo in_array('wifi', $facilities) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="edit_wifi_<?php echo $center['id']; ?>">Free WiFi</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="facilities[]" value="ac" id="edit_ac_<?php echo $center['id']; ?>" <?php echo in_array('ac', $facilities) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="edit_ac_<?php echo $center['id']; ?>">Air Conditioning</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="facilities[]" value="medical_staff" id="edit_medical_staff_<?php echo $center['id']; ?>" <?php echo in_array('medical_staff', $facilities) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="edit_medical_staff_<?php echo $center['id']; ?>">24/7 Medical Staff</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="edit_is_active_<?php echo $center['id']; ?>" <?php echo $center['is_active'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="edit_is_active_<?php echo $center['id']; ?>">
                                    Active (Available for donations)
                                </label>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-success">Update Center</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Archive Modal -->
        <div class="modal fade" id="archiveModal<?php echo $center['id']; ?>" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Archive Donation Center</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="archive">
                        <input type="hidden" name="center_id" value="<?php echo $center['id']; ?>">
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Warning:</strong> Archiving this donation center will make it unavailable for new donations. This action can be undone later if needed.
                            </div>
                            <p><strong>Center:</strong> <?php echo htmlspecialchars($center['name']); ?></p>
                            <p><strong>Location:</strong> <?php echo htmlspecialchars($center['address'] . ', ' . $center['city']); ?></p>
                            <div class="mb-3">
                                <label class="form-label">Archive Reason *</label>
                                <textarea name="reason" class="form-control" rows="3" placeholder="Please provide a reason for archiving this center..." required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-warning">Archive Center</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        function viewCenter(centerId) {
            // You can implement a view details modal or redirect to a details page
            alert('View center details functionality - Center ID: ' + centerId);
        }
    </script>
</body>
</html>
