<?php
/**
 * Mark All Notifications as Read API Endpoint
 * Blood Donation Management System
 */

require_once '../../config/constants.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';
require_once '../../classes/Notification.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session and check authentication
startSecureSession();

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Authentication required'
    ]);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit;
}

$currentUser = getCurrentUser();

try {
    // Mark all notifications as read
    $success = Notification::markAllAsRead($currentUser['id']);
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'unread_count' => 0,
            'message' => 'All notifications marked as read'
        ]);
        
        // Log the action
        logEvent('INFO', 'All notifications marked as read', [
            'user_id' => $currentUser['id']
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to mark all notifications as read'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error'
    ]);
    
    // Log the error
    logEvent('ERROR', 'Mark all notifications as read API error', [
        'user_id' => $currentUser['id'],
        'error' => $e->getMessage()
    ]);
}
?>
