<?php
/**
 * Get Messageable Users API - Restricted Messaging System
 * Blood Donation Management System
 * 
 * Returns list of users that the current user can message based on restrictions:
 * - Regular users can only see administrators
 * - Administrators can see all users
 */

require_once '../../config/constants.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Start session and check authentication
startSecureSession();

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$currentUser = getCurrentUser();

// Get parameters
$search = sanitizeInput($_GET['search'] ?? '');
$page = (int)($_GET['page'] ?? 1);
$limit = (int)($_GET['limit'] ?? 20);
$offset = ($page - 1) * $limit;

// Validate parameters
if ($limit > 100) {
    $limit = 100; // Maximum limit for security
}

try {
    // Get total count
    $total = getMessagableUsersCount($currentUser['id'], $search);
    
    // Get users
    $users = getMessagableUsers($currentUser['id'], $search, $limit, $offset);
    
    // Calculate pagination info
    $totalPages = ceil($total / $limit);
    $hasNextPage = $page < $totalPages;
    $hasPrevPage = $page > 1;
    
    // Format user data for response
    $formattedUsers = [];
    foreach ($users as $user) {
        $formattedUsers[] = [
            'id' => (int)$user['id'],
            'username' => $user['username'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'full_name' => $user['first_name'] . ' ' . $user['last_name'],
            'user_type' => $user['user_type'],
            'is_unified_user' => (bool)$user['is_unified_user'],
            'roles' => $user['roles'] ? explode(',', $user['roles']) : [],
            'profile_photo' => $user['profile_photo'],
            'is_online' => (bool)$user['is_online'],
            'last_activity' => $user['last_activity'],
            'last_seen' => $user['last_seen'],
            'can_chat' => canChatWith($currentUser['id'], $user['id']) // Double-check permissions
        ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'users' => $formattedUsers,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_users' => $total,
                'per_page' => $limit,
                'has_next_page' => $hasNextPage,
                'has_prev_page' => $hasPrevPage
            ],
            'search' => $search,
            'restrictions' => [
                'is_admin' => $currentUser['user_type'] === USER_TYPE_ADMIN,
                'can_message_all' => $currentUser['user_type'] === USER_TYPE_ADMIN,
                'message' => $currentUser['user_type'] === USER_TYPE_ADMIN 
                    ? 'You can message any user in the system'
                    : 'You can only message system administrators'
            ]
        ]
    ]);
    
} catch (Exception $e) {
    logEvent('ERROR', 'Get messageable users failed', [
        'user_id' => $currentUser['id'],
        'search' => $search,
        'error' => $e->getMessage()
    ]);
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Failed to load users'
    ]);
}
?>
