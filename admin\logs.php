<?php
/**
 * Admin Audit Logs Viewer
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/audit.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();

// Export functionality removed for security reasons

// Get filters
$filters = [
    'user_id' => sanitizeInput($_GET['user_id'] ?? ''),
    'action' => sanitizeInput($_GET['action'] ?? ''),
    'date_from' => sanitizeInput($_GET['date_from'] ?? ''),
    'date_to' => sanitizeInput($_GET['date_to'] ?? ''),
    'log_type' => sanitizeInput($_GET['log_type'] ?? 'audit')
];

$page = (int)($_GET['page'] ?? 1);

// Get logs based on type
if ($filters['log_type'] === 'security') {
    $result = getSecurityLogs($page, RECORDS_PER_PAGE, $filters);
} else {
    $result = getAuditLogs($page, RECORDS_PER_PAGE, $filters);
}

$logs = $result['logs'];
$pagination = $result['pagination'];

// Get users for filter dropdown
$users = $db->fetchAll("SELECT id, first_name, last_name, username FROM users WHERE user_type = 'admin' ORDER BY first_name, last_name");

// Log this page access
logAdminAction('VIEW_AUDIT_LOGS', ['filters' => $filters]);

// Get flash message
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audit Logs - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.php">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>

                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="admin-panel-section">
                    <div class="section-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-file-alt"></i> System Audit & Security Logs</h5>
                            <div class="btn-group">
                                <div class="text-muted small me-3">
                                    <i class="fas fa-info-circle"></i> Export disabled for security
                                </div>
                                <a href="index.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="section-body">
                        <!-- Log Type Tabs -->
                        <ul class="nav nav-tabs mb-4">
                            <li class="nav-item">
                                <a class="nav-link <?php echo $filters['log_type'] === 'audit' ? 'active' : ''; ?>" 
                                   href="?<?php echo http_build_query(array_merge($filters, ['log_type' => 'audit'])); ?>">
                                    <i class="fas fa-user-shield"></i> Admin Actions
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $filters['log_type'] === 'security' ? 'active' : ''; ?>" 
                                   href="?<?php echo http_build_query(array_merge($filters, ['log_type' => 'security'])); ?>">
                                    <i class="fas fa-shield-alt"></i> Security Events
                                </a>
                            </li>
                        </ul>

                        <!-- Filters -->
                        <div class="admin-panel-section mb-4">
                            <div class="section-header">
                                <h6><i class="fas fa-filter"></i> Filter Logs</h6>
                            </div>
                            <div class="section-body">
                                <form method="GET" class="row g-3">
                                    <input type="hidden" name="log_type" value="<?php echo htmlspecialchars($filters['log_type']); ?>">
                                    
                                    <div class="col-md-3">
                                        <label class="form-label">User</label>
                                        <select name="user_id" class="form-select">
                                            <option value="">All Users</option>
                                            <?php foreach ($users as $user): ?>
                                                <option value="<?php echo $user['id']; ?>" 
                                                        <?php echo $filters['user_id'] == $user['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <label class="form-label"><?php echo $filters['log_type'] === 'security' ? 'Event' : 'Action'; ?></label>
                                        <input type="text" name="<?php echo $filters['log_type'] === 'security' ? 'event' : 'action'; ?>" 
                                               class="form-control" placeholder="Search..." 
                                               value="<?php echo htmlspecialchars($filters[$filters['log_type'] === 'security' ? 'event' : 'action']); ?>">
                                    </div>
                                    
                                    <div class="col-md-2">
                                        <label class="form-label">From Date</label>
                                        <input type="date" name="date_from" class="form-control" 
                                               value="<?php echo htmlspecialchars($filters['date_from']); ?>">
                                    </div>
                                    
                                    <div class="col-md-2">
                                        <label class="form-label">To Date</label>
                                        <input type="date" name="date_to" class="form-control" 
                                               value="<?php echo htmlspecialchars($filters['date_to']); ?>">
                                    </div>
                                    
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-danger">
                                                <i class="fas fa-search"></i> Filter
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Logs Table -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th><?php echo $filters['log_type'] === 'security' ? 'Event' : 'Action'; ?></th>
                                        <?php if ($filters['log_type'] === 'security'): ?>
                                            <th>Severity</th>
                                        <?php endif; ?>
                                        <th>Details</th>
                                        <th>IP Address</th>
                                        <th>Date/Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($logs)): ?>
                                        <tr>
                                            <td colspan="<?php echo $filters['log_type'] === 'security' ? '7' : '6'; ?>" class="text-center text-muted py-4">
                                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                                <br>No logs found matching your criteria
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($logs as $log): ?>
                                            <tr>
                                                <td><span class="badge bg-light text-dark"><?php echo $log['id']; ?></span></td>
                                                <td>
                                                    <?php if ($log['username']): ?>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($log['first_name'] . ' ' . $log['last_name']); ?></div>
                                                        <small class="text-muted">@<?php echo htmlspecialchars($log['username']); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">System</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <code><?php echo htmlspecialchars($filters['log_type'] === 'security' ? $log['event'] : $log['action']); ?></code>
                                                </td>
                                                <?php if ($filters['log_type'] === 'security'): ?>
                                                    <td>
                                                        <?php
                                                        $severityClass = [
                                                            'DEBUG' => 'secondary',
                                                            'INFO' => 'info',
                                                            'WARNING' => 'warning',
                                                            'ERROR' => 'danger',
                                                            'CRITICAL' => 'danger'
                                                        ];
                                                        ?>
                                                        <span class="badge bg-<?php echo $severityClass[$log['severity']] ?? 'secondary'; ?>">
                                                            <?php echo $log['severity']; ?>
                                                        </span>
                                                    </td>
                                                <?php endif; ?>
                                                <td>
                                                    <?php if ($log['details']): ?>
                                                        <button class="btn btn-sm btn-outline-info" 
                                                                onclick="showDetails('<?php echo htmlspecialchars(json_encode($log['details'])); ?>')">
                                                            <i class="fas fa-eye"></i> View
                                                        </button>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><code><?php echo htmlspecialchars($log['ip_address']); ?></code></td>
                                                <td>
                                                    <div class="small">
                                                        <?php echo formatDate($log['created_at']); ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($pagination['total_pages'] > 1): ?>
                            <nav aria-label="Logs pagination">
                                <?php echo generatePaginationHTML($pagination, 'logs.php?' . http_build_query($filters)); ?>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Details Modal -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Log Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="detailsContent" class="bg-light p-3 rounded"></pre>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showDetails(details) {
            try {
                const parsed = JSON.parse(details);
                document.getElementById('detailsContent').textContent = JSON.stringify(parsed, null, 2);
                new bootstrap.Modal(document.getElementById('detailsModal')).show();
            } catch (e) {
                document.getElementById('detailsContent').textContent = details;
                new bootstrap.Modal(document.getElementById('detailsModal')).show();
            }
        }
    </script>
</body>
</html>
