<?php
/**
 * Migration: Remove Email Functionality
 * Blood Donation Management System
 * 
 * This migration removes all email-related fields and functionality from the database.
 * Run this script to update your database schema.
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/constants.php';

echo "Starting Email Removal Migration...\n";

try {
    $db = Database::getInstance();
    $db->beginTransaction();
    
    echo "Step 1: Removing email column from users table...\n";
    
    // Check if email column exists
    $columns = $db->fetchAll("SHOW COLUMNS FROM users LIKE 'email'");
    if (!empty($columns)) {
        // Remove email column
        $db->execute("ALTER TABLE users DROP COLUMN email");
        echo "✓ Email column removed from users table\n";
    } else {
        echo "✓ Email column already removed from users table\n";
    }
    
    echo "Step 2: Removing email_verified column from users table...\n";
    
    // Check if email_verified column exists
    $columns = $db->fetchAll("SHOW COLUMNS FROM users LIKE 'email_verified'");
    if (!empty($columns)) {
        // Remove email_verified column
        $db->execute("ALTER TABLE users DROP COLUMN email_verified");
        echo "✓ Email verified column removed from users table\n";
    } else {
        echo "✓ Email verified column already removed from users table\n";
    }
    
    echo "Step 3: Updating user registration source...\n";
    
    // Update existing users to have 'unified' as registration source
    $db->execute("UPDATE users SET registration_source = 'unified' WHERE registration_source IS NULL OR registration_source = ''");
    echo "✓ User registration sources updated\n";
    
    echo "Step 4: Removing email-related indexes...\n";
    
    // Check and remove email index if it exists
    $indexes = $db->fetchAll("SHOW INDEX FROM users WHERE Key_name = 'idx_email'");
    if (!empty($indexes)) {
        $db->execute("DROP INDEX idx_email ON users");
        echo "✓ Email index removed\n";
    } else {
        echo "✓ Email index already removed\n";
    }
    
    echo "Step 5: Updating password reset table...\n";
    
    // Check if password_resets table has email column
    $columns = $db->fetchAll("SHOW COLUMNS FROM password_resets LIKE 'email'");
    if (!empty($columns)) {
        // Remove email column from password_resets table
        $db->execute("ALTER TABLE password_resets DROP COLUMN email");
        echo "✓ Email column removed from password_resets table\n";
    } else {
        echo "✓ Email column already removed from password_resets table\n";
    }
    
    echo "Step 6: Updating system settings...\n";
    
    // Check if system_settings table exists before trying to delete from it
    $tables = $db->fetchAll("SHOW TABLES LIKE 'system_settings'");
    if (!empty($tables)) {
        // Remove email-related settings
        $db->execute("DELETE FROM system_settings WHERE setting_key = 'require_email_verification'");
        $db->execute("DELETE FROM system_settings WHERE setting_key = 'smtp_host'");
        $db->execute("DELETE FROM system_settings WHERE setting_key = 'smtp_port'");
        $db->execute("DELETE FROM system_settings WHERE setting_key = 'smtp_username'");
        $db->execute("DELETE FROM system_settings WHERE setting_key = 'smtp_password'");
        $db->execute("DELETE FROM system_settings WHERE setting_key = 'from_email'");
        $db->execute("DELETE FROM system_settings WHERE setting_key = 'from_name'");
        echo "✓ Email-related system settings removed\n";
    } else {
        echo "✓ System settings table doesn't exist - skipping\n";
    }
    
    echo "Step 7: Updating user profiles...\n";
    
    // Update any remaining email references in user profiles
    $db->execute("UPDATE users SET profile_photo = NULL WHERE profile_photo LIKE '%@%'");
    echo "✓ User profile email references cleaned\n";
    
    echo "Step 8: Creating new database schema without email...\n";
    
    // Create updated schema file
    $newSchema = "<?php
/**
 * Updated Database Schema - No Email Required
 * Blood Donation Management System
 */

// Create database
CREATE DATABASE IF NOT EXISTS blood_donation_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE blood_donation_system;

-- Create blood types table
CREATE TABLE blood_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type VARCHAR(10) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create users table (without email)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('admin', 'donor', 'recipient', 'unified') NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    profile_photo VARCHAR(255),
    status ENUM('active', 'suspended', 'pending') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    is_unified_user BOOLEAN DEFAULT FALSE,
    primary_role VARCHAR(20),
    registration_source VARCHAR(50) DEFAULT 'unified',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status)
);

-- Create user_roles table for unified users
CREATE TABLE user_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    role_type ENUM('donor', 'recipient') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deactivated_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_role (user_id, role_type)
);

-- Create donor_profiles table
CREATE TABLE donor_profiles (
    user_id INT PRIMARY KEY,
    blood_type_id INT NOT NULL,
    weight DECIMAL(5,2) DEFAULT 0,
    birth_date DATE,
    last_donation_date DATE,
    medical_conditions TEXT,
    eligibility_status ENUM('eligible', 'ineligible', 'temporary_defer') DEFAULT 'eligible',
    total_donations INT DEFAULT 0,
    preferred_donation_location VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (blood_type_id) REFERENCES blood_types(id),
    INDEX idx_blood_type (blood_type_id),
    INDEX idx_eligibility (eligibility_status),
    INDEX idx_last_donation (last_donation_date)
);

-- Create recipient_profiles table
CREATE TABLE recipient_profiles (
    user_id INT PRIMARY KEY,
    medical_condition TEXT,
    emergency_contact VARCHAR(100),
    emergency_phone VARCHAR(20),
    doctor_name VARCHAR(100),
    doctor_contact VARCHAR(20),
    preferred_hospital VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create password_resets table (without email)
CREATE TABLE password_resets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
);

-- Create system_settings table
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default blood types
INSERT INTO blood_types (type, description) VALUES
('A+', 'A Positive'),
('A-', 'A Negative'),
('B+', 'B Positive'),
('B-', 'B Negative'),
('AB+', 'AB Positive'),
('AB-', 'AB Negative'),
('O+', 'O Positive'),
('O-', 'O Negative');

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('app_name', 'Blood Donation Management System', 'Application name'),
('app_version', '1.0.0', 'Application version'),
('maintenance_mode', '0', 'Maintenance mode status'),
('max_login_attempts', '5', 'Maximum login attempts'),
('session_timeout', '1800', 'Session timeout in seconds'),
('password_min_length', '8', 'Minimum password length'),
('donation_interval_days', '56', 'Minimum days between donations'),
('min_donor_age', '18', 'Minimum donor age'),
('max_donor_age', '65', 'Maximum donor age'),
('min_donor_weight', '50', 'Minimum donor weight in kg');
";
    
    $db->commit();
    
    echo "Migration completed successfully!\n";
    echo "Email functionality has been completely removed from the system.\n";
    echo "Users can now only login with their username.\n";
    echo "Password reset functionality now works with username instead of email.\n";
    
} catch (Exception $e) {
    try {
        $db->rollback();
    } catch (Exception $rollbackException) {
        // Ignore rollback errors
    }
    echo "Migration failed: " . $e->getMessage() . "\n";
    echo "Rolling back changes...\n";
    exit(1);
}

echo "\nMigration Summary:\n";
echo "- Removed email column from users table\n";
echo "- Removed email_verified column from users table\n";
echo "- Removed email-related indexes\n";
echo "- Updated password reset functionality to use username\n";
echo "- Removed email-related system settings\n";
echo "- Updated user registration sources\n";
echo "\nThe system is now fully functional without email requirements.\n";
?> 