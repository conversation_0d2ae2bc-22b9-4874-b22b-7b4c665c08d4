<?php
/**
 * Email Service Class
 * Blood Donation Management System
 */

require_once __DIR__ . '/../config/email.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../includes/functions.php';

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService {
    private $mailer;
    
    public function __construct() {
        $this->mailer = new PHPMailer(true);
        $this->configureMailer();
    }
    
    /**
     * Configure PHPMailer settings
     */
    private function configureMailer() {
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = SMTP_HOST;
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = SMTP_USERNAME;
            $this->mailer->Password = SMTP_PASSWORD;
            $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $this->mailer->Port = SMTP_PORT;
            
            // Default sender
            $this->mailer->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
            
            // Enable HTML
            $this->mailer->isHTML(true);
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Email configuration failed', ['error' => $e->getMessage()]);
            throw new Exception('Email service configuration failed');
        }
    }
    
    /**
     * Send registration confirmation email
     */
    public function sendRegistrationConfirmation($email, $name, $userType) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email, $name);
            
            $this->mailer->Subject = 'Welcome to ' . APP_NAME . ' - Registration Confirmed';
            
            $body = $this->getEmailTemplate('registration_confirmation', [
                'name' => $name,
                'user_type' => $userType,
                'app_name' => APP_NAME,
                'login_url' => BASE_URL . '/login.php',
                'dashboard_url' => BASE_URL . '/' . $userType . '/'
            ]);
            
            $this->mailer->Body = $body;
            
            $this->mailer->send();
            
            logEvent('INFO', 'Registration confirmation email sent', [
                'email' => $email,
                'user_type' => $userType
            ]);
            
            return true;
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Registration confirmation email failed', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordReset($email, $name, $token) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email, $name);
            
            $this->mailer->Subject = 'Password Reset - ' . APP_NAME;
            
            $resetUrl = BASE_URL . '/reset-password.php?token=' . $token;
            
            $body = $this->getEmailTemplate('password_reset', [
                'name' => $name,
                'app_name' => APP_NAME,
                'reset_url' => $resetUrl,
                'expiry_hours' => PASSWORD_RESET_EXPIRY / 3600
            ]);
            
            $this->mailer->Body = $body;
            
            $this->mailer->send();
            
            logEvent('INFO', 'Password reset email sent', ['email' => $email]);
            
            return true;
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Password reset email failed', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Send blood request notification to donors
     */
    public function sendBloodRequestNotification($donorEmail, $donorName, $requestDetails) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($donorEmail, $donorName);
            
            $this->mailer->Subject = 'Urgent Blood Request - ' . $requestDetails['blood_type'] . ' Needed';
            
            $body = $this->getEmailTemplate('blood_request_notification', [
                'donor_name' => $donorName,
                'recipient_name' => $requestDetails['recipient_name'],
                'blood_type' => $requestDetails['blood_type'],
                'units_needed' => $requestDetails['units_needed'],
                'urgency_level' => $requestDetails['urgency_level'],
                'hospital_name' => $requestDetails['hospital_name'],
                'required_by_date' => $requestDetails['required_by_date'],
                'app_name' => APP_NAME,
                'dashboard_url' => BASE_URL . '/donor/',
                'schedule_url' => BASE_URL . '/donor/schedule.php?request_id=' . $requestDetails['request_id']
            ]);
            
            $this->mailer->Body = $body;
            
            $this->mailer->send();
            
            logEvent('INFO', 'Blood request notification sent', [
                'donor_email' => $donorEmail,
                'request_id' => $requestDetails['request_id']
            ]);
            
            return true;
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Blood request notification failed', [
                'donor_email' => $donorEmail,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Send donation confirmation email
     */
    public function sendDonationConfirmation($donorEmail, $donorName, $donationDetails) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($donorEmail, $donorName);
            
            $this->mailer->Subject = 'Donation Scheduled - ' . APP_NAME;
            
            $body = $this->getEmailTemplate('donation_confirmation', [
                'donor_name' => $donorName,
                'donation_date' => $donationDetails['donation_date'],
                'location' => $donationDetails['location'],
                'blood_type' => $donationDetails['blood_type'],
                'app_name' => APP_NAME,
                'dashboard_url' => BASE_URL . '/donor/',
                'contact_phone' => CONTACT_PHONE
            ]);
            
            $this->mailer->Body = $body;
            
            $this->mailer->send();
            
            logEvent('INFO', 'Donation confirmation sent', [
                'donor_email' => $donorEmail,
                'donation_id' => $donationDetails['donation_id']
            ]);
            
            return true;
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Donation confirmation failed', [
                'donor_email' => $donorEmail,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Send blood request status update
     */
    public function sendRequestStatusUpdate($recipientEmail, $recipientName, $requestDetails, $status) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($recipientEmail, $recipientName);
            
            $statusMessages = [
                REQUEST_STATUS_APPROVED => 'Approved',
                REQUEST_STATUS_FULFILLED => 'Fulfilled',
                REQUEST_STATUS_CANCELLED => 'Cancelled'
            ];
            
            $this->mailer->Subject = 'Blood Request ' . $statusMessages[$status] . ' - ' . APP_NAME;
            
            $body = $this->getEmailTemplate('request_status_update', [
                'recipient_name' => $recipientName,
                'request_id' => $requestDetails['request_id'],
                'blood_type' => $requestDetails['blood_type'],
                'units_needed' => $requestDetails['units_needed'],
                'status' => $statusMessages[$status],
                'status_message' => $this->getStatusMessage($status),
                'app_name' => APP_NAME,
                'dashboard_url' => BASE_URL . '/recipient/',
                'admin_notes' => $requestDetails['admin_notes'] ?? ''
            ]);
            
            $this->mailer->Body = $body;
            
            $this->mailer->send();
            
            logEvent('INFO', 'Request status update sent', [
                'recipient_email' => $recipientEmail,
                'request_id' => $requestDetails['request_id'],
                'status' => $status
            ]);
            
            return true;
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Request status update failed', [
                'recipient_email' => $recipientEmail,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Send general notification email
     */
    public function sendNotification($email, $name, $subject, $message) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email, $name);
            
            $this->mailer->Subject = $subject . ' - ' . APP_NAME;
            
            $body = $this->getEmailTemplate('general_notification', [
                'name' => $name,
                'subject' => $subject,
                'message' => $message,
                'app_name' => APP_NAME,
                'dashboard_url' => BASE_URL
            ]);
            
            $this->mailer->Body = $body;
            
            $this->mailer->send();
            
            logEvent('INFO', 'General notification sent', ['email' => $email]);
            
            return true;
            
        } catch (Exception $e) {
            logEvent('ERROR', 'General notification failed', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Get email template
     */
    private function getEmailTemplate($template, $variables = []) {
        $templatePath = __DIR__ . '/../templates/email/' . $template . '.html';
        
        if (!file_exists($templatePath)) {
            // Return basic template if specific template not found
            return $this->getBasicTemplate($variables);
        }
        
        $content = file_get_contents($templatePath);
        
        // Replace variables
        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }
        
        return $content;
    }
    
    /**
     * Get basic email template
     */
    private function getBasicTemplate($variables) {
        $name = $variables['name'] ?? 'User';
        $message = $variables['message'] ?? 'Thank you for using our service.';
        $appName = $variables['app_name'] ?? APP_NAME;
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>{$appName}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #d32f2f; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>{$appName}</h1>
                </div>
                <div class='content'>
                    <p>Dear {$name},</p>
                    <p>{$message}</p>
                    <p>Best regards,<br>The {$appName} Team</p>
                </div>
                <div class='footer'>
                    <p>This is an automated message. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * Get status message for request updates
     */
    private function getStatusMessage($status) {
        switch ($status) {
            case REQUEST_STATUS_APPROVED:
                return 'Your blood request has been approved and we are now looking for compatible donors.';
            case REQUEST_STATUS_FULFILLED:
                return 'Great news! Your blood request has been fulfilled. Please coordinate with the hospital for the transfusion.';
            case REQUEST_STATUS_CANCELLED:
                return 'Your blood request has been cancelled. If you have questions, please contact our support team.';
            default:
                return 'Your blood request status has been updated.';
        }
    }
}
?>
