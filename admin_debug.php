<?php
/**
 * Admin Debug Page
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Simulate admin login
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_type'] = 'admin';
$_SESSION['first_name'] = 'System';
$_SESSION['last_name'] = 'Administrator';
$_SESSION['logged_in'] = true;
$_SESSION['last_activity'] = time();

echo "<h2>Admin Debug Page</h2>";
echo "<p>Session data set for admin user.</p>";

// Test database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=blood_donation_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Test basic queries
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "<p>Total users: " . $result['count'] . "</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM blood_types");
    $result = $stmt->fetch();
    echo "<p>Total blood types: " . $result['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='index.php'>Try accessing admin dashboard</a></p>";
?> 