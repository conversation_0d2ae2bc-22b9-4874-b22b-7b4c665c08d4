<?php
/**
 * Donor Profile Management
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../classes/Donor.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_DONOR, '../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

// Load donor profile
$donor = new Donor($currentUser['id']);

// Get blood types
$bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");

$errors = [];
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $profileData = [
                    'first_name' => sanitizeInput($_POST['first_name'] ?? ''),
                    'last_name' => sanitizeInput($_POST['last_name'] ?? ''),
                    'phone' => sanitizeInput($_POST['phone'] ?? ''),
                    'address' => sanitizeInput($_POST['address'] ?? ''),
                    'weight' => sanitizeInput($_POST['weight'] ?? ''),
                    'blood_type_id' => (int)($_POST['blood_type_id'] ?? 0),
                    'birth_date' => sanitizeInput($_POST['birth_date'] ?? ''),
                    'medical_conditions' => sanitizeInput($_POST['medical_conditions'] ?? '')
                ];
                
                $validation = validateProfileUpdate($profileData, USER_TYPE_DONOR);
                
                if ($validation->isValid) {
                    try {
                        $donor->updateProfile($profileData);
                        $success = SUCCESS_MESSAGES['PROFILE_UPDATED'];
                        
                        // Reload donor data
                        $donor = new Donor($currentUser['id']);
                        
                    } catch (Exception $e) {
                        $errors[] = $e->getMessage();
                    }
                } else {
                    foreach ($validation->getErrors() as $field => $fieldErrors) {
                        $errors = array_merge($errors, $fieldErrors);
                    }
                }
                break;
                
            case 'change_password':
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';
                
                if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                    $errors[] = 'All password fields are required';
                } elseif ($newPassword !== $confirmPassword) {
                    $errors[] = 'New passwords do not match';
                } elseif (!validatePassword($newPassword)) {
                    $errors[] = ERROR_MESSAGES['WEAK_PASSWORD'];
                } else {
                    try {
                        $donor->changePassword($currentPassword, $newPassword);
                        $success = SUCCESS_MESSAGES['PASSWORD_CHANGED'];
                    } catch (Exception $e) {
                        $errors[] = $e->getMessage();
                    }
                }
                break;
                
            case 'upload_photo':
                if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] === UPLOAD_ERR_OK) {
                    $validation = validateFileUpload($_FILES['profile_photo'], ALLOWED_IMAGE_TYPES);
                    
                    if ($validation->isValid) {
                        try {
                            $fileName = $donor->uploadProfilePhoto($_FILES['profile_photo']);
                            $success = 'Profile photo updated successfully';
                        } catch (Exception $e) {
                            $errors[] = $e->getMessage();
                        }
                    } else {
                        foreach ($validation->getErrors() as $field => $fieldErrors) {
                            $errors = array_merge($errors, $fieldErrors);
                        }
                    }
                } else {
                    $errors[] = 'Please select a photo to upload';
                }
                break;
        }
    }
}

// Get flash message
$flash = getFlashMessage();
if ($flash) {
    if ($flash['type'] === 'success') {
        $success = $flash['message'];
    } else {
        $errors[] = $flash['message'];
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?> - Donor
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="profile.php">
                            <i class="fas fa-user"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart"></i> My Donations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical"></i> Blood Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.php">
                            <i class="fas fa-calendar"></i> My Scheduled Donations
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo $currentUser['first_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Photo -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-camera"></i> Profile Photo</h5>
                    </div>
                    <div class="card-body text-center">
                        <?php if ($donor->getProfilePhoto()): ?>
                            <img src="../uploads/<?php echo htmlspecialchars($donor->getProfilePhoto()); ?>" 
                                 class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                        <?php else: ?>
                            <div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                                 style="width: 150px; height: 150px;">
                                <i class="fas fa-user fa-4x text-white"></i>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                            <input type="hidden" name="action" value="upload_photo">
                            
                            <div class="mb-3">
                                <input type="file" class="form-control" name="profile_photo" accept="image/*" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Upload Photo
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Donor Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Donor Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-6"><strong>Blood Type:</strong></div>
                            <div class="col-sm-6">
                                <span class="badge bg-danger"><?php echo htmlspecialchars($donor->getBloodType()); ?></span>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-6"><strong>Total Donations:</strong></div>
                            <div class="col-sm-6"><?php echo number_format($donor->getTotalDonations()); ?></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-6"><strong>Last Donation:</strong></div>
                            <div class="col-sm-6">
                                <?php echo $donor->getLastDonationDate() ? formatDate($donor->getLastDonationDate()) : 'Never'; ?>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-6"><strong>Eligibility:</strong></div>
                            <div class="col-sm-6">
                                <span class="badge bg-<?php echo $donor->getEligibilityStatus() === 'eligible' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($donor->getEligibilityStatus()); ?>
                                </span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6"><strong>Member Since:</strong></div>
                            <div class="col-sm-6"><?php echo formatDate($donor->getCreatedAt()); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Form -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="profileTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">
                                    <i class="fas fa-user"></i> Profile Information
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="password-tab" data-bs-toggle="tab" data-bs-target="#password" type="button" role="tab">
                                    <i class="fas fa-lock"></i> Change Password
                                </button>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card-body">
                        <div class="tab-content" id="profileTabsContent">
                            <!-- Profile Information Tab -->
                            <div class="tab-pane fade show active" id="profile" role="tabpanel">
                                <form method="POST">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="action" value="update_profile">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">First Name *</label>
                                                <input type="text" class="form-control" name="first_name" 
                                                       value="<?php echo htmlspecialchars($donor->getFirstName()); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Last Name *</label>
                                                <input type="text" class="form-control" name="last_name" 
                                                       value="<?php echo htmlspecialchars($donor->getLastName()); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Email Address</label>
                                                <input type="email" class="form-control" value="<?php echo htmlspecialchars($donor->getEmail()); ?>" disabled>
                                                <div class="form-text">Email cannot be changed. Contact admin if needed.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Phone Number</label>
                                                <input type="tel" class="form-control" name="phone" 
                                                       value="<?php echo htmlspecialchars($donor->getPhone()); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Address</label>
                                        <textarea class="form-control" name="address" rows="2"><?php echo htmlspecialchars($donor->getAddress()); ?></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Blood Type *</label>
                                                <select class="form-select" name="blood_type_id" required>
                                                    <?php foreach ($bloodTypes as $bloodType): ?>
                                                        <option value="<?php echo $bloodType['id']; ?>"
                                                                <?php echo $donor->getBloodTypeId() == $bloodType['id'] ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($bloodType['type']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Weight (kg) *</label>
                                                <input type="number" class="form-control" name="weight" 
                                                       value="<?php echo $donor->getWeight(); ?>" 
                                                       min="<?php echo MIN_DONOR_WEIGHT; ?>" step="0.1" required>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Birth Date *</label>
                                                <input type="date" class="form-control" name="birth_date" 
                                                       value="<?php echo $donor->getBirthDate(); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Medical Conditions</label>
                                        <textarea class="form-control" name="medical_conditions" rows="3" 
                                                  placeholder="List any medical conditions, medications, or allergies..."><?php echo htmlspecialchars($donor->getMedicalConditions()); ?></textarea>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Update Profile
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Change Password Tab -->
                            <div class="tab-pane fade" id="password" role="tabpanel">
                                <form method="POST">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="action" value="change_password">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Current Password *</label>
                                        <input type="password" class="form-control" name="current_password" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">New Password *</label>
                                        <input type="password" class="form-control" name="new_password" required>
                                        <div class="form-text">
                                            Password must be at least <?php echo PASSWORD_MIN_LENGTH; ?> characters long 
                                            and contain uppercase, lowercase, and number.
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Confirm New Password *</label>
                                        <input type="password" class="form-control" name="confirm_password" required>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-key"></i> Change Password
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // Password confirmation validation
        document.querySelector('input[name="confirm_password"]').addEventListener('input', function() {
            const newPassword = document.querySelector('input[name="new_password"]').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
