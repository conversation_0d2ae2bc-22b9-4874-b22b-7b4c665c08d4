const { test, expect } = require('@playwright/test');
const DatabaseHelper = require('../utils/database-helper');
const TestDataGenerator = require('../utils/test-data');

test.describe('Automated Registration Process', () => {
  let dbHelper;
  let testResults = {
    testsPassed: 0,
    testsFailed: 0,
    issuesFound: [],
    fixesApplied: [],
    summary: ''
  };

  test.beforeAll(async () => {
    dbHelper = new DatabaseHelper();
    console.log('🚀 Starting automated registration testing...');
  });

  test.afterAll(async () => {
    if (dbHelper) {
      await dbHelper.cleanupTestUsers();
      await dbHelper.disconnect();
    }
    
    // Generate comprehensive test summary
    generateTestSummary();
  });

  test('Complete Registration Flow with Database Auto-Setup', async ({ page }) => {
    console.log('📋 Test 1: Complete Registration Flow with Database Auto-Setup');
    
    try {
      // Step 1: Check database connectivity and schema
      await checkAndSetupDatabase(page);
      
      // Step 2: Navigate to registration page
      await navigateToRegistration(page);
      
      // Step 3: Fill and submit registration form
      const testUser = TestDataGenerator.generateUser({
        username: `test_reg_${Date.now()}`,
        password: 'TestPass123!'
      });
      
      await fillRegistrationForm(page, testUser);
      await submitRegistrationForm(page);
      
      // Step 4: Verify registration success
      await verifyRegistrationSuccess(page, testUser);
      
      testResults.testsPassed++;
      console.log('✅ Registration flow test passed');
      
    } catch (error) {
      testResults.testsFailed++;
      testResults.issuesFound.push(`Registration flow failed: ${error.message}`);
      console.error('❌ Registration flow test failed:', error.message);
      throw error;
    }
  });

  test('Registration Error Handling and Recovery', async ({ page }) => {
    console.log('📋 Test 2: Registration Error Handling and Recovery');
    
    try {
      // Test with invalid data to trigger validation errors
      await page.goto('/register.php');
      
      // Submit empty form to test validation
      await page.click('button[type="submit"]');
      
      // Check for validation errors
      const errorAlert = page.locator('.alert-danger');
      if (await errorAlert.isVisible()) {
        console.log('✅ Form validation working correctly');
        testResults.testsPassed++;
      }
      
      // Test password mismatch
      await page.fill('#username', 'testuser');
      await page.fill('#first_name', 'Test');
      await page.fill('#last_name', 'User');
      await page.fill('#password', 'password123');
      await page.fill('#confirm_password', 'differentpassword');
      await page.click('button[type="submit"]');
      
      // Verify password mismatch is caught
      const passwordError = page.locator('input[name="confirm_password"]:invalid');
      if (await passwordError.count() > 0) {
        console.log('✅ Password validation working correctly');
        testResults.testsPassed++;
      }
      
    } catch (error) {
      testResults.testsFailed++;
      testResults.issuesFound.push(`Error handling test failed: ${error.message}`);
      console.error('❌ Error handling test failed:', error.message);
    }
  });

  test('Database Connection Recovery', async ({ page }) => {
    console.log('📋 Test 3: Database Connection Recovery');
    
    try {
      // Simulate database connection issues by checking current state
      const schemaResults = await dbHelper.verifyDatabaseSchema();
      const missingTables = Object.entries(schemaResults)
        .filter(([table, exists]) => !exists)
        .map(([table]) => table);
      
      if (missingTables.length > 0) {
        console.log(`⚠️ Missing tables detected: ${missingTables.join(', ')}`);
        testResults.issuesFound.push(`Missing database tables: ${missingTables.join(', ')}`);
        
        // Attempt to fix by running database setup
        await runDatabaseSetup(page);
        testResults.fixesApplied.push('Executed database setup scripts');
      }
      
      // Verify database is now working
      const postSetupResults = await dbHelper.verifyDatabaseSchema();
      const stillMissing = Object.entries(postSetupResults)
        .filter(([table, exists]) => !exists)
        .map(([table]) => table);
      
      if (stillMissing.length === 0) {
        console.log('✅ Database schema verification passed');
        testResults.testsPassed++;
      } else {
        throw new Error(`Database setup failed. Still missing: ${stillMissing.join(', ')}`);
      }
      
    } catch (error) {
      testResults.testsFailed++;
      testResults.issuesFound.push(`Database recovery failed: ${error.message}`);
      console.error('❌ Database recovery test failed:', error.message);
    }
  });

  // Helper Functions
  async function checkAndSetupDatabase(page) {
    console.log('🔍 Checking database connectivity...');
    
    try {
      await dbHelper.connect();
      const schemaResults = await dbHelper.verifyDatabaseSchema();
      
      const missingTables = Object.entries(schemaResults)
        .filter(([table, exists]) => !exists)
        .map(([table]) => table);
      
      if (missingTables.length > 0) {
        console.log(`⚠️ Database issues detected. Missing tables: ${missingTables.join(', ')}`);
        testResults.issuesFound.push(`Missing database tables: ${missingTables.join(', ')}`);
        
        // Run database setup
        await runDatabaseSetup(page);
        testResults.fixesApplied.push('Executed database setup to create missing tables');
      } else {
        console.log('✅ Database schema is complete');
      }
      
    } catch (error) {
      console.log('❌ Database connection failed, attempting setup...');
      testResults.issuesFound.push(`Database connection failed: ${error.message}`);
      
      // Try to run database setup
      await runDatabaseSetup(page);
      testResults.fixesApplied.push('Executed database setup due to connection failure');
    }
  }

  async function runDatabaseSetup(page) {
    console.log('🔧 Running database setup scripts...');
    
    try {
      // First run test_db_connection.php
      await page.goto('/test_db_connection.php');
      await page.waitForLoadState('networkidle');
      
      const connectionContent = await page.textContent('body');
      if (connectionContent.includes('Database connection successful')) {
        console.log('✅ Database connection test passed');
      }
      
      // Then run setup_database.php
      await page.goto('/setup_database.php');
      await page.waitForLoadState('networkidle');
      
      const setupContent = await page.textContent('body');
      if (setupContent.includes('Database setup completed successfully')) {
        console.log('✅ Database setup completed successfully');
        testResults.fixesApplied.push('Database schema created successfully');
      } else if (setupContent.includes('Database setup failed')) {
        throw new Error('Database setup script failed');
      }
      
    } catch (error) {
      console.error('❌ Database setup failed:', error.message);
      testResults.issuesFound.push(`Database setup script failed: ${error.message}`);
      throw error;
    }
  }

  async function navigateToRegistration(page) {
    console.log('🌐 Navigating to registration page...');
    
    await page.goto('/register.php');
    await page.waitForLoadState('networkidle');
    
    // Verify page loaded correctly
    await expect(page.locator('h4')).toContainText('Create Account');
    await expect(page.locator('form#registrationForm')).toBeVisible();
    
    console.log('✅ Registration page loaded successfully');
  }

  async function fillRegistrationForm(page, userData) {
    console.log('📝 Filling registration form...');
    
    await page.fill('#username', userData.username);
    await page.fill('#first_name', userData.firstName);
    await page.fill('#last_name', userData.lastName);
    await page.fill('#phone', userData.phone);
    await page.fill('#address', userData.address);
    await page.fill('#password', userData.password);
    await page.fill('#confirm_password', userData.password);
    
    console.log(`✅ Form filled with user: ${userData.username}`);
  }

  async function submitRegistrationForm(page) {
    console.log('📤 Submitting registration form...');
    
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
  }

  async function verifyRegistrationSuccess(page, userData) {
    console.log('✅ Verifying registration success...');
    
    // Check for success scenarios
    const currentUrl = page.url();
    
    if (currentUrl.includes('dashboard')) {
      console.log('✅ Successfully redirected to dashboard');
      
      // Check for success message
      const successMessage = page.locator('.alert-success');
      if (await successMessage.isVisible()) {
        const messageText = await successMessage.textContent();
        if (messageText.includes('Registration successful')) {
          console.log('✅ Success message displayed correctly');
        }
      }
      
      // Verify user was created in database
      const dbUser = await dbHelper.getUserByUsername(userData.username);
      if (dbUser) {
        console.log('✅ User successfully created in database');
        testResults.fixesApplied.push(`User ${userData.username} created successfully`);
      } else {
        throw new Error('User not found in database after registration');
      }
      
    } else {
      // Check for error messages on registration page
      const errorAlert = page.locator('.alert-danger');
      if (await errorAlert.isVisible()) {
        const errorText = await errorAlert.textContent();
        
        if (errorText.includes('Database operation failed')) {
          console.log('⚠️ Database operation failed - attempting recovery...');
          testResults.issuesFound.push('Database operation failed during registration');
          
          // Try database setup and re-test
          await runDatabaseSetup(page);
          await fillRegistrationForm(page, userData);
          await submitRegistrationForm(page);
          
          // Re-verify after fix
          await verifyRegistrationSuccess(page, userData);
        } else {
          throw new Error(`Registration failed with error: ${errorText}`);
        }
      } else {
        throw new Error('Registration did not complete successfully and no error message shown');
      }
    }
  }

  function generateTestSummary() {
    const totalTests = testResults.testsPassed + testResults.testsFailed;
    const successRate = totalTests > 0 ? (testResults.testsPassed / totalTests * 100).toFixed(1) : 0;
    
    testResults.summary = `
📊 AUTOMATED REGISTRATION TEST SUMMARY
=====================================
✅ Tests Passed: ${testResults.testsPassed}
❌ Tests Failed: ${testResults.testsFailed}
📈 Success Rate: ${successRate}%

🔍 Issues Found:
${testResults.issuesFound.length > 0 ? testResults.issuesFound.map(issue => `  • ${issue}`).join('\n') : '  • No issues found'}

🔧 Fixes Applied:
${testResults.fixesApplied.length > 0 ? testResults.fixesApplied.map(fix => `  • ${fix}`).join('\n') : '  • No fixes needed'}

📋 Test Coverage:
  • Registration form validation
  • Database connectivity and schema verification
  • Automatic database setup and recovery
  • Error handling and user feedback
  • Success flow verification
  • Data persistence verification

🎯 Recommendations:
${generateRecommendations()}
`;
    
    console.log(testResults.summary);
  }

  function generateRecommendations() {
    const recommendations = [];
    
    if (testResults.testsFailed > 0) {
      recommendations.push('Review failed tests and address underlying issues');
    }
    
    if (testResults.issuesFound.some(issue => issue.includes('Database'))) {
      recommendations.push('Consider implementing database health checks in production');
    }
    
    if (testResults.fixesApplied.length > 0) {
      recommendations.push('Document the fixes applied for future reference');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('All tests passed successfully - system is functioning correctly');
    }
    
    return recommendations.map(rec => `  • ${rec}`).join('\n');
  }
});
