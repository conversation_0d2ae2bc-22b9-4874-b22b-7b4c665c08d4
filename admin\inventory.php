<?php
/**
 * Admin Blood Inventory Management
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();

// Get blood inventory data
$inventory = $db->fetchAll("SELECT 
    bt.type,
    bt.id,
    COUNT(d.id) as total_donations,
    SUM(CASE WHEN d.status = ? THEN d.units_donated ELSE 0 END) as available_units,
    COUNT(CASE WHEN d.donation_date >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND d.status = ? THEN 1 END) as recent_donations,
    COUNT(CASE WHEN d.donation_date >= DATE_SUB(NOW(), INTERVAL 7 DAY) AND d.status = ? THEN 1 END) as weekly_donations
    FROM blood_types bt
    LEFT JOIN donations d ON bt.id = d.blood_type_id
    GROUP BY bt.id, bt.type
    ORDER BY bt.type", [DONATION_STATUS_COMPLETED, DONATION_STATUS_COMPLETED, DONATION_STATUS_COMPLETED]);

// Get recent donations for activity log
$recentDonations = $db->fetchAll("SELECT 
    d.id, d.donation_date, d.units_donated, d.status,
    bt.type as blood_type,
    u.first_name, u.last_name
    FROM donations d
    JOIN blood_types bt ON d.blood_type_id = bt.id
    JOIN users u ON d.donor_id = u.id
    WHERE d.status = ?
    ORDER BY d.donation_date DESC
    LIMIT 10", [DONATION_STATUS_COMPLETED]);

$pageTitle = "Blood Inventory";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="inventory.php">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="admin-page-title">
                            <i class="fas fa-tint text-danger me-2"></i>Blood Inventory
                        </h2>
                        <p class="text-muted">Monitor blood stock levels and availability</p>
                    </div>
                    <div>
                        <a href="reports.php?type=inventory" class="btn btn-outline-danger">
                            <i class="fas fa-chart-bar me-1"></i> View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Blood Inventory Grid -->
        <div class="row g-4 mb-4">
            <?php foreach ($inventory as $blood): ?>
                <div class="col-md-3">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-header bg-danger text-white text-center">
                            <h4 class="mb-0"><?php echo htmlspecialchars($blood['type']); ?></h4>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <div class="display-4 text-danger fw-bold">
                                    <?php echo $blood['available_units']; ?>
                                </div>
                                <small class="text-muted">Available Units</small>
                            </div>
                            
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="fw-bold text-primary"><?php echo $blood['weekly_donations']; ?></div>
                                    <small class="text-muted">This Week</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold text-success"><?php echo $blood['recent_donations']; ?></div>
                                    <small class="text-muted">This Month</small>
                                </div>
                            </div>
                            
                            <?php 
                            $stockLevel = 'success';
                            $stockText = 'Good Stock';
                            if ($blood['available_units'] < 5) {
                                $stockLevel = 'danger';
                                $stockText = 'Critical Low';
                            } elseif ($blood['available_units'] < 10) {
                                $stockLevel = 'warning';
                                $stockText = 'Low Stock';
                            }
                            ?>
                            
                            <div class="mt-3">
                                <span class="badge bg-<?php echo $stockLevel; ?>"><?php echo $stockText; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Recent Donations Activity -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-history text-primary me-2"></i>Recent Donations Activity
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentDonations)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No recent donations found</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Donor</th>
                                            <th>Blood Type</th>
                                            <th>Units</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentDonations as $donation): ?>
                                            <tr>
                                                <td><?php echo date('M j, Y g:i A', strtotime($donation['donation_date'])); ?></td>
                                                <td><?php echo htmlspecialchars($donation['first_name'] . ' ' . $donation['last_name']); ?></td>
                                                <td>
                                                    <span class="badge bg-danger"><?php echo htmlspecialchars($donation['blood_type']); ?></span>
                                                </td>
                                                <td><?php echo $donation['units_donated']; ?> units</td>
                                                <td>
                                                    <span class="badge bg-success">Completed</span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
