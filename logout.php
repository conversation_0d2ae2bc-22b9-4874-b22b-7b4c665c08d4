<?php
/**
 * Logout Script
 */

session_start();
session_destroy();
session_start();

// Clear all session variables
$_SESSION = array();

// Delete the session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

echo "<h2>Logged Out Successfully</h2>";
echo "<p>Your session has been cleared.</p>";
echo "<p><a href='login.php'>Click here to login again</a></p>";
?>
