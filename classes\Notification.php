<?php
/**
 * Notification Class
 * Blood Donation Management System
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../includes/functions.php';

class Notification {
    private $db;
    private $id;
    private $title;
    private $message;
    private $createdBy;
    private $targetAudience;
    private $isActive;
    private $createdAt;
    
    public function __construct($id = null) {
        $this->db = Database::getInstance();
        
        if ($id) {
            $this->loadNotification($id);
        }
    }
    
    /**
     * Load notification data from database
     */
    private function loadNotification($id) {
        $sql = "SELECT * FROM notifications WHERE id = ?";
        $notificationData = $this->db->fetch($sql, [$id]);
        
        if ($notificationData) {
            $this->id = $notificationData['id'];
            $this->title = $notificationData['title'];
            $this->message = $notificationData['message'];
            $this->createdBy = $notificationData['created_by'];
            $this->targetAudience = $notificationData['target_audience'];
            $this->isActive = $notificationData['is_active'];
            $this->createdAt = $notificationData['created_at'];
        }
    }
    
    /**
     * Create new notification
     */
    public static function create($data) {
        $db = Database::getInstance();
        
        try {
            $db->beginTransaction();
            
            // Insert notification
            $sql = "INSERT INTO notifications (title, message, created_by, target_audience, is_active) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $db->execute($sql, [
                $data['title'],
                $data['message'],
                $data['created_by'],
                $data['target_audience'],
                $data['is_active'] ?? true
            ]);
            
            $notificationId = $db->lastInsertId();
            
            // Create user notifications for target audience
            self::createUserNotifications($db, $notificationId, $data['target_audience']);
            
            $db->commit();
            
            logEvent('INFO', 'Notification created', [
                'notification_id' => $notificationId,
                'created_by' => $data['created_by'],
                'target_audience' => $data['target_audience']
            ]);
            
            return new self($notificationId);
            
        } catch (Exception $e) {
            $db->rollback();
            logEvent('ERROR', 'Notification creation failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Create user notifications for target audience
     */
    private static function createUserNotifications($db, $notificationId, $targetAudience) {
        $whereClause = "WHERE status = ?";
        $params = [USER_STATUS_ACTIVE];
        
        if ($targetAudience === 'donors') {
            $whereClause .= " AND (user_type = ? OR (user_type = 'unified' AND EXISTS (SELECT 1 FROM user_roles WHERE user_id = users.id AND role_type = 'donor' AND is_active = TRUE)))";
            $params[] = USER_TYPE_DONOR;
        } elseif ($targetAudience === 'recipients') {
            $whereClause .= " AND (user_type = ? OR (user_type = 'unified' AND EXISTS (SELECT 1 FROM user_roles WHERE user_id = users.id AND role_type = 'recipient' AND is_active = TRUE)))";
            $params[] = USER_TYPE_RECIPIENT;
        } elseif ($targetAudience === 'all') {
            $whereClause .= " AND (user_type IN (?, ?) OR (user_type = 'unified' AND EXISTS (SELECT 1 FROM user_roles WHERE user_id = users.id AND role_type IN ('donor', 'recipient') AND is_active = TRUE)))";
            $params[] = USER_TYPE_DONOR;
            $params[] = USER_TYPE_RECIPIENT;
        }
        
        // Get target users
        $usersSql = "SELECT id FROM users $whereClause";
        $users = $db->fetchAll($usersSql, $params);
        
        // Insert user notifications
        if (!empty($users)) {
            $insertSql = "INSERT INTO user_notifications (user_id, notification_id) VALUES ";
            $insertParams = [];
            $placeholders = [];
            
            foreach ($users as $user) {
                $placeholders[] = "(?, ?)";
                $insertParams[] = $user['id'];
                $insertParams[] = $notificationId;
            }
            
            $insertSql .= implode(', ', $placeholders);
            $db->execute($insertSql, $insertParams);
        }
    }
    
    /**
     * Update notification
     */
    public function update($data) {
        $sql = "UPDATE notifications SET 
                title = ?, 
                message = ?, 
                target_audience = ?, 
                is_active = ? 
                WHERE id = ?";
        
        $this->db->execute($sql, [
            $data['title'] ?? $this->title,
            $data['message'] ?? $this->message,
            $data['target_audience'] ?? $this->targetAudience,
            $data['is_active'] ?? $this->isActive,
            $this->id
        ]);
        
        // Update object properties
        $this->title = $data['title'] ?? $this->title;
        $this->message = $data['message'] ?? $this->message;
        $this->targetAudience = $data['target_audience'] ?? $this->targetAudience;
        $this->isActive = $data['is_active'] ?? $this->isActive;
        
        logEvent('INFO', 'Notification updated', ['notification_id' => $this->id]);
        
        return true;
    }
    
    /**
     * Activate notification
     */
    public function activate() {
        $sql = "UPDATE notifications SET is_active = 1 WHERE id = ?";
        $this->db->execute($sql, [$this->id]);
        
        $this->isActive = true;
        
        logEvent('INFO', 'Notification activated', ['notification_id' => $this->id]);
        
        return true;
    }
    
    /**
     * Deactivate notification
     */
    public function deactivate() {
        $sql = "UPDATE notifications SET is_active = 0 WHERE id = ?";
        $this->db->execute($sql, [$this->id]);
        
        $this->isActive = false;
        
        logEvent('INFO', 'Notification deactivated', ['notification_id' => $this->id]);
        
        return true;
    }
    
    /**
     * Delete notification
     */
    public function delete() {
        $sql = "DELETE FROM notifications WHERE id = ?";
        $this->db->execute($sql, [$this->id]);
        
        logEvent('INFO', 'Notification deleted', ['notification_id' => $this->id]);
        
        return true;
    }
    
    /**
     * Get notification statistics
     */
    public function getStatistics() {
        $sql = "SELECT 
                COUNT(*) as total_recipients,
                SUM(CASE WHEN is_read = 1 THEN 1 ELSE 0 END) as read_count,
                SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count
                FROM user_notifications 
                WHERE notification_id = ?";
        
        return $this->db->fetch($sql, [$this->id]);
    }
    
    /**
     * Get users who received this notification
     */
    public function getRecipients($page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM user_notifications un 
                     JOIN users u ON un.user_id = u.id 
                     WHERE un.notification_id = ?";
        
        $totalResult = $this->db->fetch($countSql, [$this->id]);
        $total = $totalResult['total'];
        
        // Get recipients
        $sql = "SELECT u.*, un.is_read, un.read_at 
                FROM user_notifications un 
                JOIN users u ON un.user_id = u.id 
                WHERE un.notification_id = ? 
                ORDER BY un.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $recipients = $this->db->fetchAll($sql, [$this->id, $limit, $offset]);
        
        return [
            'recipients' => $recipients,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
    
    /**
     * Send urgent blood request notification to compatible donors
     */
    public static function sendBloodRequestNotification($requestId, $adminId) {
        $db = Database::getInstance();
        
        // Get request details
        $requestSql = "SELECT br.*, bt.type as blood_type 
                       FROM blood_requests br 
                       JOIN blood_types bt ON br.blood_type_id = bt.id 
                       WHERE br.id = ?";
        
        $request = $db->fetch($requestSql, [$requestId]);
        
        if (!$request) {
            throw new Exception('Blood request not found');
        }
        
        // Get compatible donor blood types
        $compatibleTypes = getCompatibleBloodTypes($request['blood_type']);
        
        if (empty($compatibleTypes)) {
            return false;
        }
        
        // Create notification
        $title = "Urgent Blood Request - " . $request['blood_type'];
        $message = "There is an urgent blood request for " . $request['blood_type'] . " blood type. " .
                   "Units needed: " . $request['units_needed'] . ". " .
                   "Urgency: " . strtoupper($request['urgency_level']) . ". " .
                   "Required by: " . formatDate($request['required_by_date']) . ". " .
                   "Please check your dashboard if you are eligible to donate.";
        
        $notificationData = [
            'title' => $title,
            'message' => $message,
            'created_by' => $adminId,
            'target_audience' => 'donors',
            'is_active' => true
        ];
        
        // Create notification but customize user notifications for compatible donors only
        try {
            $db->beginTransaction();
            
            // Insert notification
            $sql = "INSERT INTO notifications (title, message, created_by, target_audience, is_active) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $db->execute($sql, [
                $notificationData['title'],
                $notificationData['message'],
                $notificationData['created_by'],
                $notificationData['target_audience'],
                $notificationData['is_active']
            ]);
            
            $notificationId = $db->lastInsertId();
            
            // Get compatible donors (including unified users with donor role)
            $placeholders = implode(',', array_fill(0, count($compatibleTypes), '?'));
            $donorsSql = "SELECT DISTINCT u.id
                          FROM users u
                          JOIN donor_profiles dp ON u.id = dp.user_id
                          JOIN blood_types bt ON dp.blood_type_id = bt.id
                          WHERE (u.user_type = ? OR (u.user_type = 'unified' AND EXISTS (
                              SELECT 1 FROM user_roles ur
                              WHERE ur.user_id = u.id
                              AND ur.role_type = 'donor'
                              AND ur.is_active = TRUE
                          )))
                          AND u.status = ?
                          AND bt.type IN ($placeholders)
                          AND dp.eligibility_status = ?";

            $donorsParams = array_merge([USER_TYPE_DONOR, USER_STATUS_ACTIVE], $compatibleTypes, ['eligible']);
            $donors = $db->fetchAll($donorsSql, $donorsParams);
            
            // Insert user notifications for compatible donors
            if (!empty($donors)) {
                $insertSql = "INSERT INTO user_notifications (user_id, notification_id) VALUES ";
                $insertParams = [];
                $placeholders = [];
                
                foreach ($donors as $donor) {
                    $placeholders[] = "(?, ?)";
                    $insertParams[] = $donor['id'];
                    $insertParams[] = $notificationId;
                }
                
                $insertSql .= implode(', ', $placeholders);
                $db->execute($insertSql, $insertParams);
            }
            
            $db->commit();
            
            logEvent('INFO', 'Blood request notification sent', [
                'notification_id' => $notificationId,
                'request_id' => $requestId,
                'compatible_donors' => count($donors)
            ]);
            
            return new self($notificationId);
            
        } catch (Exception $e) {
            $db->rollback();
            logEvent('ERROR', 'Blood request notification failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    // Getters
    public function getId() { return $this->id; }
    public function getTitle() { return $this->title; }
    public function getMessage() { return $this->message; }
    public function getCreatedBy() { return $this->createdBy; }
    public function getTargetAudience() { return $this->targetAudience; }
    public function isActive() { return $this->isActive; }
    public function getCreatedAt() { return $this->createdAt; }
    
    /**
     * Get all notifications with pagination
     */
    public static function getAll($page = 1, $limit = RECORDS_PER_PAGE, $filters = []) {
        $db = Database::getInstance();
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if (!empty($filters['target_audience'])) {
            $whereClause .= " AND n.target_audience = ?";
            $params[] = $filters['target_audience'];
        }
        
        if (isset($filters['is_active'])) {
            $whereClause .= " AND n.is_active = ?";
            $params[] = $filters['is_active'];
        }
        
        if (!empty($filters['search'])) {
            $whereClause .= " AND (n.title LIKE ? OR n.message LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm]);
        }
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM notifications n 
                     $whereClause";
        
        $totalResult = $db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get notifications
        $sql = "SELECT n.*, 
                       u.first_name as created_by_first_name, 
                       u.last_name as created_by_last_name
                FROM notifications n
                JOIN users u ON n.created_by = u.id
                $whereClause 
                ORDER BY n.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $notifications = $db->fetchAll($sql, $params);
        
        return [
            'notifications' => $notifications,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
    
    /**
     * Get user notifications
     */
    public static function getUserNotifications($userId, $page = 1, $limit = RECORDS_PER_PAGE, $unreadOnly = false) {
        $db = Database::getInstance();
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE un.user_id = ? AND n.is_active = 1";
        $params = [$userId];
        
        if ($unreadOnly) {
            $whereClause .= " AND un.is_read = 0";
        }
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM user_notifications un 
                     JOIN notifications n ON un.notification_id = n.id 
                     $whereClause";
        
        $totalResult = $db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get notifications
        $sql = "SELECT n.*, un.is_read, un.read_at 
                FROM user_notifications un 
                JOIN notifications n ON un.notification_id = n.id 
                $whereClause 
                ORDER BY n.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $notifications = $db->fetchAll($sql, $params);
        
        return [
            'notifications' => $notifications,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
    
    /**
     * Mark user notification as read
     */
    public static function markAsRead($userId, $notificationId) {
        $db = Database::getInstance();
        
        $sql = "UPDATE user_notifications 
                SET is_read = 1, read_at = NOW() 
                WHERE user_id = ? AND notification_id = ?";
        
        return $db->execute($sql, [$userId, $notificationId]);
    }
    
    /**
     * Mark all user notifications as read
     */
    public static function markAllAsRead($userId) {
        $db = Database::getInstance();
        
        $sql = "UPDATE user_notifications 
                SET is_read = 1, read_at = NOW() 
                WHERE user_id = ? AND is_read = 0";
        
        return $db->execute($sql, [$userId]);
    }
    
    /**
     * Get unread notifications count for user
     */
    public static function getUnreadCount($userId) {
        $db = Database::getInstance();
        
        $sql = "SELECT COUNT(*) as count 
                FROM user_notifications un 
                JOIN notifications n ON un.notification_id = n.id 
                WHERE un.user_id = ? AND un.is_read = 0 AND n.is_active = 1";
        
        $result = $db->fetch($sql, [$userId]);
        return $result['count'];
    }
}
?>
