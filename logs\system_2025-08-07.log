[2025-08-07 06:18:27] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"user3","user_id":8}}
[2025-08-07 06:18:27] INFO: User logged in Context: {"user_id":8,"username":"user3","user_type":"unified","ip_address":"127.0.0.1"}
[2025-08-07 06:18:45] ERROR: Failed to get profile data Context: {"user_id":8,"error":"Database operation failed"}
[2025-08-07 06:19:39] ERROR: Failed to get profile data Context: {"user_id":8,"error":"Database operation failed"}
[2025-08-07 06:20:22] INFO: User logged out Context: {"user_id":8,"username":"user3"}
[2025-08-07 06:21:19] ERROR: Registration failed Context: {"username":"User4","error":"Database operation failed"}
[2025-08-07 06:22:30] ERROR: Registration failed Context: {"username":"user4","error":"Database operation failed"}
[2025-08-07 06:38:10] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin"}}
[2025-08-07 06:38:10] INFO: User logged in Context: {"user_id":1,"username":"admin","user_type":"admin","ip_address":"127.0.0.1"}
[2025-08-07 06:39:44] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin"}}
[2025-08-07 06:39:44] INFO: User logged in Context: {"user_id":1,"username":"admin","user_type":"admin","ip_address":"127.0.0.1"}
[2025-08-07 06:43:02] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin"}}
[2025-08-07 06:43:02] INFO: User logged in Context: {"user_id":1,"username":"admin","user_type":"admin","ip_address":"127.0.0.1"}
[2025-08-07 06:53:54] INFO: Basic user account created Context: {"user_id":"2","username":"johndoe123"}
[2025-08-07 06:54:45] SECURITY: LOGIN_FAILED_USER_NOT_FOUND Context: {"event":"LOGIN_FAILED_USER_NOT_FOUND","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"Use Playwright to create an automated test that navigates to the registration page, fills out the registration form with test data, submits it, and verifies the registration process works correctly. The test should:  1. Navigate to the registration page (register.php) 2. Fill out all required fields (username, password, confirm password, first name, last name, phone, address) 3. Submit the form 4. Check for any error messages, particularly the &quot;Database operation failed&quot; error 5. If the registration fails due to database issues, automatically run the database setup scripts (test_db_connection.php and setup_database.php) to initialize the database 6. Re-test the registration process after database setup 7. Verify successful registration by checking for success messages or redirect to dashboard 8. Fix any issues found during testing, including database schema problems, missing tables, or configuration errors 9. Provide a summary of what was tested, what issues were found, and what fixes were applied  The test should handle common failure scenarios like missing database, incorrect schema, and connection issues, and automatically resolve them where possible."}}
[2025-08-07 06:55:25] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"johndoe123","user_id":2}}
[2025-08-07 06:55:25] INFO: User logged in Context: {"user_id":2,"username":"johndoe123","user_type":"unified","ip_address":"127.0.0.1"}
[2025-08-07 07:04:58] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"johndoe123","user_id":2}}
[2025-08-07 07:04:58] INFO: User logged in Context: {"user_id":2,"username":"johndoe123","user_type":"unified","ip_address":"127.0.0.1"}
[2025-08-07 07:05:49] ERROR: Failed to get profile data Context: {"user_id":2,"error":"Database operation failed"}
[2025-08-07 07:11:35] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"johndoe123","user_id":2}}
[2025-08-07 07:11:35] INFO: User logged in Context: {"user_id":2,"username":"johndoe123","user_type":"unified","ip_address":"127.0.0.1"}
[2025-08-07 07:21:07] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"johndoe123","user_id":2}}
[2025-08-07 07:21:07] INFO: User logged in Context: {"user_id":2,"username":"johndoe123","user_type":"unified","ip_address":"127.0.0.1"}
[2025-08-07 07:29:50] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/test_login.php","user_id":null,"details":{"username":"testuser","user_id":3}}
[2025-08-07 07:29:50] INFO: User logged in Context: {"user_id":3,"username":"testuser","user_type":"unified","ip_address":"127.0.0.1"}
[2025-08-07 08:19:26] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"testuser","user_id":3}}
[2025-08-07 08:19:26] INFO: User logged in Context: {"user_id":3,"username":"testuser","user_type":"unified","ip_address":"127.0.0.1"}
[2025-08-07 08:19:33] ERROR: Failed to get profile data Context: {"user_id":3,"error":"Database operation failed"}
[2025-08-07 08:25:55] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin"}}
[2025-08-07 08:25:55] INFO: User logged in Context: {"user_id":1,"username":"admin","user_type":"admin","ip_address":"127.0.0.1"}
[2025-08-07 08:37:39] ERROR: Failed to get profile data Context: {"user_id":3,"error":"Database operation failed"}
[2025-08-07 08:41:41] ERROR: Failed to get profile data Context: {"user_id":3,"error":"Database operation failed"}
[2025-08-07 08:56:08] ERROR: Failed to get profile data Context: {"user_id":3,"error":"Database operation failed"}
[2025-08-07 09:11:50] INFO: Donor medical fields database migration completed Context: {"migration":"donor_medical_fields_migration","statements_executed":1}
[2025-08-07 09:21:39] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/quick_login.php","user_id":null,"details":{"username":"testuser","user_id":3}}
[2025-08-07 09:21:39] INFO: User logged in Context: {"user_id":3,"username":"testuser","user_type":"unified","ip_address":"127.0.0.1"}
[2025-08-07 09:47:33] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"johndoe123","user_id":2}}
[2025-08-07 09:47:33] INFO: User logged in Context: {"user_id":2,"username":"johndoe123","user_type":"unified","ip_address":"127.0.0.1"}
[2025-08-07 09:47:39] ERROR: Failed to get profile data Context: {"user_id":2,"error":"Database operation failed"}
