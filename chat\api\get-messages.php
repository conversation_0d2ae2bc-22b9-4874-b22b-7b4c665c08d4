<?php
/**
 * Get Chat Messages API
 * Blood Donation Management System
 */

require_once '../../config/constants.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Start session and check authentication
startSecureSession();

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$currentUser = getCurrentUser();
$userId = (int)($_GET['user_id'] ?? 0);

// Validate input
if ($userId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
    exit;
}

// Check if user can chat with this user
if (!canChatWith($currentUser['id'], $userId)) {
    echo json_encode(['success' => false, 'message' => 'You cannot chat with this user']);
    exit;
}

try {
    $messages = getChatMessages($currentUser['id'], $userId);
    
    // Mark messages as read
    markMessagesAsRead($currentUser['id'], $userId);
    
    echo json_encode([
        'success' => true,
        'messages' => $messages
    ]);
    
} catch (Exception $e) {
    logEvent('ERROR', 'Get chat messages failed', [
        'user_id' => $currentUser['id'],
        'chat_user_id' => $userId,
        'error' => $e->getMessage()
    ]);
    
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to load messages']);
}
?>
