/**
 * Admin Mobile Navigation JavaScript
 * Blood Donation Management System - Admin Panel
 * 
 * This file provides enhanced mobile navigation functionality specifically for the admin panel including:
 * - Admin-specific hamburger menu functionality
 * - Enhanced dropdown visibility and positioning
 * - Role-based access control for mobile navigation
 * - Admin-specific touch interactions and animations
 */

class AdminMobileNavigation {
    constructor() {
        this.navbar = null;
        this.toggler = null;
        this.collapse = null;
        this.navLinks = null;
        this.adminDropdown = null;
        this.adminProfile = null;
        this.isOpen = false;
        this.isDropdownOpen = false;
        this.touchStartY = 0;
        this.touchEndY = 0;
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupAdminNavigation());
        } else {
            this.setupAdminNavigation();
        }
    }
    
    setupAdminNavigation() {
        this.navbar = document.querySelector('.admin-navbar');
        this.toggler = document.querySelector('.admin-navbar .navbar-toggler');
        this.collapse = document.querySelector('.admin-navbar .navbar-collapse');
        this.navLinks = document.querySelectorAll('.admin-navbar .navbar-nav .nav-link');
        this.adminDropdown = document.querySelector('.admin-dropdown');
        this.adminProfile = document.querySelector('.admin-profile');
        
        if (!this.navbar || !this.toggler || !this.collapse) {
            console.warn('Admin Mobile Navigation: Required elements not found');
            return;
        }
        
        this.bindAdminEvents();
        this.setupAdminAccessibility();
        this.setupAdminTouchGestures();
        this.setupAdminScrollBehavior();
        this.setupAdminDropdownHandling();
    }
    
    bindAdminEvents() {
        // Toggler click event
        this.toggler.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleAdminNavigation();
        });
        
        // Close navigation when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.navbar.contains(e.target)) {
                this.closeAdminNavigation();
            }
            
            // Close dropdown when clicking outside
            if (this.isDropdownOpen && !this.adminProfile?.contains(e.target) && !this.adminDropdown?.contains(e.target)) {
                this.closeAdminDropdown();
            }
        });
        
        // Handle navigation link clicks
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // Add click animation
                this.addClickAnimation(e.target);
                
                // Close mobile navigation after link click
                if (window.innerWidth < 768) {
                    setTimeout(() => this.closeAdminNavigation(), 300);
                }
            });
        });
        
        // Handle window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleAdminResize();
        }, 250));
        
        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleAdminResize(), 100);
        });
    }
    
    setupAdminAccessibility() {
        // Add ARIA attributes for admin navigation
        this.toggler.setAttribute('aria-expanded', 'false');
        this.toggler.setAttribute('aria-label', 'Toggle admin navigation menu');
        
        // Add keyboard navigation for admin
        this.toggler.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleAdminNavigation();
            }
        });
        
        // Handle escape key for admin navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (this.isOpen) {
                    this.closeAdminNavigation();
                }
                if (this.isDropdownOpen) {
                    this.closeAdminDropdown();
                }
            }
        });
        
        // Add focus management for admin navigation
        this.navLinks.forEach(link => {
            link.addEventListener('focus', () => {
                if (this.isOpen) {
                    link.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            });
        });
    }
    
    setupAdminTouchGestures() {
        // Handle touch gestures for admin mobile
        if ('ontouchstart' in window) {
            this.navbar.addEventListener('touchstart', (e) => {
                this.touchStartY = e.touches[0].clientY;
            });
            
            this.navbar.addEventListener('touchend', (e) => {
                this.touchEndY = e.changedTouches[0].clientY;
                this.handleAdminTouchGesture();
            });
        }
    }
    
    setupAdminScrollBehavior() {
        let lastScrollTop = 0;
        let scrollThreshold = 10;
        
        window.addEventListener('scroll', this.throttle(() => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollDelta = Math.abs(scrollTop - lastScrollTop);
            
            if (scrollDelta > scrollThreshold) {
                if (scrollTop > lastScrollTop && this.isOpen) {
                    // Scrolling down - close admin navigation
                    this.closeAdminNavigation();
                }
                
                // Add scroll-based admin navbar styling
                if (scrollTop > 50) {
                    this.navbar.classList.add('admin-navbar-scrolled');
                } else {
                    this.navbar.classList.remove('admin-navbar-scrolled');
                }
                
                lastScrollTop = scrollTop;
            }
        }, 100));
    }
    
    setupAdminDropdownHandling() {
        if (this.adminProfile && this.adminDropdown) {
            // Handle admin profile dropdown
            this.adminProfile.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleAdminDropdown();
            });
            
            // Handle dropdown item clicks
            const dropdownItems = this.adminDropdown.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    // Add click animation
                    this.addClickAnimation(e.target);
                    
                    // Close dropdown after item click
                    setTimeout(() => this.closeAdminDropdown(), 200);
                });
            });
            
            // Keyboard navigation for dropdown
            this.adminProfile.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleAdminDropdown();
                }
            });
        }
    }
    
    toggleAdminNavigation() {
        if (this.isOpen) {
            this.closeAdminNavigation();
        } else {
            this.openAdminNavigation();
        }
    }
    
    openAdminNavigation() {
        this.isOpen = true;
        this.toggler.setAttribute('aria-expanded', 'true');
        this.collapse.classList.add('show');
        this.navbar.classList.add('admin-navigation-open');
        
        // Add body scroll lock
        document.body.style.overflow = 'hidden';
        
        // Focus first navigation link
        setTimeout(() => {
            const firstLink = this.collapse.querySelector('.nav-link');
            if (firstLink) {
                firstLink.focus();
            }
        }, 300);
        
        // Add entrance animation for admin
        this.addAdminEntranceAnimation();
    }
    
    closeAdminNavigation() {
        this.isOpen = false;
        this.toggler.setAttribute('aria-expanded', 'false');
        this.collapse.classList.remove('show');
        this.navbar.classList.remove('admin-navigation-open');
        
        // Restore body scroll
        document.body.style.overflow = '';
        
        // Add exit animation for admin
        this.addAdminExitAnimation();
    }
    
    toggleAdminDropdown() {
        if (this.isDropdownOpen) {
            this.closeAdminDropdown();
        } else {
            this.openAdminDropdown();
        }
    }
    
    openAdminDropdown() {
        this.isDropdownOpen = true;
        this.adminDropdown.classList.add('show');
        this.adminProfile.setAttribute('aria-expanded', 'true');
        
        // Add entrance animation for dropdown
        this.addDropdownEntranceAnimation();
    }
    
    closeAdminDropdown() {
        this.isDropdownOpen = false;
        this.adminDropdown.classList.remove('show');
        this.adminProfile.setAttribute('aria-expanded', 'false');
        
        // Add exit animation for dropdown
        this.addDropdownExitAnimation();
    }
    
    addAdminEntranceAnimation() {
        this.navLinks.forEach((link, index) => {
            link.style.opacity = '0';
            link.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                link.style.transition = 'all 0.3s ease';
                link.style.opacity = '1';
                link.style.transform = 'translateX(0)';
            }, index * 100);
        });
    }
    
    addAdminExitAnimation() {
        this.navLinks.forEach((link, index) => {
            setTimeout(() => {
                link.style.opacity = '0';
                link.style.transform = 'translateX(-20px)';
            }, index * 50);
        });
    }
    
    addDropdownEntranceAnimation() {
        const dropdownItems = this.adminDropdown.querySelectorAll('.dropdown-item');
        dropdownItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(-10px)';
            
            setTimeout(() => {
                item.style.transition = 'all 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 50);
        });
    }
    
    addDropdownExitAnimation() {
        const dropdownItems = this.adminDropdown.querySelectorAll('.dropdown-item');
        dropdownItems.forEach((item, index) => {
            setTimeout(() => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(-10px)';
            }, index * 25);
        });
    }
    
    addClickAnimation(element) {
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }
    
    handleAdminTouchGesture() {
        const touchDistance = this.touchStartY - this.touchEndY;
        const minSwipeDistance = 50;
        
        if (Math.abs(touchDistance) > minSwipeDistance) {
            if (touchDistance > 0 && this.isOpen) {
                // Swipe up - close admin navigation
                this.closeAdminNavigation();
            }
        }
    }
    
    handleAdminResize() {
        if (window.innerWidth >= 768) {
            // Reset admin mobile-specific states on larger screens
            this.isOpen = false;
            this.toggler.setAttribute('aria-expanded', 'false');
            this.collapse.classList.remove('show');
            this.navbar.classList.remove('admin-navigation-open');
            document.body.style.overflow = '';
            
            // Reset animations
            this.navLinks.forEach(link => {
                link.style.opacity = '';
                link.style.transform = '';
                link.style.transition = '';
            });
        }
    }
    
    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialize admin mobile navigation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new AdminMobileNavigation();
});

// Export for potential use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminMobileNavigation;
} 