<?php
/**
 * Admin Blood Requests Management
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/BloodRequest.php';
require_once '../classes/Notification.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('requests.php', 'Invalid security token', 'error');
    }
    
    $action = $_POST['action'] ?? '';
    $requestId = (int)($_POST['request_id'] ?? 0);
    $adminId = getCurrentUser()['id'];
    
    if ($requestId > 0) {
        $request = new BloodRequest($requestId);
        
        switch ($action) {
            case 'approve':
                $notes = sanitizeInput($_POST['notes'] ?? '');
                $request->approve($adminId, $notes);
                
                // Send notification to compatible donors
                try {
                    Notification::sendBloodRequestNotification($requestId, $adminId);
                } catch (Exception $e) {
                    logEvent('WARNING', 'Failed to send blood request notification', [
                        'request_id' => $requestId,
                        'error' => $e->getMessage()
                    ]);
                }
                
                redirectWithMessage('requests.php', 'Blood request approved successfully', 'success');
                break;
                
            case 'reject':
                $reason = sanitizeInput($_POST['reason'] ?? '');
                if (empty($reason)) {
                    redirectWithMessage('requests.php', 'Rejection reason is required', 'error');
                }
                $request->reject($adminId, $reason);
                redirectWithMessage('requests.php', 'Blood request rejected', 'success');
                break;
                
            case 'fulfill':
                $notes = sanitizeInput($_POST['notes'] ?? '');
                $request->fulfill($adminId, $notes);
                redirectWithMessage('requests.php', 'Blood request marked as fulfilled', 'success');
                break;

            case 'archive':
                $reason = sanitizeInput($_POST['reason'] ?? '');
                if (empty($reason)) {
                    redirectWithMessage('requests.php', 'Archive reason is required', 'error');
                }
                $request->archive($adminId, $reason);
                redirectWithMessage('requests.php', 'Blood request archived', 'success');
                break;

            case 'restore':
                $reason = sanitizeInput($_POST['reason'] ?? '');
                $request->restore($adminId, $reason);
                redirectWithMessage('requests.php', 'Blood request restored', 'success');
                break;
        }
    }
}

// Get filters
$filters = [
    'status' => sanitizeInput($_GET['status'] ?? ''),
    'urgency_level' => sanitizeInput($_GET['urgency_level'] ?? ''),
    'blood_type_id' => (int)($_GET['blood_type_id'] ?? 0),
    'search' => sanitizeInput($_GET['search'] ?? '')
];

$page = (int)($_GET['page'] ?? 1);

// Get requests with pagination
$result = BloodRequest::getAll($page, RECORDS_PER_PAGE, $filters);
$requests = $result['requests'];
$pagination = $result['pagination'];

// Get blood types for filter
$bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");

// Get flash message
$flash = getFlashMessage();
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blood Requests - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.php">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>

                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-hand-holding-medical"></i> Blood Requests Management</h5>
                    </div>
                    
                    <div class="card-body">
                        <!-- Filters -->
                        <form method="GET" class="row g-3 mb-4">
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="pending" <?php echo $filters['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="approved" <?php echo $filters['status'] === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="fulfilled" <?php echo $filters['status'] === 'fulfilled' ? 'selected' : ''; ?>>Fulfilled</option>
                                    <option value="cancelled" <?php echo $filters['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Urgency</label>
                                <select name="urgency_level" class="form-select">
                                    <option value="">All Urgency</option>
                                    <option value="low" <?php echo $filters['urgency_level'] === 'low' ? 'selected' : ''; ?>>Low</option>
                                    <option value="medium" <?php echo $filters['urgency_level'] === 'medium' ? 'selected' : ''; ?>>Medium</option>
                                    <option value="high" <?php echo $filters['urgency_level'] === 'high' ? 'selected' : ''; ?>>High</option>
                                    <option value="critical" <?php echo $filters['urgency_level'] === 'critical' ? 'selected' : ''; ?>>Critical</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Blood Type</label>
                                <select name="blood_type_id" class="form-select">
                                    <option value="">All Types</option>
                                    <?php foreach ($bloodTypes as $bloodType): ?>
                                        <option value="<?php echo $bloodType['id']; ?>" 
                                                <?php echo $filters['blood_type_id'] == $bloodType['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($bloodType['type']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Search</label>
                                <input type="text" name="search" class="form-control" placeholder="Recipient name, hospital..." 
                                       value="<?php echo htmlspecialchars($filters['search']); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Requests Table -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Recipient</th>
                                        <th>Blood Type</th>
                                        <th>Units</th>
                                        <th>Urgency</th>
                                        <th>Hospital</th>
                                        <th>Required By</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($requests)): ?>
                                        <tr>
                                            <td colspan="9" class="text-center text-muted">No blood requests found</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($requests as $request): ?>
                                            <tr>
                                                <td><?php echo $request['id']; ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($request['recipient_first_name'] . ' ' . $request['recipient_last_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($request['recipient_phone']); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-danger"><?php echo htmlspecialchars($request['blood_type']); ?></span>
                                                </td>
                                                <td><?php echo $request['units_needed']; ?></td>
                                                <td>
                                                    <span class="badge urgency-<?php echo $request['urgency_level']; ?>">
                                                        <?php echo strtoupper($request['urgency_level']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($request['hospital_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($request['hospital_address']); ?></small>
                                                </td>
                                                <td>
                                                    <?php 
                                                    $requiredDate = new DateTime($request['required_by_date']);
                                                    $today = new DateTime();
                                                    $isOverdue = $requiredDate < $today;
                                                    ?>
                                                    <span class="<?php echo $isOverdue ? 'text-danger fw-bold' : ''; ?>">
                                                        <?php echo formatDate($request['required_by_date']); ?>
                                                    </span>
                                                    <?php if ($isOverdue): ?>
                                                        <br><small class="text-danger">OVERDUE</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge status-<?php echo $request['status']; ?>">
                                                        <?php echo ucfirst($request['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="viewRequest(<?php echo $request['id']; ?>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <?php if ($request['status'] === 'pending'): ?>
                                                            <button class="btn btn-outline-success" onclick="approveRequest(<?php echo $request['id']; ?>)">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <button class="btn btn-outline-danger" onclick="rejectRequest(<?php echo $request['id']; ?>)">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        <?php elseif ($request['status'] === 'approved'): ?>
                                                            <button class="btn btn-outline-info" onclick="fulfillRequest(<?php echo $request['id']; ?>)">
                                                                <i class="fas fa-check-double"></i>
                                                            </button>
                                                        <?php endif; ?>

                                                        <!-- Archive button for all requests -->
                                                        <button class="btn btn-outline-warning" onclick="archiveRequest(<?php echo $request['id']; ?>)" title="Archive Request">
                                                            <i class="fas fa-archive"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($pagination['total_pages'] > 1): ?>
                            <nav aria-label="Requests pagination">
                                <?php echo generatePaginationHTML($pagination, 'requests.php?' . http_build_query($filters)); ?>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Modals -->
    <!-- Approve Modal -->
    <div class="modal fade" id="approveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    <input type="hidden" name="action" value="approve">
                    <input type="hidden" name="request_id" id="approveRequestId">
                    
                    <div class="modal-header">
                        <h5 class="modal-title">Approve Blood Request</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Admin Notes (Optional)</label>
                            <textarea name="notes" class="form-control" rows="3" placeholder="Add any notes about the approval..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Approve Request</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    <input type="hidden" name="action" value="reject">
                    <input type="hidden" name="request_id" id="rejectRequestId">
                    
                    <div class="modal-header">
                        <h5 class="modal-title">Reject Blood Request</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Rejection Reason *</label>
                            <textarea name="reason" class="form-control" rows="3" placeholder="Please provide a reason for rejection..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Reject Request</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Fulfill Modal -->
    <div class="modal fade" id="fulfillModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    <input type="hidden" name="action" value="fulfill">
                    <input type="hidden" name="request_id" id="fulfillRequestId">
                    
                    <div class="modal-header">
                        <h5 class="modal-title">Mark as Fulfilled</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Fulfillment Notes (Optional)</label>
                            <textarea name="notes" class="form-control" rows="3" placeholder="Add any notes about the fulfillment..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-info">Mark as Fulfilled</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Archive Modal -->
    <div class="modal fade" id="archiveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    <input type="hidden" name="action" value="archive">
                    <input type="hidden" name="request_id" id="archiveRequestId">

                    <div class="modal-header">
                        <h5 class="modal-title">Archive Blood Request</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Warning:</strong> Archiving this request will move it to the archive. This action can be undone later if needed.
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Archive Reason *</label>
                            <textarea name="reason" class="form-control" rows="3" placeholder="Please provide a reason for archiving this request..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">Archive Request</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        function viewRequest(requestId) {
            window.location.href = 'request-details.php?id=' + requestId;
        }

        function approveRequest(requestId) {
            document.getElementById('approveRequestId').value = requestId;
            new bootstrap.Modal(document.getElementById('approveModal')).show();
        }

        function rejectRequest(requestId) {
            document.getElementById('rejectRequestId').value = requestId;
            new bootstrap.Modal(document.getElementById('rejectModal')).show();
        }

        function fulfillRequest(requestId) {
            document.getElementById('fulfillRequestId').value = requestId;
            new bootstrap.Modal(document.getElementById('fulfillModal')).show();
        }

        function archiveRequest(requestId) {
            document.getElementById('archiveRequestId').value = requestId;
            new bootstrap.Modal(document.getElementById('archiveModal')).show();
        }
    </script>
</body>
</html>
