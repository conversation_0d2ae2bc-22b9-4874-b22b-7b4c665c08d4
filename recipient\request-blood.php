<?php
/**
 * Recipient Blood Request
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../classes/Recipient.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_RECIPIENT, '../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

// Load recipient profile
$recipient = new Recipient($currentUser['id']);

// Get blood types
$bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");

$errors = [];
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        $requestData = [
            'blood_type_id' => (int)($_POST['blood_type_id'] ?? 0),
            'units_needed' => (int)($_POST['units_needed'] ?? 0),
            'urgency_level' => sanitizeInput($_POST['urgency_level'] ?? ''),
            'hospital_name' => sanitizeInput($_POST['hospital_name'] ?? ''),
            'hospital_address' => sanitizeInput($_POST['hospital_address'] ?? ''),
            'hospital_contact' => sanitizeInput($_POST['hospital_contact'] ?? ''),
            'required_by_date' => sanitizeInput($_POST['required_by_date'] ?? '')
        ];
        
        $validation = validateBloodRequest($requestData);
        
        if ($validation->isValid) {
            try {
                $requestId = $recipient->submitBloodRequest($requestData);
                
                redirectWithMessage('requests.php', 'Blood request submitted successfully! Request ID: #' . $requestId, 'success');
                
            } catch (Exception $e) {
                $errors[] = $e->getMessage();
            }
        } else {
            foreach ($validation->getErrors() as $field => $fieldErrors) {
                $errors = array_merge($errors, $fieldErrors);
            }
        }
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Blood - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-hand-holding-medical"></i> <?php echo APP_NAME; ?> - Recipient
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical"></i> My Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="request-blood.php">
                            <i class="fas fa-plus"></i> Request Blood
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donors.php">
                            <i class="fas fa-heart"></i> Find Donors
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo $currentUser['first_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-plus-circle"></i> Submit Blood Request</h4>
                        <p class="mb-0 text-muted">Fill out the form below to request blood. Your request will be reviewed by our medical team.</p>
                    </div>
                    
                    <div class="card-body">
                        <!-- Recipient Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6><i class="fas fa-user"></i> Your Information</h6>
                                        <p class="mb-1"><strong>Name:</strong> <?php echo htmlspecialchars($recipient->getFullName()); ?></p>
                                        <p class="mb-1"><strong>Email:</strong> <?php echo htmlspecialchars($recipient->getEmail()); ?></p>
                                        <p class="mb-1"><strong>Phone:</strong> <?php echo htmlspecialchars($recipient->getPhone() ?: 'Not provided'); ?></p>
                                        <p class="mb-0"><strong>Medical Condition:</strong> <?php echo htmlspecialchars($recipient->getMedicalCondition() ?: 'Not specified'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h6><i class="fas fa-info-circle"></i> Important Information</h6>
                                        <ul class="mb-0">
                                            <li>All requests are reviewed by medical staff</li>
                                            <li>Approval typically takes 2-4 hours</li>
                                            <li>You will be notified of status updates</li>
                                            <li>Emergency requests are prioritized</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Request Form -->
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                            
                            <h5><i class="fas fa-tint"></i> Blood Requirements</h5>
                            <hr>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Blood Type Required *</label>
                                        <select class="form-select" name="blood_type_id" required>
                                            <option value="">Select Blood Type</option>
                                            <?php foreach ($bloodTypes as $bloodType): ?>
                                                <option value="<?php echo $bloodType['id']; ?>"
                                                        <?php echo ($_POST['blood_type_id'] ?? '') == $bloodType['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($bloodType['type']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="form-text">Select the blood type you need</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Units Needed *</label>
                                        <select class="form-select" name="units_needed" required>
                                            <option value="">Select Units</option>
                                            <?php for ($i = 1; $i <= 10; $i++): ?>
                                                <option value="<?php echo $i; ?>"
                                                        <?php echo ($_POST['units_needed'] ?? '') == $i ? 'selected' : ''; ?>>
                                                    <?php echo $i; ?> unit<?php echo $i > 1 ? 's' : ''; ?>
                                                </option>
                                            <?php endfor; ?>
                                        </select>
                                        <div class="form-text">Number of blood units required</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Urgency Level *</label>
                                        <select class="form-select" name="urgency_level" required>
                                            <option value="">Select Urgency</option>
                                            <?php foreach (URGENCY_LEVELS as $level => $description): ?>
                                                <option value="<?php echo $level; ?>"
                                                        <?php echo ($_POST['urgency_level'] ?? '') === $level ? 'selected' : ''; ?>>
                                                    <?php echo ucfirst($level); ?> - <?php echo $description; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="form-text">How urgent is this request?</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Required By Date *</label>
                                        <input type="date" class="form-control future-date" name="required_by_date" 
                                               min="<?php echo date('Y-m-d'); ?>" 
                                               value="<?php echo htmlspecialchars($_POST['required_by_date'] ?? ''); ?>" required>
                                        <div class="form-text">When do you need the blood?</div>
                                    </div>
                                </div>
                            </div>

                            <h5><i class="fas fa-hospital"></i> Hospital Information</h5>
                            <hr>
                            
                            <div class="mb-3">
                                <label class="form-label">Hospital Name *</label>
                                <input type="text" class="form-control" name="hospital_name" 
                                       value="<?php echo htmlspecialchars($_POST['hospital_name'] ?? ''); ?>" 
                                       placeholder="Enter hospital name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Hospital Address *</label>
                                <textarea class="form-control" name="hospital_address" rows="3" 
                                          placeholder="Enter complete hospital address" required><?php echo htmlspecialchars($_POST['hospital_address'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Hospital Contact Number</label>
                                <input type="tel" class="form-control" name="hospital_contact" 
                                       value="<?php echo htmlspecialchars($_POST['hospital_contact'] ?? ''); ?>" 
                                       placeholder="Hospital phone number (optional)">
                            </div>

                            <!-- Important Notes -->
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Important Notes</h6>
                                <ul class="mb-0">
                                    <li>Ensure all information is accurate and complete</li>
                                    <li>Critical and high urgency requests are processed first</li>
                                    <li>You will receive email and SMS notifications about your request status</li>
                                    <li>Contact the hospital directly for immediate emergencies</li>
                                    <li>Our team will work to find compatible donors in your area</li>
                                </ul>
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I confirm that all information provided is accurate and I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">terms and conditions</a> *
                                </label>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Submit Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Terms and Conditions Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Blood Request Terms and Conditions</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>Request Submission</h6>
                    <ul>
                        <li>All blood requests must be medically justified</li>
                        <li>Requests are subject to medical review and approval</li>
                        <li>False or misleading information may result in request denial</li>
                        <li>Emergency requests should also contact emergency services</li>
                    </ul>
                    
                    <h6>Medical Information</h6>
                    <ul>
                        <li>Provide accurate medical condition information</li>
                        <li>Hospital verification may be required</li>
                        <li>Medical records may be requested for verification</li>
                        <li>Blood compatibility testing will be performed</li>
                    </ul>
                    
                    <h6>Privacy and Data</h6>
                    <ul>
                        <li>Your information will be shared only with relevant medical staff and donors</li>
                        <li>Personal data is protected according to privacy policies</li>
                        <li>Request information may be used for system improvement</li>
                        <li>You may withdraw your request at any time</li>
                    </ul>
                    
                    <h6>Donor Matching</h6>
                    <ul>
                        <li>We will attempt to find compatible donors but cannot guarantee availability</li>
                        <li>Donor contact information may be shared for coordination</li>
                        <li>Direct communication with donors should be respectful and medical-focused</li>
                        <li>Alternative sources may be suggested if donors are unavailable</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Update urgency level styling
        document.querySelector('select[name="urgency_level"]').addEventListener('change', function() {
            const urgencyCard = document.querySelector('.card-body');
            urgencyCard.className = urgencyCard.className.replace(/bg-\w+/, '');
            
            switch(this.value) {
                case 'critical':
                    urgencyCard.classList.add('bg-danger', 'text-white');
                    break;
                case 'high':
                    urgencyCard.classList.add('bg-warning');
                    break;
                case 'medium':
                    urgencyCard.classList.add('bg-info', 'text-white');
                    break;
                case 'low':
                    urgencyCard.classList.add('bg-success', 'text-white');
                    break;
            }
        });
    </script>
</body>
</html>
