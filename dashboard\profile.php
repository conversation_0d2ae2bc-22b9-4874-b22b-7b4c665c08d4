<?php
/**
 * User Profile Management Page
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../classes/UnifiedUser.php';
require_once '../classes/ProfileManager.php';

// Start session and check authentication
startSecureSession();
requireUnifiedAccess('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();
$unifiedUser = new UnifiedUser($currentUser['id']);
$profileManager = new ProfileManager($currentUser['id']);

// Get user roles and current role
$userRoles = $unifiedUser->getActiveRoles();
$currentRole = $_SESSION['current_role'] ?? $unifiedUser->getPrimaryRole();

$errors = [];
$success = '';
$activeTab = $_GET['tab'] ?? 'personal';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_personal':
                $result = $profileManager->updatePersonalInfo($_POST);
                if ($result['success']) {
                    $success = $result['message'];
                    // Refresh current user data
                    $currentUser = getCurrentUser();
                } else {
                    $errors = $result['errors'];
                }
                break;
                
            case 'change_password':
                $result = $profileManager->changePassword($_POST);
                if ($result['success']) {
                    $success = $result['message'];
                } else {
                    $errors = $result['errors'];
                }
                $activeTab = 'security';
                break;
                
            case 'upload_photo':
                $result = $profileManager->uploadProfilePhoto($_FILES['profile_photo'] ?? null);
                if ($result['success']) {
                    $success = $result['message'];
                    // Refresh current user data
                    $currentUser = getCurrentUser();
                } else {
                    $errors = $result['errors'];
                }
                break;
                
            case 'update_donor_profile':
                if (in_array('donor', $userRoles)) {
                    $result = $profileManager->updateDonorProfile($_POST);
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $errors = $result['errors'];
                    }
                }
                $activeTab = 'roles';
                break;
                
            case 'update_recipient_profile':
                if (in_array('recipient', $userRoles)) {
                    $result = $profileManager->updateRecipientProfile($_POST);
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $errors = $result['errors'];
                    }
                }
                $activeTab = 'roles';
                break;
                

        }
    }
}

// Get profile data
$profileData = $profileManager->getProfileData();
$donorProfile = null;
$recipientProfile = null;

if (in_array('donor', $userRoles)) {
    $donorProfile = $profileManager->getDonorProfile();
}

if (in_array('recipient', $userRoles)) {
    $recipientProfile = $profileManager->getRecipientProfile();
}

// Get blood types for donor profile
$bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");

$pageTitle = 'Profile Management';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Blood Donation Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/responsive.css" rel="stylesheet">
    <style>
        /* Profile-specific styles that are not covered by responsive.css */
        .form-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #dc3545;
        }

        .form-section h5 {
            color: #dc3545;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .profile-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .profile-actions .btn {
            flex: 1;
            min-width: 120px;
        }

        @media (max-width: 768px) {
            .profile-actions {
                flex-direction: column;
            }
            
            .profile-actions .btn {
                width: 100%;
            }
        }

        .role-switch-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            border: 2px solid #dee2e6;
        }

        .role-card {
            background: #fff;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .role-card:hover {
            border-color: #dc3545;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.1);
        }

        .role-card.active {
            border-color: #dc3545;
            background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
        }

        .role-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .role-title {
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .role-status {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .role-status.active {
            background-color: #28a745;
            color: #fff;
        }

        .role-status.inactive {
            background-color: #6c757d;
            color: #fff;
        }

        .upload-section {
            text-align: center;
            padding: 2rem;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .upload-icon {
            font-size: 3rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-label {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #dc3545;
            color: #fff;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input-label:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .preview-container {
            margin-top: 1rem;
            text-align: center;
        }

        .preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .security-section {
            background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #dc3545;
        }

        .password-strength {
            margin-top: 0.5rem;
        }

        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
        }

        .strength-weak { background: #dc3545; width: 25%; }
        .strength-fair { background: #ffc107; width: 50%; }
        .strength-good { background: #17a2b8; width: 75%; }
        .strength-strong { background: #28a745; width: 100%; }

        .strength-text {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .activity-log {
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            transition: background-color 0.3s ease;
        }

        .activity-item:hover {
            background-color: #f8f9fa;
        }

        .activity-time {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .activity-description {
            margin: 0.5rem 0;
            font-weight: 500;
        }

        .activity-details {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .notification-settings {
            background: #fff;
            border-radius: 10px;
            padding: 1.5rem;
            border: 2px solid #dee2e6;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
            border-bottom: 1px solid #dee2e6;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            font-weight: 500;
            color: #333;
        }

        .setting-description {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #dc3545;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .privacy-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #dee2e6;
        }

        .privacy-option {
            background: #fff;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .privacy-option:hover {
            border-color: #dc3545;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.1);
        }

        .privacy-option.selected {
            border-color: #dc3545;
            background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
        }

        .privacy-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .privacy-title {
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .privacy-description {
            color: #6c757d;
            font-size: 0.875rem;
            margin: 0;
        }

        .data-export {
            background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #dc3545;
        }

        .export-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: #fff;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .export-option:hover {
            border-color: #dc3545;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
        }

        .export-info {
            flex: 1;
        }

        .export-title {
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .export-description {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0.25rem 0 0 0;
        }

        .export-button {
            padding: 0.5rem 1rem;
            background: #dc3545;
            color: #fff;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .export-button:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        .delete-account {
            background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #dc3545;
        }

        .delete-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .delete-warning i {
            color: #f39c12;
            margin-right: 0.5rem;
        }

        .confirmation-checkbox {
            margin: 1rem 0;
        }

        .confirmation-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-left: 0.5rem;
        }

        .delete-button {
            background: #dc3545;
            color: #fff;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .delete-button:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .delete-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-tint"></i> Blood Donation Management System
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="profile.php">
                            <i class="fas fa-user"></i> Profile
                        </a>
                    </li>
                    <?php if (in_array('donor', $userRoles)): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart"></i> My Donations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.php">
                            <i class="fas fa-calendar"></i> My Scheduled Donations
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php if (in_array('recipient', $userRoles)): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical"></i> My Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="create-request.php">
                            <i class="fas fa-plus"></i> Request Blood
                        </a>
                    </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments"></i> Messages
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Role Switcher -->
                    <?php if (count($userRoles) > 1): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="roleSwitcher" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-tag"></i> 
                            <?php echo ucfirst($currentRole); ?> Mode
                        </a>
                        <ul class="dropdown-menu">
                            <?php foreach ($userRoles as $role): ?>
                                <?php if ($role !== $currentRole): ?>
                                <li>
                                    <form method="POST" action="index.php" class="d-inline">
                                        <button type="submit" name="switch_role" value="<?php echo $role; ?>" class="dropdown-item">
                                            <i class="fas fa-<?php echo $role === 'donor' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                                            Switch to <?php echo ucfirst($role); ?>
                                        </button>
                                    </form>
                                </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ul>
                    </li>
                    <?php endif; ?>
                    
                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($currentUser['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Success/Error Messages -->
        <?php if (!empty($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Sidebar -->
            <div class="col-md-4 col-lg-3">
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <?php 
                            $profilePhotoUrl = !empty($currentUser['profile_photo']) 
                                ? '../uploads/profiles/' . $currentUser['profile_photo']
                                : 'https://via.placeholder.com/150x150/6c757d/ffffff?text=' . strtoupper(substr($currentUser['first_name'], 0, 1) . substr($currentUser['last_name'], 0, 1));
                            ?>
                            <img src="<?php echo htmlspecialchars($profilePhotoUrl); ?>" 
                                 class="rounded-circle profile-photo" 
                                 alt="Profile Photo"
                                 id="currentProfilePhoto">
                        </div>
                        <h5 class="mb-1"><?php echo htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']); ?></h5>
                        <p class="text-muted mb-2">@<?php echo htmlspecialchars($currentUser['username']); ?></p>
                        
                        <!-- Role Badges -->
                        <div class="mb-3">
                            <?php foreach ($userRoles as $role): ?>
                            <span class="badge role-badge <?php echo $role === $currentRole ? 'bg-primary' : 'bg-secondary'; ?>">
                                <i class="fas fa-<?php echo $role === 'donor' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                                <?php echo ucfirst($role); ?>
                                <?php echo $role === $currentRole ? ' (Active)' : ''; ?>
                            </span>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="d-grid">
                            <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#photoModal">
                                <i class="fas fa-camera"></i> Change Photo
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-bar"></i> Quick Stats</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-primary mb-0"><?php echo count($userRoles); ?></h6>
                                    <small class="text-muted">Roles</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-success mb-0">
                                    <?php echo ($currentUser['status'] ?? 'active') === 'active' ? 'Active' : ucfirst($currentUser['status'] ?? 'active'); ?>
                                </h6>
                                <small class="text-muted">Status</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Content -->
            <div class="col-md-8 col-lg-9">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo $activeTab === 'personal' ? 'active' : ''; ?>" 
                                id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" 
                                type="button" role="tab">
                            <i class="fas fa-user"></i> Personal Info
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo $activeTab === 'security' ? 'active' : ''; ?>" 
                                id="security-tab" data-bs-toggle="tab" data-bs-target="#security" 
                                type="button" role="tab">
                            <i class="fas fa-shield-alt"></i> Security
                        </button>
                    </li>
                    <?php if (!empty($userRoles)): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo $activeTab === 'roles' ? 'active' : ''; ?>" 
                                id="roles-tab" data-bs-toggle="tab" data-bs-target="#roles" 
                                type="button" role="tab">
                            <i class="fas fa-user-tag"></i> Role Profiles
                        </button>
                    </li>
                    <?php endif; ?>

                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="profileTabContent">
                    <!-- Personal Information Tab -->
                    <div class="tab-pane fade <?php echo $activeTab === 'personal' ? 'show active' : ''; ?>"
                         id="personal" role="tabpanel">
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="update_personal">

                            <div class="form-section">
                                <h5><i class="fas fa-user"></i> Basic Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="first_name" class="form-label">First Name *</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name"
                                                   value="<?php echo htmlspecialchars($currentUser['first_name']); ?>" required>
                                            <div class="invalid-feedback">Please provide a valid first name.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="last_name" class="form-label">Last Name *</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name"
                                                   value="<?php echo htmlspecialchars($currentUser['last_name']); ?>" required>
                                            <div class="invalid-feedback">Please provide a valid last name.</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">Username</label>
                                            <input type="text" class="form-control" id="username"
                                                   value="<?php echo htmlspecialchars($currentUser['username']); ?>" disabled>
                                            <div class="form-text">Username cannot be changed for security reasons.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" id="phone" name="phone"
                                                   value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h5><i class="fas fa-address-card"></i> Contact Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" id="phone" name="phone"
                                                   value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Account Status</label>
                                            <input type="text" class="form-control"
                                                   value="<?php echo ucfirst($currentUser['status'] ?? 'active'); ?>" disabled>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($currentUser['address'] ?? ''); ?></textarea>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <div class="text-muted">
                                    <small>
                                        <i class="fas fa-info-circle"></i>
                                        Last updated: <?php echo ($currentUser['updated_at'] ?? null) ? date('M j, Y g:i A', strtotime($currentUser['updated_at'])) : 'Never'; ?>
                                    </small>
                                </div>
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-save"></i> Update Personal Information
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Security Tab -->
                    <div class="tab-pane fade <?php echo $activeTab === 'security' ? 'show active' : ''; ?>"
                         id="security" role="tabpanel">
                        <div class="form-section">
                            <h5><i class="fas fa-key"></i> Change Password</h5>
                            <form method="POST" class="needs-validation" novalidate id="passwordForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="change_password">

                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Current Password *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="current_password"
                                               name="current_password" required>
                                        <button class="btn btn-outline-secondary" type="button"
                                                onclick="togglePassword('current_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">Please enter your current password.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="new_password" class="form-label">New Password *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="new_password"
                                               name="new_password" required minlength="8">
                                        <button class="btn btn-outline-secondary" type="button"
                                                onclick="togglePassword('new_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength" id="passwordStrength"></div>
                                    <div class="form-text">
                                        Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid password.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm New Password *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="confirm_password"
                                               name="confirm_password" required>
                                        <button class="btn btn-outline-secondary" type="button"
                                                onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">Passwords do not match.</div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <div class="text-muted">
                                        <small>
                                            <i class="fas fa-shield-alt"></i>
                                            Last changed: <?php echo ($profileData['password_changed_at'] ?? null) ? date('M j, Y', strtotime($profileData['password_changed_at'])) : 'Never'; ?>
                                        </small>
                                    </div>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-key"></i> Change Password
                                    </button>
                                </div>
                            </form>
                        </div>

                        <div class="form-section">
                            <h5><i class="fas fa-history"></i> Security Information</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-clock text-muted me-2"></i>
                                        <div>
                                            <strong>Last Login:</strong><br>
                                            <small class="text-muted">
                                                <?php echo ($currentUser['last_login'] ?? null) ? date('M j, Y g:i A', strtotime($currentUser['last_login'])) : 'Never'; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-user-check text-muted me-2"></i>
                                        <div>
                                            <strong>Account Created:</strong><br>
                                            <small class="text-muted">
                                                <?php echo ($currentUser['created_at'] ?? null) ? date('M j, Y', strtotime($currentUser['created_at'])) : 'Unknown'; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Role Profiles Tab -->
                    <?php if (!empty($userRoles)): ?>
                    <div class="tab-pane fade <?php echo $activeTab === 'roles' ? 'show active' : ''; ?>"
                         id="roles" role="tabpanel">

                        <?php if (in_array('donor', $userRoles)): ?>
                        <div class="form-section">
                            <h5><i class="fas fa-heart text-danger"></i> Donor Profile</h5>
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_donor_profile">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="blood_type_id" class="form-label">Blood Type *</label>
                                            <select class="form-select" id="blood_type_id" name="blood_type_id" required>
                                                <option value="">Select Blood Type</option>
                                                <?php foreach ($bloodTypes as $bloodType): ?>
                                                <option value="<?php echo $bloodType['id']; ?>"
                                                        <?php echo ($donorProfile && $donorProfile['blood_type_id'] == $bloodType['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($bloodType['type']); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="invalid-feedback">Please select your blood type.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="weight" class="form-label">Weight (kg)</label>
                                            <input type="number" class="form-control" id="weight" name="weight"
                                                   value="<?php echo $donorProfile['weight'] ?? ''; ?>"
                                                   min="40" max="200" step="0.1">
                                            <div class="form-text">Minimum weight requirement is 50kg for donation.</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="birth_date" class="form-label">Date of Birth</label>
                                            <input type="date" class="form-control" id="birth_date" name="birth_date"
                                                   value="<?php echo $donorProfile['birth_date'] ?? ''; ?>"
                                                   max="<?php echo date('Y-m-d', strtotime('-18 years')); ?>">
                                            <div class="form-text">Must be at least 18 years old to donate.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="preferred_donation_location" class="form-label">Preferred Donation Location</label>
                                            <input type="text" class="form-control" id="preferred_donation_location"
                                                   name="preferred_donation_location"
                                                   value="<?php echo $donorProfile['preferred_donation_location'] ?? ''; ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="medical_conditions" class="form-label">Medical Conditions</label>
                                    <textarea class="form-control" id="medical_conditions" name="medical_conditions"
                                              rows="3" placeholder="List any medical conditions, medications, or allergies..."><?php echo $donorProfile['medical_conditions'] ?? ''; ?></textarea>
                                    <div class="form-text">This information helps ensure safe donation practices.</div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <div class="text-muted">
                                        <small>
                                            <i class="fas fa-info-circle"></i>
                                            Eligibility Status:
                                            <span class="badge bg-<?php echo ($donorProfile['eligibility_status'] ?? 'eligible') === 'eligible' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($donorProfile['eligibility_status'] ?? 'eligible'); ?>
                                            </span>
                                        </small>
                                    </div>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-heart"></i> Update Donor Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                        <?php endif; ?>

                        <?php if (in_array('recipient', $userRoles)): ?>
                        <div class="form-section">
                            <h5><i class="fas fa-hand-holding-medical text-primary"></i> Recipient Profile</h5>
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_recipient_profile">

                                <div class="mb-3">
                                    <label for="medical_condition" class="form-label">Medical Condition *</label>
                                    <textarea class="form-control" id="medical_condition" name="medical_condition"
                                              rows="3" required placeholder="Describe your medical condition requiring blood transfusion..."><?php echo $recipientProfile['medical_condition'] ?? ''; ?></textarea>
                                    <div class="invalid-feedback">Please describe your medical condition.</div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_contact" class="form-label">Emergency Contact Name *</label>
                                            <input type="text" class="form-control" id="emergency_contact"
                                                   name="emergency_contact" required
                                                   value="<?php echo $recipientProfile['emergency_contact'] ?? ''; ?>">
                                            <div class="invalid-feedback">Please provide an emergency contact name.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_phone" class="form-label">Emergency Contact Phone *</label>
                                            <input type="tel" class="form-control" id="emergency_phone"
                                                   name="emergency_phone" required
                                                   value="<?php echo $recipientProfile['emergency_phone'] ?? ''; ?>">
                                            <div class="invalid-feedback">Please provide an emergency contact phone.</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="doctor_name" class="form-label">Doctor Name</label>
                                            <input type="text" class="form-control" id="doctor_name" name="doctor_name"
                                                   value="<?php echo $recipientProfile['doctor_name'] ?? ''; ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="doctor_contact" class="form-label">Doctor Contact</label>
                                            <input type="tel" class="form-control" id="doctor_contact" name="doctor_contact"
                                                   value="<?php echo $recipientProfile['doctor_contact'] ?? ''; ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="preferred_hospital" class="form-label">Preferred Hospital</label>
                                    <input type="text" class="form-control" id="preferred_hospital"
                                           name="preferred_hospital"
                                           value="<?php echo $recipientProfile['preferred_hospital'] ?? ''; ?>">
                                </div>

                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-hand-holding-medical"></i> Update Recipient Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>


                </div>
            </div>
        </div>
    </div>

    <!-- Profile Photo Upload Modal -->
    <div class="modal fade" id="photoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-camera"></i> Change Profile Photo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data" id="photoUploadForm">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="upload_photo">

                        <div class="mb-3">
                            <label for="profile_photo" class="form-label">Select Photo</label>
                            <input type="file" class="form-control" id="profile_photo" name="profile_photo"
                                   accept="image/jpeg,image/png,image/jpg" required>
                            <div class="form-text">
                                Supported formats: JPEG, PNG. Maximum size: 2MB. Recommended: 300x300px.
                            </div>
                        </div>

                        <div class="mb-3" id="photoPreview" style="display: none;">
                            <label class="form-label">Preview</label>
                            <div class="text-center">
                                <img id="previewImage" class="profile-photo-preview" alt="Preview">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-upload"></i> Upload Photo
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Password visibility toggle
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const button = field.nextElementSibling.querySelector('i');

            if (field.type === 'password') {
                field.type = 'text';
                button.classList.remove('fa-eye');
                button.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                button.classList.remove('fa-eye-slash');
                button.classList.add('fa-eye');
            }
        }

        // Password strength checker
        document.getElementById('new_password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');
            const strength = checkPasswordStrength(password);

            strengthBar.className = 'password-strength strength-' + strength.level;
            strengthBar.style.width = strength.percentage + '%';
        });

        function checkPasswordStrength(password) {
            let score = 0;

            if (password.length >= 8) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;

            if (score < 3) return { level: 'weak', percentage: 33 };
            if (score < 5) return { level: 'medium', percentage: 66 };
            return { level: 'strong', percentage: 100 };
        }

        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('new_password').value;
            const confirm = this.value;

            if (password !== confirm) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });

        // Photo upload preview
        document.getElementById('profile_photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file size (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    alert('File size must be less than 2MB');
                    this.value = '';
                    return;
                }

                // Validate file type
                if (!['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
                    alert('Only JPEG and PNG files are allowed');
                    this.value = '';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImage').src = e.target.result;
                    document.getElementById('photoPreview').style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                document.getElementById('photoPreview').style.display = 'none';
            }
        });

        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Tab persistence
        const activeTab = '<?php echo $activeTab; ?>';
        if (activeTab) {
            const tabButton = document.getElementById(activeTab + '-tab');
            if (tabButton) {
                const tab = new bootstrap.Tab(tabButton);
                tab.show();
            }
        }
    </script>
</body>
</html>
