<?php
/**
 * Audit Logging System
 * Blood Donation Management System
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/constants.php';

/**
 * Log admin actions for audit trail
 */
if (!function_exists('logAdminAction')) {
    function logAdminAction($action, $details = [], $userId = null) {
        try {
            $db = Database::getInstance();

            // Get current user if not provided
            if ($userId === null && isset($_SESSION['user_id'])) {
                $userId = $_SESSION['user_id'];
            }

            // Get IP address
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
            }

            // Get user agent
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

            // Prepare details as JSON
            $detailsJson = json_encode($details);

            // Insert audit log
            $sql = "INSERT INTO admin_audit_logs (user_id, action, details, ip_address, user_agent, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())";

            $db->execute($sql, [$userId, $action, $detailsJson, $ipAddress, $userAgent]);

            return true;
        } catch (Exception $e) {
            // Log error but don't break the application
            error_log("Audit logging failed: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * Log security events
 */
if (!function_exists('logSecurityEvent')) {
    function logSecurityEvent($event, $details = [], $severity = 'INFO') {
        try {
            $db = Database::getInstance();

            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            $userId = $_SESSION['user_id'] ?? null;

            $detailsJson = json_encode($details);

            $sql = "INSERT INTO security_logs (user_id, event, severity, details, ip_address, user_agent, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";

            $db->execute($sql, [$userId, $event, $severity, $detailsJson, $ipAddress, $userAgent]);

            return true;
        } catch (Exception $e) {
            error_log("Security logging failed: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * Get audit logs with pagination
 */
if (!function_exists('getAuditLogs')) {
    function getAuditLogs($page = 1, $limit = 20, $filters = []) {
    try {
        $db = Database::getInstance();
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        // Apply filters
        if (!empty($filters['user_id'])) {
            $whereConditions[] = "al.user_id = ?";
            $params[] = $filters['user_id'];
        }
        
        if (!empty($filters['action'])) {
            $whereConditions[] = "al.action LIKE ?";
            $params[] = '%' . $filters['action'] . '%';
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(al.created_at) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(al.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM admin_audit_logs al 
                     LEFT JOIN users u ON al.user_id = u.id 
                     $whereClause";
        $totalResult = $db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get logs
        $sql = "SELECT al.*, u.first_name, u.last_name, u.username 
                FROM admin_audit_logs al 
                LEFT JOIN users u ON al.user_id = u.id 
                $whereClause 
                ORDER BY al.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $logs = $db->fetchAll($sql, $params);
        
        return [
            'logs' => $logs,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            ]
        ];
    } catch (Exception $e) {
        error_log("Failed to get audit logs: " . $e->getMessage());
        return [
            'logs' => [],
            'pagination' => [
                'current_page' => 1,
                'total_pages' => 0,
                'total_records' => 0,
                'per_page' => $limit
            ]
        ];
    }
}
}

/**
 * Get security logs with pagination
 */
if (!function_exists('getSecurityLogs')) {
    function getSecurityLogs($page = 1, $limit = 20, $filters = []) {
    try {
        $db = Database::getInstance();
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        // Apply filters
        if (!empty($filters['severity'])) {
            $whereConditions[] = "sl.severity = ?";
            $params[] = $filters['severity'];
        }
        
        if (!empty($filters['event'])) {
            $whereConditions[] = "sl.event LIKE ?";
            $params[] = '%' . $filters['event'] . '%';
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(sl.created_at) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(sl.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM security_logs sl $whereClause";
        $totalResult = $db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get logs
        $sql = "SELECT sl.*, u.first_name, u.last_name, u.username 
                FROM security_logs sl 
                LEFT JOIN users u ON sl.user_id = u.id 
                $whereClause 
                ORDER BY sl.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $logs = $db->fetchAll($sql, $params);
        
        return [
            'logs' => $logs,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            ]
        ];
    } catch (Exception $e) {
        error_log("Failed to get security logs: " . $e->getMessage());
        return [
            'logs' => [],
            'pagination' => [
                'current_page' => 1,
                'total_pages' => 0,
                'total_records' => 0,
                'per_page' => $limit
            ]
        ];
    }
}
}

/**
 * Clean old logs (retention policy)
 */
if (!function_exists('cleanOldLogs')) {
    function cleanOldLogs($retentionDays = 90) {
    try {
        $db = Database::getInstance();
        
        // Clean audit logs
        $sql = "DELETE FROM admin_audit_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $auditDeleted = $db->execute($sql, [$retentionDays]);
        
        // Clean security logs
        $sql = "DELETE FROM security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $securityDeleted = $db->execute($sql, [$retentionDays]);
        
        logAdminAction('LOG_CLEANUP', [
            'retention_days' => $retentionDays,
            'audit_logs_deleted' => $auditDeleted,
            'security_logs_deleted' => $securityDeleted
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("Failed to clean old logs: " . $e->getMessage());
        return false;
    }
    }
}

/**
 * Export audit logs to CSV
 */
if (!function_exists('exportAuditLogs')) {
    function exportAuditLogs($filters = []) {
    try {
        $db = Database::getInstance();
        
        $whereConditions = [];
        $params = [];
        
        // Apply filters (same as getAuditLogs)
        if (!empty($filters['user_id'])) {
            $whereConditions[] = "al.user_id = ?";
            $params[] = $filters['user_id'];
        }
        
        if (!empty($filters['action'])) {
            $whereConditions[] = "al.action LIKE ?";
            $params[] = '%' . $filters['action'] . '%';
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(al.created_at) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(al.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        $sql = "SELECT al.*, u.first_name, u.last_name, u.username 
                FROM admin_audit_logs al 
                LEFT JOIN users u ON al.user_id = u.id 
                $whereClause 
                ORDER BY al.created_at DESC";
        
        $logs = $db->fetchAll($sql, $params);
        
        // Generate CSV content
        $csv = "ID,User,Action,Details,IP Address,User Agent,Date\n";
        foreach ($logs as $log) {
            $user = $log['username'] ? $log['first_name'] . ' ' . $log['last_name'] . ' (' . $log['username'] . ')' : 'System';
            $csv .= sprintf('"%s","%s","%s","%s","%s","%s","%s"' . "\n",
                $log['id'],
                $user,
                $log['action'],
                str_replace('"', '""', $log['details']),
                $log['ip_address'],
                str_replace('"', '""', $log['user_agent']),
                $log['created_at']
            );
        }
        
        return $csv;
    } catch (Exception $e) {
        error_log("Failed to export audit logs: " . $e->getMessage());
        return false;
    }
}
}
?>
