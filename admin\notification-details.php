<?php
/**
 * Notification Details Page
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/Notification.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

// Get notification ID
$notificationId = (int)($_GET['id'] ?? 0);

if ($notificationId <= 0) {
    redirectWithMessage('notifications.php', 'Invalid notification ID', 'error');
}

// Load notification
try {
    $notification = new Notification($notificationId);
    if (!$notification->getId()) {
        redirectWithMessage('notifications.php', 'Notification not found', 'error');
    }
} catch (Exception $e) {
    redirectWithMessage('notifications.php', 'Error loading notification: ' . $e->getMessage(), 'error');
}

// Get notification statistics
$stats = $notification->getStatistics();

// Get recipients with pagination
$page = (int)($_GET['page'] ?? 1);
$recipientsData = $notification->getRecipients($page);
$recipients = $recipientsData['recipients'];
$pagination = $recipientsData['pagination'];

// Generate CSRF token
$csrfToken = generateCSRFToken();

$pageTitle = 'Notification Details - ' . APP_NAME;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.php">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-bell text-primary me-2"></i>
                            Notification Details
                        </h5>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="notifications.php">Notifications</a></li>
                                <li class="breadcrumb-item active">Details</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="notifications.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Notifications
                        </a>
                    </div>
                </div>

                <!-- Notification Details Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Notification Information</h6>
                            <div>
                                <span class="badge bg-<?php echo $notification->isActive() ? 'success' : 'secondary'; ?> me-2">
                                    <?php echo $notification->isActive() ? 'Active' : 'Inactive'; ?>
                                </span>
                                <span class="badge bg-<?php 
                                    echo $notification->getTargetAudience() === 'all' ? 'primary' : 
                                        ($notification->getTargetAudience() === 'donors' ? 'success' : 'info'); 
                                ?>">
                                    <i class="fas fa-<?php 
                                        echo $notification->getTargetAudience() === 'all' ? 'users' : 
                                            ($notification->getTargetAudience() === 'donors' ? 'heart' : 'hand-holding-medical'); 
                                    ?>"></i>
                                    <?php echo ucfirst($notification->getTargetAudience()); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="mb-3"><?php echo htmlspecialchars($notification->getTitle()); ?></h4>
                                <div class="notification-message">
                                    <?php echo nl2br(htmlspecialchars($notification->getMessage())); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="notification-meta">
                                    <div class="mb-3">
                                        <strong>Created:</strong><br>
                                        <small class="text-muted"><?php echo formatDate($notification->getCreatedAt(), DISPLAY_DATETIME_FORMAT); ?></small>
                                    </div>
                                    <div class="mb-3">
                                        <strong>Created By:</strong><br>
                                        <small class="text-muted">System Administrator</small>
                                    </div>
                                    <div class="mb-3">
                                        <strong>Target Audience:</strong><br>
                                        <small class="text-muted"><?php echo ucfirst($notification->getTargetAudience()); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Delivery Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <h3 class="text-primary"><?php echo number_format($stats['total_recipients']); ?></h3>
                                    <p class="mb-0">Total Recipients</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <h3 class="text-success"><?php echo number_format($stats['read_count']); ?></h3>
                                    <p class="mb-0">Read</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <h3 class="text-warning"><?php echo number_format($stats['unread_count']); ?></h3>
                                    <p class="mb-0">Unread</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
