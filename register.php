<?php
/**
 * Simplified Registration Page
 * Blood Donation Management System
 *
 * Basic account creation - role selection happens post-registration in dashboard
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';
require_once 'classes/UnifiedUser.php';

// Start session
if (!startSecureSession()) {
    redirectWithMessage('login.php', ERROR_MESSAGES['SESSION_EXPIRED'], 'error');
}

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    switch ($user['user_type']) {
        case USER_TYPE_ADMIN:
            header('Location: admin/');
            break;
        case USER_TYPE_DONOR:
            header('Location: donor/');
            break;
        case USER_TYPE_RECIPIENT:
            header('Location: recipient/');
            break;
    }
    exit;
}

$errors = [];
$success = '';

// Get blood types for dropdown
$db = Database::getInstance();
$bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Sanitize input - only basic account information
        $userData = [
            'username' => sanitizeInput($_POST['username'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'first_name' => sanitizeInput($_POST['first_name'] ?? ''),
            'last_name' => sanitizeInput($_POST['last_name'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'address' => sanitizeInput($_POST['address'] ?? '')
        ];

        // Validate input - basic validation only
        $validation = validateBasicRegistration($userData);

        if ($validation->isValid) {
            try {
                // Create basic user account without roles
                $user = UnifiedUser::createBasicAccount($userData);

                // Redirect to dashboard for role selection
                redirectWithMessage('dashboard/', 'Registration successful! Please select your role to get started.', 'success');

            } catch (Exception $e) {
                $errors[] = $e->getMessage();
                logEvent('ERROR', 'Registration failed', [
                    'username' => $userData['username'],
                    'error' => $e->getMessage()
                ]);
            }
        } else {
            foreach ($validation->getErrors() as $field => $fieldErrors) {
                $errors = array_merge($errors, $fieldErrors);
            }
        }
    }
}

// Generate CSRF token
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/responsive.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow-sm mt-3 mb-5">
                    <div class="card-header bg-danger text-white text-center">
                        <h4><i class="fas fa-user-plus"></i> Create Account</h4>
                        <p class="mb-0">Join our blood donation community</p>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="register.php" id="registrationForm">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">

                            <!-- Basic Information -->
                            <h6><i class="fas fa-user"></i> Basic Information</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="first_name" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" 
                                               value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="last_name" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" 
                                               value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username *</label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password *</label>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <div class="form-text">Minimum 8 characters with uppercase, lowercase, and number</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">Confirm Password *</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <input type="text" class="form-control" id="address" name="address" 
                                       value="<?php echo htmlspecialchars($_POST['address'] ?? ''); ?>">
                            </div>
                            
                            <hr>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a> *
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-user-plus"></i> Create Account
                                </button>
                            </div>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <p class="mb-0">Already have an account?</p>
                            <a href="login.php" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-sign-in-alt"></i> Login Here
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Terms and Conditions Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Terms and Conditions</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>Blood Donation System Terms of Service</h6>
                    <p>By registering for this blood donation system, you agree to:</p>
                    <ul>
                        <li>Provide accurate and truthful information</li>
                        <li>Follow all medical guidelines and requirements</li>
                        <li>Respect the privacy of other users</li>
                        <li>Use the system responsibly and ethically</li>
                        <li>Comply with all applicable laws and regulations</li>
                    </ul>
                    <p>For donors:</p>
                    <ul>
                        <li>You must meet all eligibility requirements for blood donation</li>
                        <li>You agree to undergo necessary medical screening</li>
                        <li>You understand the donation process and potential risks</li>
                    </ul>
                    <p>For recipients:</p>
                    <ul>
                        <li>You will provide accurate medical information</li>
                        <li>You understand that blood availability is not guaranteed</li>
                        <li>You will work with medical professionals for transfusion needs</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
