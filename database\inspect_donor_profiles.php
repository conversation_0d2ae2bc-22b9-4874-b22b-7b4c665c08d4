<?php
/**
 * Inspect donor_profiles table structure
 * Blood Donation Management System
 */

require_once '../config/database.php';

try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    echo "=== Donor Profiles Table Structure ===\n";
    
    // Describe the donor_profiles table
    $stmt = $connection->query("DESCRIBE donor_profiles");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "Field: " . $column['Field'] . "\n";
        echo "Type: " . $column['Type'] . "\n";
        echo "Null: " . $column['Null'] . "\n";
        echo "Key: " . $column['Key'] . "\n";
        echo "Default: " . $column['Default'] . "\n";
        echo "Extra: " . $column['Extra'] . "\n";
        echo "---\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
