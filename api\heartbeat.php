<?php
/**
 * Heartbeat API - Update User Online Status
 * Blood Donation Management System
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../config/constants.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session
startSecureSession();

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $currentUser = getCurrentUser();
    
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    $isActive = $input['active'] ?? true;
    
    if ($isActive) {
        // Update user online status
        updateUserOnlineStatus($currentUser['id']);
        
        echo json_encode([
            'success' => true,
            'message' => 'Heartbeat received',
            'timestamp' => time(),
            'status' => 'online'
        ]);
    } else {
        // Set user as inactive but not completely offline
        $db = Database::getInstance();
        $db->execute("UPDATE users SET last_activity = CURRENT_TIMESTAMP WHERE id = ?", [$currentUser['id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Status updated',
            'timestamp' => time(),
            'status' => 'inactive'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Heartbeat error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error'
    ]);
}
?>
