-- Migration Script: Unified User System
-- Blood Donation Management System
-- This script updates the database schema to support the unified user system

USE blood_donation_system;

-- Add unified user fields to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS is_unified_user BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS primary_role VARCHAR(20) NULL,
ADD COLUMN IF NOT EXISTS registration_source VARCHAR(20) DEFAULT 'legacy',
ADD COLUMN IF NOT EXISTS password VARCHAR(255) NULL;

-- Update user_type enum to include 'unified'
ALTER TABLE users 
MODIFY COLUMN user_type ENUM('admin', 'donor', 'recipient', 'unified') NOT NULL;

-- Create user_roles table for managing multiple roles per user
CREATE TABLE IF NOT EXISTS user_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    role_type <PERSON><PERSON><PERSON>('donor', 'recipient') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deactivated_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_role (user_id, role_type),
    INDEX idx_user_id (user_id),
    INDEX idx_role_type (role_type),
    INDEX idx_active (is_active)
);

-- Create system_announcements table if it doesn't exist
CREATE TABLE IF NOT EXISTS system_announcements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    target_roles JSON,
    is_active BOOLEAN DEFAULT TRUE,
    is_pinned BOOLEAN DEFAULT FALSE,
    expires_at DATETIME NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by),
    INDEX idx_expires (expires_at)
);

-- Update existing users to have password field populated from password_hash
UPDATE users 
SET password = password_hash 
WHERE password IS NULL AND password_hash IS NOT NULL;

-- Add indexes for better performance
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_is_unified (is_unified_user),
ADD INDEX IF NOT EXISTS idx_primary_role (primary_role),
ADD INDEX IF NOT EXISTS idx_registration_source (registration_source);

-- Make blood_type_id nullable in donor_profiles for gradual profile completion
ALTER TABLE donor_profiles 
MODIFY COLUMN blood_type_id INT NULL;

-- Add application status fields to profiles
ALTER TABLE donor_profiles 
ADD COLUMN IF NOT EXISTS application_status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved',
ADD COLUMN IF NOT EXISTS application_date TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS approved_by INT NULL,
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP NULL;

ALTER TABLE recipient_profiles 
ADD COLUMN IF NOT EXISTS application_status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved',
ADD COLUMN IF NOT EXISTS application_date TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS approved_by INT NULL,
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP NULL;

-- Note: Foreign key constraints for approval tracking can be added later if needed
-- ALTER TABLE donor_profiles ADD CONSTRAINT fk_donor_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL;
-- ALTER TABLE recipient_profiles ADD CONSTRAINT fk_recipient_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL;

-- Create migration log table to track schema changes
CREATE TABLE IF NOT EXISTS schema_migrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT NULL,
    
    INDEX idx_migration_name (migration_name),
    INDEX idx_executed_at (executed_at)
);

-- Record this migration
INSERT IGNORE INTO schema_migrations (migration_name, success) 
VALUES ('migrate_to_unified_system', TRUE);

-- Create sample system announcements
INSERT IGNORE INTO system_announcements (title, content, target_roles, created_by, is_pinned) 
VALUES 
('Welcome to the Unified System', 
 'We have upgraded our system to provide a better experience. You can now easily switch between donor and recipient roles!', 
 '["all"]', 
 1, 
 TRUE),
('New Role Application Process', 
 'You can now apply for additional roles directly from your dashboard. Simply click the "Become a Donor" or "Become a Recipient" buttons.', 
 '["unified"]', 
 1, 
 FALSE);

COMMIT;
