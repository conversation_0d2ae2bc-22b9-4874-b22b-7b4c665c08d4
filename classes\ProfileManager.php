<?php
/**
 * Profile Manager Class
 * Blood Donation Management System
 * 
 * Handles comprehensive user profile management including:
 * - Personal information updates
 * - Password changes with security validation
 * - Profile photo uploads
 * - Role-specific profile management
 * - User preferences
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/validation.php';

class ProfileManager {
    private $db;
    private $userId;
    private $uploadDir;
    private $allowedImageTypes;
    private $maxFileSize;
    
    public function __construct($userId) {
        $this->db = Database::getInstance();
        $this->userId = $userId;
        $this->uploadDir = __DIR__ . '/../uploads/profiles/';
        $this->allowedImageTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        $this->maxFileSize = 2 * 1024 * 1024; // 2MB
        
        // Ensure upload directory exists
        $this->ensureUploadDirectory();
    }
    
    /**
     * Update personal information
     */
    public function updatePersonalInfo($data) {
        $errors = [];
        
        // Validate input
        $firstName = sanitizeInput($data['first_name'] ?? '');
        $lastName = sanitizeInput($data['last_name'] ?? '');
        $phone = sanitizeInput($data['phone'] ?? '');
        $address = sanitizeInput($data['address'] ?? '');
        
        if (empty($firstName)) {
            $errors[] = 'First name is required';
        }
        
        if (empty($lastName)) {
            $errors[] = 'Last name is required';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        try {
            // Update user information
            $sql = "UPDATE users SET 
                    first_name = ?, 
                    last_name = ?, 
                    phone = ?, 
                    address = ?,
                    profile_updated_at = NOW(),
                    updated_at = NOW()
                    WHERE id = ?";
            
            $this->db->execute($sql, [
                $firstName, $lastName, $phone, $address, $this->userId
            ]);
            
            // Log the update
            $this->logProfileUpdate('personal_info', 'Personal information updated');
            
            // Update session data
            $_SESSION['first_name'] = $firstName;
            $_SESSION['last_name'] = $lastName;
            
            return ['success' => true, 'message' => 'Personal information updated successfully'];
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Profile update failed', [
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'errors' => ['Failed to update profile: ' . $e->getMessage()]];
        }
    }
    
    /**
     * Change user password with security validation
     */
    public function changePassword($data) {
        $errors = [];
        
        $currentPassword = $data['current_password'] ?? '';
        $newPassword = $data['new_password'] ?? '';
        $confirmPassword = $data['confirm_password'] ?? '';
        
        if (empty($currentPassword)) {
            $errors[] = 'Current password is required';
        }
        
        if (empty($newPassword)) {
            $errors[] = 'New password is required';
        } elseif (strlen($newPassword) < 8) {
            $errors[] = 'New password must be at least 8 characters long';
        } elseif (!$this->validatePasswordStrength($newPassword)) {
            $errors[] = 'Password must contain uppercase, lowercase, number, and special character';
        }
        
        if ($newPassword !== $confirmPassword) {
            $errors[] = 'Password confirmation does not match';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        try {
            // Verify current password
            $user = $this->db->fetch("SELECT password FROM users WHERE id = ?", [$this->userId]);
            
            if (!$user || $user['password'] !== $currentPassword) {
                return ['success' => false, 'errors' => ['Current password is incorrect']];
            }
            
            // Update password
            $sql = "UPDATE users SET 
                    password = ?, 
                    password_changed_at = NOW(),
                    updated_at = NOW()
                    WHERE id = ?";
            
            $this->db->execute($sql, [$newPassword, $this->userId]);
            
            // Log password change
            $this->logPasswordChange();
            
            return ['success' => true, 'message' => 'Password changed successfully'];
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Password change failed', [
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'errors' => ['Failed to change password: ' . $e->getMessage()]];
        }
    }
    
    /**
     * Upload and process profile photo
     */
    public function uploadProfilePhoto($file) {
        if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'errors' => ['No file uploaded or upload error occurred']];
        }
        
        $errors = [];
        
        // Validate file size
        if ($file['size'] > $this->maxFileSize) {
            $errors[] = 'File size must be less than 2MB';
        }
        
        // Validate file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $this->allowedImageTypes)) {
            $errors[] = 'Only JPEG and PNG files are allowed';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        try {
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'profile_' . $this->userId . '_' . time() . '.' . $extension;
            $filepath = $this->uploadDir . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                return ['success' => false, 'errors' => ['Failed to save uploaded file']];
            }
            
            // Create thumbnail (optional - for future use)
            $this->createThumbnail($filepath, $filename);
            
            // Get current profile photo to delete old one
            $currentUser = $this->db->fetch("SELECT profile_photo FROM users WHERE id = ?", [$this->userId]);
            $oldPhoto = $currentUser['profile_photo'] ?? null;
            
            // Update database
            $this->db->execute(
                "UPDATE users SET profile_photo = ?, updated_at = NOW() WHERE id = ?", 
                [$filename, $this->userId]
            );
            
            // Record in profile_pictures table
            $this->db->execute(
                "INSERT INTO profile_pictures (user_id, filename, original_filename, file_size, mime_type) 
                 VALUES (?, ?, ?, ?, ?)",
                [$this->userId, $filename, $file['name'], $file['size'], $mimeType]
            );
            
            // Delete old photo if exists
            if ($oldPhoto && file_exists($this->uploadDir . $oldPhoto)) {
                unlink($this->uploadDir . $oldPhoto);
                // Also delete thumbnail if exists
                $thumbnailPath = $this->uploadDir . 'thumbnails/thumb_' . $oldPhoto;
                if (file_exists($thumbnailPath)) {
                    unlink($thumbnailPath);
                }
            }
            
            // Log the update
            $this->logProfileUpdate('profile_photo', 'Profile photo updated');
            
            return ['success' => true, 'message' => 'Profile photo updated successfully'];
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Profile photo upload failed', [
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'errors' => ['Failed to upload photo: ' . $e->getMessage()]];
        }
    }
    
    /**
     * Update donor profile information
     */
    public function updateDonorProfile($data) {
        $errors = [];
        
        $bloodTypeId = (int)($data['blood_type_id'] ?? 0);
        $weight = (float)($data['weight'] ?? 0);
        $birthDate = $data['birth_date'] ?? null;
        $medicalConditions = sanitizeInput($data['medical_conditions'] ?? '');
        $preferredLocation = sanitizeInput($data['preferred_donation_location'] ?? '');
        
        if ($bloodTypeId <= 0) {
            $errors[] = 'Blood type is required';
        }
        
        if ($weight > 0 && $weight < 50) {
            $errors[] = 'Weight must be at least 50kg for blood donation';
        }
        
        if (!empty($birthDate)) {
            $birthDateTime = new DateTime($birthDate);
            $now = new DateTime();
            $age = $now->diff($birthDateTime)->y;
            
            if ($age < 18) {
                $errors[] = 'Must be at least 18 years old to donate blood';
            }
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        try {
            // Check if donor profile exists
            $existingProfile = $this->db->fetch(
                "SELECT user_id FROM donor_profiles WHERE user_id = ?", 
                [$this->userId]
            );
            
            if ($existingProfile) {
                // Update existing profile
                $sql = "UPDATE donor_profiles SET 
                        blood_type_id = ?, 
                        weight = ?, 
                        birth_date = ?, 
                        medical_conditions = ?,
                        preferred_donation_location = ?,
                        updated_at = NOW()
                        WHERE user_id = ?";
                
                $this->db->execute($sql, [
                    $bloodTypeId, $weight, $birthDate, $medicalConditions, 
                    $preferredLocation, $this->userId
                ]);
            } else {
                // Create new profile
                $sql = "INSERT INTO donor_profiles 
                        (user_id, blood_type_id, weight, birth_date, medical_conditions, preferred_donation_location) 
                        VALUES (?, ?, ?, ?, ?, ?)";
                
                $this->db->execute($sql, [
                    $this->userId, $bloodTypeId, $weight, $birthDate, 
                    $medicalConditions, $preferredLocation
                ]);
            }
            
            // Log the update
            $this->logProfileUpdate('donor_profile', 'Donor profile updated');
            
            return ['success' => true, 'message' => 'Donor profile updated successfully'];
            
        } catch (Exception $e) {
            logEvent('ERROR', 'Donor profile update failed', [
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'errors' => ['Failed to update donor profile: ' . $e->getMessage()]];
        }
    }

    /**
     * Update recipient profile information
     */
    public function updateRecipientProfile($data) {
        $errors = [];

        $medicalCondition = sanitizeInput($data['medical_condition'] ?? '');
        $emergencyContact = sanitizeInput($data['emergency_contact'] ?? '');
        $emergencyPhone = sanitizeInput($data['emergency_phone'] ?? '');
        $doctorName = sanitizeInput($data['doctor_name'] ?? '');
        $doctorContact = sanitizeInput($data['doctor_contact'] ?? '');
        $preferredHospital = sanitizeInput($data['preferred_hospital'] ?? '');

        if (empty($medicalCondition)) {
            $errors[] = 'Medical condition is required';
        }

        if (empty($emergencyContact)) {
            $errors[] = 'Emergency contact name is required';
        }

        if (empty($emergencyPhone)) {
            $errors[] = 'Emergency contact phone is required';
        }

        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        try {
            // Check if recipient profile exists
            $existingProfile = $this->db->fetch(
                "SELECT user_id FROM recipient_profiles WHERE user_id = ?",
                [$this->userId]
            );

            if ($existingProfile) {
                // Update existing profile
                $sql = "UPDATE recipient_profiles SET
                        medical_condition = ?,
                        emergency_contact = ?,
                        emergency_phone = ?,
                        doctor_name = ?,
                        doctor_contact = ?,
                        preferred_hospital = ?,
                        updated_at = NOW()
                        WHERE user_id = ?";

                $this->db->execute($sql, [
                    $medicalCondition, $emergencyContact, $emergencyPhone,
                    $doctorName, $doctorContact, $preferredHospital, $this->userId
                ]);
            } else {
                // Create new profile
                $sql = "INSERT INTO recipient_profiles
                        (user_id, medical_condition, emergency_contact, emergency_phone, doctor_name, doctor_contact, preferred_hospital)
                        VALUES (?, ?, ?, ?, ?, ?, ?)";

                $this->db->execute($sql, [
                    $this->userId, $medicalCondition, $emergencyContact, $emergencyPhone,
                    $doctorName, $doctorContact, $preferredHospital
                ]);
            }

            // Log the update
            $this->logProfileUpdate('recipient_profile', 'Recipient profile updated');

            return ['success' => true, 'message' => 'Recipient profile updated successfully'];

        } catch (Exception $e) {
            logEvent('ERROR', 'Recipient profile update failed', [
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'errors' => ['Failed to update recipient profile: ' . $e->getMessage()]];
        }
    }

    /**
     * Update user preferences
     */
    public function updatePreferences($data) {
        try {
            $emailNotifications = isset($data['email_notifications']) ? 1 : 0;
            $smsNotifications = isset($data['sms_notifications']) ? 1 : 0;
            $profileVisibility = $data['profile_visibility'] ?? 'private';
            $showContactInfo = isset($data['show_contact_info']) ? 1 : 0;
            $showDonationHistory = isset($data['show_donation_history']) ? 1 : 0;
            $theme = $data['theme'] ?? 'light';
            $language = $data['language'] ?? 'en';
            $timezone = $data['timezone'] ?? 'UTC';

            // Validate enum values
            if (!in_array($profileVisibility, ['public', 'private', 'contacts_only'])) {
                $profileVisibility = 'private';
            }

            if (!in_array($theme, ['light', 'dark', 'auto'])) {
                $theme = 'light';
            }

            // Update or insert preferences
            $sql = "INSERT INTO user_profile_preferences
                    (user_id, email_notifications, sms_notifications, profile_visibility,
                     show_contact_info, show_donation_history, theme, language, timezone, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE
                    email_notifications = VALUES(email_notifications),
                    sms_notifications = VALUES(sms_notifications),
                    profile_visibility = VALUES(profile_visibility),
                    show_contact_info = VALUES(show_contact_info),
                    show_donation_history = VALUES(show_donation_history),
                    theme = VALUES(theme),
                    language = VALUES(language),
                    timezone = VALUES(timezone),
                    updated_at = NOW()";

            $this->db->execute($sql, [
                $this->userId, $emailNotifications, $smsNotifications, $profileVisibility,
                $showContactInfo, $showDonationHistory, $theme, $language, $timezone
            ]);

            // Log the update
            $this->logProfileUpdate('preferences', 'User preferences updated');

            return ['success' => true, 'message' => 'Preferences updated successfully'];

        } catch (Exception $e) {
            logEvent('ERROR', 'Preferences update failed', [
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'errors' => ['Failed to update preferences: ' . $e->getMessage()]];
        }
    }

    /**
     * Get complete profile data
     */
    public function getProfileData() {
        try {
            $preferences = $this->db->fetch(
                "SELECT * FROM user_profile_preferences WHERE user_id = ?",
                [$this->userId]
            );

            $user = $this->db->fetch(
                "SELECT password_changed_at, profile_updated_at FROM users WHERE id = ?",
                [$this->userId]
            );

            return array_merge($preferences ?: [], $user ?: []);

        } catch (Exception $e) {
            logEvent('ERROR', 'Failed to get profile data', [
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get donor profile data
     */
    public function getDonorProfile() {
        try {
            return $this->db->fetch(
                "SELECT dp.*, bt.type as blood_type_name
                 FROM donor_profiles dp
                 LEFT JOIN blood_types bt ON dp.blood_type_id = bt.id
                 WHERE dp.user_id = ?",
                [$this->userId]
            );
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Get recipient profile data
     */
    public function getRecipientProfile() {
        try {
            return $this->db->fetch(
                "SELECT * FROM recipient_profiles WHERE user_id = ?",
                [$this->userId]
            );
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Validate password strength
     */
    private function validatePasswordStrength($password) {
        // Check for at least 8 characters, uppercase, lowercase, number, and special character
        return strlen($password) >= 8 &&
               preg_match('/[a-z]/', $password) &&
               preg_match('/[A-Z]/', $password) &&
               preg_match('/[0-9]/', $password) &&
               preg_match('/[^A-Za-z0-9]/', $password);
    }

    /**
     * Create thumbnail for profile photo
     */
    private function createThumbnail($filepath, $filename) {
        try {
            $thumbnailDir = $this->uploadDir . 'thumbnails/';
            if (!is_dir($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
            }

            $thumbnailPath = $thumbnailDir . 'thumb_' . $filename;

            // Get image info
            $imageInfo = getimagesize($filepath);
            if (!$imageInfo) return false;

            $width = $imageInfo[0];
            $height = $imageInfo[1];
            $type = $imageInfo[2];

            // Calculate thumbnail dimensions (100x100)
            $thumbWidth = 100;
            $thumbHeight = 100;

            // Create image resource based on type
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $source = imagecreatefromjpeg($filepath);
                    break;
                case IMAGETYPE_PNG:
                    $source = imagecreatefrompng($filepath);
                    break;
                default:
                    return false;
            }

            // Create thumbnail
            $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);

            // Preserve transparency for PNG
            if ($type == IMAGETYPE_PNG) {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
            }

            imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $thumbWidth, $thumbHeight, $width, $height);

            // Save thumbnail
            switch ($type) {
                case IMAGETYPE_JPEG:
                    imagejpeg($thumbnail, $thumbnailPath, 90);
                    break;
                case IMAGETYPE_PNG:
                    imagepng($thumbnail, $thumbnailPath);
                    break;
            }

            // Clean up
            imagedestroy($source);
            imagedestroy($thumbnail);

            return true;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Ensure upload directory exists
     */
    private function ensureUploadDirectory() {
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }

        $thumbnailDir = $this->uploadDir . 'thumbnails/';
        if (!is_dir($thumbnailDir)) {
            mkdir($thumbnailDir, 0755, true);
        }
    }

    /**
     * Log profile update
     */
    private function logProfileUpdate($fieldName, $description) {
        try {
            $this->db->execute(
                "INSERT INTO profile_update_log (user_id, field_name, new_value, ip_address, user_agent)
                 VALUES (?, ?, ?, ?, ?)",
                [
                    $this->userId,
                    $fieldName,
                    $description,
                    $_SERVER['REMOTE_ADDR'] ?? null,
                    $_SERVER['HTTP_USER_AGENT'] ?? null
                ]
            );
        } catch (Exception $e) {
            // Ignore logging errors
        }
    }

    /**
     * Log password change
     */
    private function logPasswordChange() {
        try {
            $this->db->execute(
                "INSERT INTO password_change_history (user_id, ip_address, user_agent)
                 VALUES (?, ?, ?)",
                [
                    $this->userId,
                    $_SERVER['REMOTE_ADDR'] ?? null,
                    $_SERVER['HTTP_USER_AGENT'] ?? null
                ]
            );
        } catch (Exception $e) {
            // Ignore logging errors
        }
    }
}
