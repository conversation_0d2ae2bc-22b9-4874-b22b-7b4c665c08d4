<?php
/**
 * Unified User Class
 * Blood Donation Management System
 * 
 * Handles unified user system with multiple roles
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/User.php';
require_once __DIR__ . '/Donor.php';
require_once __DIR__ . '/Recipient.php';

class UnifiedUser extends User {
    protected $roles = [];
    protected $primaryRole;
    protected $isUnifiedUser;
    protected $registrationSource;
    
    public function __construct($id = null) {
        parent::__construct($id);
        
        if ($id) {
            $this->loadUserRoles();
        }
    }
    
    /**
     * Load user roles from database
     */
    protected function loadUserRoles() {
        $sql = "SELECT role_type, is_active, activated_at, deactivated_at 
                FROM user_roles 
                WHERE user_id = ? 
                ORDER BY activated_at ASC";
        
        $roles = $this->db->fetchAll($sql, [$this->id]);
        
        foreach ($roles as $role) {
            $this->roles[$role['role_type']] = [
                'is_active' => (bool)$role['is_active'],
                'activated_at' => $role['activated_at'],
                'deactivated_at' => $role['deactivated_at']
            ];
        }
        
        // Load additional unified user data
        $userData = $this->db->fetch("SELECT is_unified_user, primary_role, registration_source FROM users WHERE id = ?", [$this->id]);
        if ($userData) {
            $this->isUnifiedUser = (bool)$userData['is_unified_user'];
            $this->primaryRole = $userData['primary_role'];
            $this->registrationSource = $userData['registration_source'];
        }
    }
    
    /**
     * Create a basic user account without roles
     */
    public static function createBasicAccount($userData) {
        $db = Database::getInstance();

        try {
            $db->beginTransaction();

            // Validate required fields
            if (empty($userData['username']) || empty($userData['password'])) {
                throw new Exception('Username and password are required');
            }

            // Generate a unique placeholder email for database compatibility
            $placeholderEmail = $userData['username'] . '@placeholder.local';

            // Check if username already exists
            $existingUser = $db->fetch("SELECT id FROM users WHERE username = ?",
                [$userData['username']]);

            if ($existingUser) {
                throw new Exception('Username already exists');
            }

            // Store password in plaintext (WARNING: insecure)
            $passwordHash = $userData['password'];

            // Insert user with unified type (use `password` column)
            $sql = "INSERT INTO users (username, email, password, user_type, first_name, last_name,
                    phone, address, status, email_verified, is_unified_user, registration_source, created_at)
                    VALUES (?, ?, ?, 'unified', ?, ?, ?, ?, ?, ?, TRUE, 'unified', NOW())";

            $db->execute($sql, [
                $userData['username'],
                $placeholderEmail,
                $passwordHash,
                $userData['first_name'],
                $userData['last_name'],
                $userData['phone'] ?? '',
                $userData['address'] ?? '',
                $userData['status'] ?? USER_STATUS_ACTIVE,
                false // email_verified set to false since no real email
            ]);

            $userId = $db->getConnection()->lastInsertId();

            $db->commit();

            // Log successful registration
            logEvent('INFO', 'Basic user account created', [
                'user_id' => $userId,
                'username' => $userData['username']
            ]);

            return new self($userId);

        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    /**
     * Create a new unified user
     */
    public static function create($userData, $selectedRoles = []) {
        $db = Database::getInstance();
        
        try {
            $db->beginTransaction();
            
            // Validate required fields
            if (empty($userData['username']) || empty($userData['email']) || empty($userData['password'])) {
                throw new Exception('Username, email, and password are required');
            }
            
            // Check if username or email already exists
            $existingUser = $db->fetch("SELECT id FROM users WHERE username = ? OR email = ?", 
                [$userData['username'], $userData['email']]);
            
            if ($existingUser) {
                throw new Exception('Username or email already exists');
            }
            
            // Store password in plaintext (WARNING: insecure)
            $passwordHash = $userData['password'];
            
            // Determine primary role
            $primaryRole = null;
            if (!empty($selectedRoles)) {
                $primaryRole = $selectedRoles[0]; // First selected role becomes primary
            }
            
            // Insert user with unified type (use `password` column)
            $sql = "INSERT INTO users (username, email, password, user_type, first_name, last_name, 
                    phone, address, status, email_verified, is_unified_user, primary_role, registration_source, created_at) 
                    VALUES (?, ?, ?, 'unified', ?, ?, ?, ?, ?, ?, TRUE, ?, 'unified', NOW())";
            
            $db->execute($sql, [
                $userData['username'],
                $userData['email'],
                $passwordHash,
                $userData['first_name'],
                $userData['last_name'],
                $userData['phone'] ?? '',
                $userData['address'] ?? '',
                $userData['status'] ?? USER_STATUS_ACTIVE,
                $userData['email_verified'] ?? false,
                $primaryRole
            ]);
            
            $userId = $db->lastInsertId();
            
            // Create user roles
            foreach ($selectedRoles as $role) {
                $db->execute("INSERT INTO user_roles (user_id, role_type, is_active) VALUES (?, ?, TRUE)", 
                    [$userId, $role]);
            }
            
            // Create user preferences
            $db->execute("INSERT INTO user_preferences (user_id, default_role) VALUES (?, ?)", 
                [$userId, $primaryRole]);
            
            // Create role-specific profiles
            foreach ($selectedRoles as $role) {
                if ($role === 'donor' && !empty($userData['blood_type_id'])) {
                    // Create donor profile
                    $donorData = [
                        'user_id' => $userId,
                        'blood_type_id' => $userData['blood_type_id'],
                        'weight' => $userData['weight'] ?? 0,
                        'birth_date' => $userData['birth_date'] ?? null,
                        'medical_conditions' => $userData['medical_conditions'] ?? '',
                        'eligibility_status' => 'eligible'
                    ];
                    
                    $donorSql = "INSERT INTO donor_profiles (user_id, blood_type_id, weight, birth_date, medical_conditions, eligibility_status) 
                                VALUES (?, ?, ?, ?, ?, ?)";
                    $db->execute($donorSql, array_values($donorData));
                    
                } elseif ($role === 'recipient') {
                    // Create recipient profile
                    $recipientData = [
                        'user_id' => $userId,
                        'medical_condition' => $userData['medical_condition'] ?? '',
                        'emergency_contact' => $userData['emergency_contact'] ?? '',
                        'emergency_phone' => $userData['emergency_phone'] ?? '',
                        'doctor_name' => $userData['doctor_name'] ?? '',
                        'doctor_contact' => $userData['doctor_contact'] ?? ''
                    ];
                    
                    $recipientSql = "INSERT INTO recipient_profiles (user_id, medical_condition, emergency_contact, emergency_phone, doctor_name, doctor_contact) 
                                    VALUES (?, ?, ?, ?, ?, ?)";
                    $db->execute($recipientSql, array_values($recipientData));
                }
            }
            
            $db->commit();
            
            // Log successful registration
            logEvent('INFO', 'Unified user registered', [
                'user_id' => $userId,
                'username' => $userData['username'],
                'roles' => $selectedRoles,
                'primary_role' => $primaryRole
            ]);
            
            return new self($userId);
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
    
    /**
     * Add a role to the user
     */
    public function addRole($roleType) {
        if ($this->hasRole($roleType)) {
            return true; // Role already exists
        }
        
        try {
            $this->db->beginTransaction();
            
            // Add role
            $this->db->execute("INSERT INTO user_roles (user_id, role_type, is_active) VALUES (?, ?, TRUE)", 
                [$this->id, $roleType]);
            
            // Create role-specific profile if needed
            if ($roleType === 'donor') {
                // Check if donor profile exists
                $result = $this->db->fetch("SELECT COUNT(*) as count FROM donor_profiles WHERE user_id = ?", [$this->id]);
                $exists = $result ? $result['count'] : 0;
                if (!$exists) {
                    $this->db->execute("INSERT INTO donor_profiles (user_id, blood_type_id, eligibility_status) VALUES (?, 1, 'eligible')", 
                        [$this->id]);
                }
            } elseif ($roleType === 'recipient') {
                // Check if recipient profile exists
                $result = $this->db->fetch("SELECT COUNT(*) as count FROM recipient_profiles WHERE user_id = ?", [$this->id]);
                $exists = $result ? $result['count'] : 0;
                if (!$exists) {
                    $this->db->execute("INSERT INTO recipient_profiles (user_id) VALUES (?)", [$this->id]);
                }
            }
            
            // Update primary role if this is the first role
            if (empty($this->primaryRole)) {
                $this->db->execute("UPDATE users SET primary_role = ? WHERE id = ?", [$roleType, $this->id]);
                $this->primaryRole = $roleType;
            }
            
            $this->db->commit();
            
            // Reload roles
            $this->loadUserRoles();
            
            logEvent('INFO', 'Role added to user', [
                'user_id' => $this->id,
                'role_type' => $roleType
            ]);
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Remove a role from the user
     */
    public function removeRole($roleType) {
        if (!$this->hasRole($roleType)) {
            return true; // Role doesn't exist
        }
        
        // Don't allow removing the last role
        if (count($this->getActiveRoles()) <= 1) {
            throw new Exception('Cannot remove the last role from a user');
        }
        
        $this->db->execute("UPDATE user_roles SET is_active = FALSE, deactivated_at = NOW() WHERE user_id = ? AND role_type = ?", 
            [$this->id, $roleType]);
        
        // Update primary role if needed
        if ($this->primaryRole === $roleType) {
            $activeRoles = $this->getActiveRoles();
            $newPrimaryRole = reset($activeRoles);
            $this->db->execute("UPDATE users SET primary_role = ? WHERE id = ?", [$newPrimaryRole, $this->id]);
            $this->primaryRole = $newPrimaryRole;
        }
        
        // Reload roles
        $this->loadUserRoles();
        
        logEvent('INFO', 'Role removed from user', [
            'user_id' => $this->id,
            'role_type' => $roleType
        ]);
        
        return true;
    }
    
    /**
     * Check if user has a specific role
     */
    public function hasRole($roleType) {
        return isset($this->roles[$roleType]) && $this->roles[$roleType]['is_active'];
    }
    
    /**
     * Get all active roles
     */
    public function getActiveRoles() {
        $activeRoles = [];
        foreach ($this->roles as $roleType => $roleData) {
            if ($roleData['is_active']) {
                $activeRoles[] = $roleType;
            }
        }
        return $activeRoles;
    }
    
    /**
     * Get primary role
     */
    public function getPrimaryRole() {
        return $this->primaryRole;
    }
    
    /**
     * Set primary role
     */
    public function setPrimaryRole($roleType) {
        if (!$this->hasRole($roleType)) {
            throw new Exception('Cannot set primary role to a role the user does not have');
        }
        
        $this->db->execute("UPDATE users SET primary_role = ? WHERE id = ?", [$roleType, $this->id]);
        $this->db->execute("UPDATE user_preferences SET default_role = ? WHERE user_id = ?", [$roleType, $this->id]);
        
        $this->primaryRole = $roleType;
        
        return true;
    }
    
    /**
     * Get donor instance if user has donor role
     */
    public function getDonorInstance() {
        if (!$this->hasRole('donor')) {
            return null;
        }
        return new Donor($this->id);
    }
    
    /**
     * Get recipient instance if user has recipient role
     */
    public function getRecipientInstance() {
        if (!$this->hasRole('recipient')) {
            return null;
        }
        return new Recipient($this->id);
    }
    
    /**
     * Check if user is unified
     */
    public function isUnifiedUser() {
        return $this->isUnifiedUser;
    }
    
    /**
     * Get registration source
     */
    public function getRegistrationSource() {
        return $this->registrationSource;
    }
}
