<?php
/**
 * Send Chat Message API
 * Blood Donation Management System
 */

require_once '../../config/constants.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Start session and check authentication
startSecureSession();

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit;
}

// Validate CSRF token
if (!verifyCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

$currentUser = getCurrentUser();
$receiverId = (int)($input['receiver_id'] ?? 0);
$message = trim($input['message'] ?? '');

// Validate input
if ($receiverId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid receiver ID']);
    exit;
}

if (empty($message)) {
    echo json_encode(['success' => false, 'message' => 'Message cannot be empty']);
    exit;
}

if (strlen($message) > MAX_MESSAGE_LENGTH) {
    echo json_encode(['success' => false, 'message' => 'Message too long']);
    exit;
}

// Check if user can chat with receiver
if (!canChatWith($currentUser['id'], $receiverId)) {
    echo json_encode(['success' => false, 'message' => 'You cannot chat with this user']);
    exit;
}

try {
    $db = Database::getInstance();
    
    // Insert message
    $sql = "INSERT INTO chat_messages (sender_id, receiver_id, message) VALUES (?, ?, ?)";
    $db->execute($sql, [$currentUser['id'], $receiverId, $message]);
    
    $messageId = $db->lastInsertId();
    
    // Log the chat message
    logEvent('INFO', 'Chat message sent', [
        'sender_id' => $currentUser['id'],
        'receiver_id' => $receiverId,
        'message_id' => $messageId
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Message sent successfully',
        'message_id' => $messageId
    ]);
    
} catch (Exception $e) {
    logEvent('ERROR', 'Chat message send failed', [
        'sender_id' => $currentUser['id'],
        'receiver_id' => $receiverId,
        'error' => $e->getMessage()
    ]);
    
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to send message']);
}
?>
