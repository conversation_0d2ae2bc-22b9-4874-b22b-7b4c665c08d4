<?php
/**
 * Find Users for Chat - Restricted Messaging System
 * Blood Donation Management System
 * 
 * Rules:
 * - Regular users can only see and message administrators
 * - Administrators can see and message all users
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';
require_once '../classes/UnifiedUser.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

// Get search parameters
$search = sanitizeInput($_GET['search'] ?? '');
$roleFilter = sanitizeInput($_GET['role'] ?? '');
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Use the new restricted messaging functions
$total = getMessagableUsersCount($currentUser['id'], $search);
$totalPages = ceil($total / $limit);
$users = getMessagableUsers($currentUser['id'], $search, $limit, $offset);

// Generate CSRF token
$csrfToken = generateCSRFToken();

// Page title and description based on user type
$pageTitle = $currentUser['user_type'] === USER_TYPE_ADMIN ? 'Find Users to Message' : 'Contact Administrators';
$pageDescription = $currentUser['user_type'] === USER_TYPE_ADMIN 
    ? 'Search and message any user in the system' 
    : 'Find and contact system administrators for support';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - Blood Donation Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .user-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border: 1px solid #e0e0e0;
        }
        
        .user-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #6c757d; }
        
        .role-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .admin-badge { background-color: #dc3545; }
        .donor-badge { background-color: #28a745; }
        .recipient-badge { background-color: #007bff; }
        .unified-badge { background-color: #6f42c1; }
        
        .search-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .restriction-notice {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border: none;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/">
                <i class="fas fa-tint"></i> Blood Donation Management System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left"></i> Back to Chat
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2>
                    <i class="fas fa-search"></i> <?php echo htmlspecialchars($pageTitle); ?>
                </h2>
                <p class="text-muted"><?php echo htmlspecialchars($pageDescription); ?></p>
            </div>
        </div>

        <?php if ($currentUser['user_type'] !== USER_TYPE_ADMIN): ?>
        <!-- Restriction Notice for Regular Users -->
        <div class="restriction-notice">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle fa-2x me-3 text-primary"></i>
                <div>
                    <h5 class="mb-1">Messaging Restrictions</h5>
                    <p class="mb-0">For privacy and security, you can only send messages to system administrators. 
                    If you need to contact other users, please reach out to an administrator who can facilitate the communication.</p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Search and Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="search-container">
                    <form method="GET" class="row g-3">
                        <div class="col-md-8">
                            <label for="search" class="form-label">Search Users</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Search by name or username...">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-light me-2">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <a href="find-users-restricted.php" class="btn btn-outline-light">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>
                            <i class="fas fa-users"></i> 
                            Found <?php echo number_format($total); ?> user<?php echo $total !== 1 ? 's' : ''; ?>
                            <?php if ($currentUser['user_type'] !== USER_TYPE_ADMIN): ?>
                                (Administrators only)
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No users found</h5>
                                <?php if ($currentUser['user_type'] === USER_TYPE_ADMIN): ?>
                                    <p class="text-muted">Try adjusting your search criteria or check back later for new members.</p>
                                <?php else: ?>
                                    <p class="text-muted">No administrators match your search criteria. Try a different search term.</p>
                                <?php endif; ?>
                                <a href="find-users-restricted.php" class="btn btn-outline-primary">Clear Search</a>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($users as $user): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card user-card h-100">
                                            <div class="card-body text-center">
                                                <!-- User Avatar -->
                                                <div class="mb-3">
                                                    <?php if (!empty($user['profile_photo']) && file_exists("../uploads/profiles/" . $user['profile_photo'])): ?>
                                                        <img src="../uploads/profiles/<?php echo htmlspecialchars($user['profile_photo']); ?>" 
                                                             alt="Profile Photo" class="user-avatar">
                                                    <?php else: ?>
                                                        <div class="user-avatar bg-secondary d-flex align-items-center justify-content-center">
                                                            <i class="fas fa-user fa-2x text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <!-- User Info -->
                                                <h6 class="card-title mb-1">
                                                    <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                                </h6>
                                                <p class="text-muted small mb-2">@<?php echo htmlspecialchars($user['username']); ?></p>
                                                
                                                <!-- User Type Badge -->
                                                <div class="mb-2">
                                                    <?php if ($user['user_type'] === USER_TYPE_ADMIN): ?>
                                                        <span class="badge admin-badge role-badge">
                                                            <i class="fas fa-shield-alt"></i> Administrator
                                                        </span>
                                                    <?php elseif ($user['is_unified_user']): ?>
                                                        <span class="badge unified-badge role-badge">
                                                            <i class="fas fa-users"></i> 
                                                            <?php echo htmlspecialchars(ucfirst(str_replace(',', ', ', $user['roles'] ?? 'User'))); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge <?php echo $user['user_type'] === 'donor' ? 'donor-badge' : 'recipient-badge'; ?> role-badge">
                                                            <i class="fas fa-<?php echo $user['user_type'] === 'donor' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                                                            <?php echo htmlspecialchars(ucfirst($user['user_type'])); ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <!-- Online Status -->
                                                <div class="mb-3">
                                                    <span class="status-indicator <?php echo $user['is_online'] ? 'status-online' : 'status-offline'; ?>"></span>
                                                    <small class="text-muted">
                                                        <?php echo $user['is_online'] ? 'Online' : 'Offline'; ?>
                                                    </small>
                                                </div>
                                                
                                                <!-- Action Button -->
                                                <a href="index.php?user_id=<?php echo $user['id']; ?>" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-comment"></i> Start Chat
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="User search pagination">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($roleFilter); ?>">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($roleFilter); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($roleFilter); ?>">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
