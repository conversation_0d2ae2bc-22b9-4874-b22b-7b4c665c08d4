-- Donor Medical Fields Migration
-- Blood Donation Management System
-- Adds comprehensive medical fields to donor_profiles table

USE blood_donation_system;

-- Add medical fields to donor_profiles table
ALTER TABLE donor_profiles 
ADD COLUMN IF NOT EXISTS medications TEXT NULL,
ADD COLUMN IF NOT EXISTS allergies TEXT NULL,
ADD COLUMN IF NOT EXISTS emergency_contact VARCHAR(100) NULL,
ADD COLUMN IF NOT EXISTS emergency_phone VARCHAR(20) NULL,
ADD COLUMN IF NOT EXISTS relationship VARCHAR(50) NULL;

-- Record this migration
INSERT INTO schema_migrations (migration_name, success) 
VALUES ('donor_medical_fields_migration', TRUE)
ON DUPLICATE KEY UPDATE 
    executed_at = CURRENT_TIMESTAMP,
    success = TRUE,
    error_message = NULL;

COMMIT;
