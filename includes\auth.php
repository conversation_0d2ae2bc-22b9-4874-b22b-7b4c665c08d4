<?php
/**
 * Authentication Functions
 * Blood Donation Management System
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/security.php';

/**
 * Start secure session
 */
function startSecureSession() {
    // Configure session settings
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_strict_mode', 1);
    
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Check session timeout
    if (isset($_SESSION['last_activity']) && 
        (time() - $_SESSION['last_activity']) > SESSION_TIMEOUT) {
        session_destroy();
        return false;
    }
    
    $_SESSION['last_activity'] = time();
    
    // Regenerate session ID periodically
    if (!isset($_SESSION['created'])) {
        $_SESSION['created'] = time();
    } else if (time() - $_SESSION['created'] > 1800) {
        session_regenerate_id(true);
        $_SESSION['created'] = time();
    }
    
    return true;
}

/**
 * Authenticate user login
 */
function authenticateUser($username, $password) {
    $db = Database::getInstance();

    // Check rate limiting
    $identifier = $username . '_' . ($_SERVER['REMOTE_ADDR'] ?? '');
    if (!checkRateLimit($identifier, MAX_LOGIN_ATTEMPTS, LOGIN_LOCKOUT_TIME)) {
        logSecurityEvent('LOGIN_RATE_LIMITED', ['username' => $username]);
        return false;
    }

    // Check for admin login in database
    // Use the new `password` column but alias it as `password_hash` for compatibility
    $adminUser = $db->fetch("SELECT id, username, password AS password_hash, first_name, last_name, status FROM users WHERE username = ? AND user_type = ? LIMIT 1", [$username, USER_TYPE_ADMIN]);

    if ($adminUser) {
        if ($password === $adminUser['password_hash']) {
            if ($adminUser['status'] !== USER_STATUS_ACTIVE) {
                recordRateLimitAttempt($identifier, 'inactive_admin_login');
                logSecurityEvent('ADMIN_LOGIN_FAILED', ['username' => $username, 'reason' => 'inactive_account']);
                return false;
            }

            logSecurityEvent('ADMIN_LOGIN_SUCCESS', ['username' => $username]);

            // Log admin login for audit trail
            if (file_exists(__DIR__ . '/audit.php')) {
                require_once __DIR__ . '/audit.php';
                logAdminAction('ADMIN_LOGIN', [
                    'username' => $username,
                    'login_time' => date('Y-m-d H:i:s'),
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ], $adminUser['id']);
            }

            return [
                'id' => $adminUser['id'],
                'username' => $adminUser['username'],
                'user_type' => USER_TYPE_ADMIN,
                'first_name' => $adminUser['first_name'],
                'last_name' => $adminUser['last_name'],
                'status' => $adminUser['status']
            ];
        }
        recordRateLimitAttempt($identifier, 'failed_admin_login');
        logSecurityEvent('ADMIN_LOGIN_FAILED', ['username' => $username]);
        return false;
    }

    // Check regular users - only by username
    $sql = "SELECT id, username, password AS password_hash, user_type, first_name, last_name, status
            FROM users
            WHERE username = ? AND status = ?";

    $user = $db->fetch($sql, [$username, USER_STATUS_ACTIVE]);

    if (!$user) {
        recordRateLimitAttempt($identifier, 'failed_login');
        logSecurityEvent('LOGIN_FAILED_USER_NOT_FOUND', ['username' => $username]);
        return false;
    }

    if ($password === $user['password_hash']) {
        // Update last login
        $db->execute("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);

        logSecurityEvent('LOGIN_SUCCESS', ['username' => $username, 'user_id' => $user['id']]);

        // Remove password hash from returned data
        unset($user['password_hash']);
        return $user;
    }

    recordRateLimitAttempt($identifier, 'failed_login');
    logSecurityEvent('LOGIN_FAILED_WRONG_PASSWORD', ['username' => $username, 'user_id' => $user['id']]);
    return false;
}

/**
 * Login user
 */
function loginUser($user) {
    session_regenerate_id(true);
    
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['user_type'] = $user['user_type'];
    $_SESSION['first_name'] = $user['first_name'];
    $_SESSION['last_name'] = $user['last_name'];
    $_SESSION['login_time'] = time();
    
    // Log login event
    logEvent('INFO', 'User logged in', [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'user_type' => $user['user_type'],
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    
    return true;
}

/**
 * Logout user
 */
function logoutUser() {
    $user = getCurrentUser();

    if ($user) {
        // Set user offline before logging out
        if (function_exists('setUserOffline')) {
            setUserOffline($user['id']);
        }

        logEvent('INFO', 'User logged out', [
            'user_id' => $user['id'],
            'username' => $user['username']
        ]);
    }

    session_destroy();
    return true;
}

/**
 * Register new user
 */
function registerUser($userData) {
    $db = Database::getInstance();
    
    try {
        $db->beginTransaction();
        
        // Check if username or email already exists
        $checkSql = "SELECT id FROM users WHERE username = ? OR email = ?";
        $existing = $db->fetch($checkSql, [$userData['username'], $userData['email']]);
        
        if ($existing) {
            throw new Exception('Username or email already exists');
        }
        
        // Store password in plaintext (WARNING: insecure)
        $passwordHash = $userData['password'];
        
        // Insert user (using `password` column instead of `password_hash`)
        $userSql = "INSERT INTO users (username, email, password, user_type, first_name, last_name, phone, address, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $db->execute($userSql, [
            $userData['username'],
            $userData['email'],
            $passwordHash,
            $userData['user_type'],
            $userData['first_name'],
            $userData['last_name'],
            $userData['phone'] ?? '',
            $userData['address'] ?? '',
            USER_STATUS_ACTIVE
        ]);
        
        $userId = $db->lastInsertId();
        
        // Create profile based on user type
        if ($userData['user_type'] === USER_TYPE_DONOR) {
            $profileSql = "INSERT INTO donor_profiles (user_id, blood_type_id, weight, medical_conditions, eligibility_status) 
                          VALUES (?, ?, ?, ?, ?)";
            
            $db->execute($profileSql, [
                $userId,
                $userData['blood_type_id'],
                $userData['weight'] ?? 0,
                $userData['medical_conditions'] ?? '',
                'eligible'
            ]);
        } elseif ($userData['user_type'] === USER_TYPE_RECIPIENT) {
            $profileSql = "INSERT INTO recipient_profiles (user_id, medical_condition, emergency_contact) 
                          VALUES (?, ?, ?)";
            
            $db->execute($profileSql, [
                $userId,
                $userData['medical_condition'] ?? '',
                $userData['emergency_contact'] ?? ''
            ]);
        }
        
        $db->commit();
        
        // Log registration
        logEvent('INFO', 'New user registered', [
            'user_id' => $userId,
            'username' => $userData['username'],
            'user_type' => $userData['user_type']
        ]);
        
        return $userId;
        
    } catch (Exception $e) {
        $db->rollback();
        logEvent('ERROR', 'User registration failed', [
            'username' => $userData['username'],
            'error' => $e->getMessage()
        ]);
        throw $e;
    }
}

/**
 * Generate password reset token
 */
function generatePasswordResetToken($username) {
    $db = Database::getInstance();
    
    // Check if user exists
    $userSql = "SELECT id, first_name, last_name FROM users WHERE username = ? AND status = ?";
    $user = $db->fetch($userSql, [$username, USER_STATUS_ACTIVE]);
    
    if (!$user) {
        return false;
    }
    
    // Generate token
    $token = generateToken();
    $expiresAt = date(DATETIME_FORMAT, time() + PASSWORD_RESET_EXPIRE);
    
    // Delete existing tokens for this user
    $deleteSql = "DELETE FROM password_resets WHERE user_id = ?";
    $db->execute($deleteSql, [$user['id']]);
    
    // Insert new token
    $insertSql = "INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?)";
    $db->execute($insertSql, [$user['id'], $token, $expiresAt]);
    
    logEvent('INFO', 'Password reset token generated', [
        'user_id' => $user['id'],
        'username' => $username
    ]);
    
    return [
        'token' => $token,
        'user' => $user
    ];
}

/**
 * Verify password reset token
 */
function verifyPasswordResetToken($token) {
    $db = Database::getInstance();
    
    $sql = "SELECT pr.user_id, pr.expires_at, u.username 
            FROM password_resets pr 
            JOIN users u ON pr.user_id = u.id 
            WHERE pr.token = ? AND pr.used_at IS NULL AND pr.expires_at > NOW()";
    
    return $db->fetch($sql, [$token]);
}

/**
 * Reset password using token
 */
function resetPassword($token, $newPassword) {
    $db = Database::getInstance();
    
    try {
        $db->beginTransaction();
        
        // Verify token
        $tokenData = verifyPasswordResetToken($token);
        if (!$tokenData) {
            throw new Exception('Invalid or expired token');
        }
        
        // Update password
        $updateSql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
        $db->execute($updateSql, [$newPassword, $tokenData['user_id']]);
        
        // Mark token as used
        $markUsedSql = "UPDATE password_resets SET used_at = NOW() WHERE token = ?";
        $db->execute($markUsedSql, [$token]);
        
        $db->commit();
        
        logEvent('INFO', 'Password reset successful', [
            'user_id' => $tokenData['user_id'],
            'username' => $tokenData['username']
        ]);
        
        return true;
        
    } catch (Exception $e) {
        $db->rollback();
        logEvent('ERROR', 'Password reset failed', [
            'token' => $token,
            'error' => $e->getMessage()
        ]);
        throw $e;
    }
}

/**
 * Change user password
 */
function changePassword($userId, $currentPassword, $newPassword) {
    $db = Database::getInstance();
    
    // Get current password hash
    $sql = "SELECT password AS password_hash FROM users WHERE id = ?";
    $user = $db->fetch($sql, [$userId]);
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Verify current password
    if ($currentPassword !== $user['password_hash']) {
        throw new Exception('Current password is incorrect');
    }

    // Update password
    $updateSql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
    $db->execute($updateSql, [$newPassword, $userId]);
    
    logEvent('INFO', 'Password changed', ['user_id' => $userId]);
    
    return true;
}

/**
 * Check login attempts and implement rate limiting
 */
function checkLoginAttempts($identifier) {
    $attemptsFile = LOG_DIR . 'login_attempts.json';
    $attempts = [];
    
    if (file_exists($attemptsFile)) {
        $attempts = json_decode(file_get_contents($attemptsFile), true) ?: [];
    }
    
    $now = time();
    $key = md5($identifier . $_SERVER['REMOTE_ADDR']);
    
    // Clean old attempts
    if (isset($attempts[$key])) {
        $attempts[$key] = array_filter($attempts[$key], function($timestamp) use ($now) {
            return ($now - $timestamp) < LOGIN_LOCKOUT_TIME;
        });
    }
    
    // Check if locked out
    if (isset($attempts[$key]) && count($attempts[$key]) >= MAX_LOGIN_ATTEMPTS) {
        return false;
    }
    
    return true;
}

/**
 * Record login attempt
 */
function recordLoginAttempt($identifier, $success = false) {
    $attemptsFile = LOG_DIR . 'login_attempts.json';
    $attempts = [];
    
    if (file_exists($attemptsFile)) {
        $attempts = json_decode(file_get_contents($attemptsFile), true) ?: [];
    }
    
    $key = md5($identifier . $_SERVER['REMOTE_ADDR']);
    
    if ($success) {
        // Clear attempts on successful login
        unset($attempts[$key]);
    } else {
        // Record failed attempt
        if (!isset($attempts[$key])) {
            $attempts[$key] = [];
        }
        $attempts[$key][] = time();
    }
    
    file_put_contents($attemptsFile, json_encode($attempts), LOCK_EX);
}

/**
 * Update user last login
 */
function updateLastLogin($userId) {
    $db = Database::getInstance();
    
    $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
    $db->execute($sql, [$userId]);
}

/**
 * Get user by ID
 */
function getUserById($userId) {
    $db = Database::getInstance();
    
    $sql = "SELECT id, username, email, user_type, first_name, last_name, phone, address, status, created_at, last_login 
            FROM users WHERE id = ?";
    
    return $db->fetch($sql, [$userId]);
}

/**
 * Update user profile
 */
function updateUserProfile($userId, $profileData) {
    $db = Database::getInstance();
    
    try {
        $db->beginTransaction();
        
        // Update user table
        $userSql = "UPDATE users SET first_name = ?, last_name = ?, phone = ?, address = ?, updated_at = NOW() 
                    WHERE id = ?";
        
        $db->execute($userSql, [
            $profileData['first_name'],
            $profileData['last_name'],
            $profileData['phone'],
            $profileData['address'],
            $userId
        ]);
        
        // Update profile table based on user type
        $user = getUserById($userId);
        
        if ($user['user_type'] === USER_TYPE_DONOR && isset($profileData['weight'])) {
            $profileSql = "UPDATE donor_profiles SET weight = ?, medical_conditions = ? WHERE user_id = ?";
            $db->execute($profileSql, [
                $profileData['weight'],
                $profileData['medical_conditions'] ?? '',
                $userId
            ]);
        } elseif ($user['user_type'] === USER_TYPE_RECIPIENT) {
            $profileSql = "UPDATE recipient_profiles SET medical_condition = ?, emergency_contact = ? WHERE user_id = ?";
            $db->execute($profileSql, [
                $profileData['medical_condition'] ?? '',
                $profileData['emergency_contact'] ?? '',
                $userId
            ]);
        }
        
        $db->commit();
        
        logEvent('INFO', 'User profile updated', ['user_id' => $userId]);
        
        return true;
        
    } catch (Exception $e) {
        $db->rollback();
        logEvent('ERROR', 'Profile update failed', [
            'user_id' => $userId,
            'error' => $e->getMessage()
        ]);
        throw $e;
    }
}

/**
 * Verify if username exists for multi-step password reset
 */
function verifyUsernameExists($username) {
    $db = Database::getInstance();
    
    $sql = "SELECT id, username, first_name, last_name, status FROM users WHERE username = ? AND status = ?";
    $user = $db->fetch($sql, [$username, USER_STATUS_ACTIVE]);
    
    if ($user) {
        logEvent('INFO', 'Username verification for password reset', [
            'username' => $username,
            'user_id' => $user['id']
        ]);
    }
    
    return $user;
}

/**
 * Verify user identity for multi-step password reset
 */
function verifyUserIdentity($userId, $firstName, $lastName) {
    $db = Database::getInstance();
    
    $sql = "SELECT id, username, first_name, last_name, status FROM users 
            WHERE id = ? AND first_name = ? AND last_name = ? AND status = ?";
    $user = $db->fetch($sql, [$userId, $firstName, $lastName, USER_STATUS_ACTIVE]);
    
    if ($user) {
        logEvent('INFO', 'Identity verification successful for password reset', [
            'user_id' => $userId,
            'username' => $user['username']
        ]);
    } else {
        logEvent('WARNING', 'Identity verification failed for password reset', [
            'user_id' => $userId,
            'provided_first_name' => $firstName,
            'provided_last_name' => $lastName
        ]);
    }
    
    return $user;
}

/**
 * Change user password without current password verification (for reset)
 */
function changeUserPassword($userId, $newPassword) {
    $db = Database::getInstance();
    
    try {
        $db->beginTransaction();
        
        // Hash the new password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // Update password (using the password column as per migration)
        $updateSql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
        $db->execute($updateSql, [$hashedPassword, $userId]);
        
        $db->commit();
        
        logEvent('INFO', 'Password changed via multi-step reset', [
            'user_id' => $userId
        ]);
        
        return true;
        
    } catch (Exception $e) {
        $db->rollback();
        logEvent('ERROR', 'Password change failed', [
            'user_id' => $userId,
            'error' => $e->getMessage()
        ]);
        throw $e;
    }
}
?>
