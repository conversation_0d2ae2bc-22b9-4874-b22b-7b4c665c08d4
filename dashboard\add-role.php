<?php
/**
 * Add Role Page
 * Blood Donation Management System
 * 
 * Allows users to add additional roles to their account
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../classes/UnifiedUser.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();
$unifiedUser = new UnifiedUser($currentUser['id']);

// Get requested role
$requestedRole = $_GET['role'] ?? '';
if (!in_array($requestedRole, ['donor', 'recipient'])) {
    redirectWithMessage('index.php', 'Invalid role specified.', 'error');
}

// Check if user already has this role
if ($unifiedUser->hasRole($requestedRole)) {
    redirectWithMessage('index.php', 'You already have this role.', 'info');
}

$errors = [];
$success = '';

// Get blood types for donor role
$bloodTypes = [];
if ($requestedRole === 'donor') {
    $bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        try {
            // Add the role
            $unifiedUser->addRole($requestedRole);
            
            // Update role-specific profile data if provided
            if ($requestedRole === 'donor') {
                // Process medical conditions (checkbox array)
                $medicalConditions = [];
                if (!empty($_POST['medical_conditions']) && is_array($_POST['medical_conditions'])) {
                    $medicalConditions = $_POST['medical_conditions'];
                }
                
                // Check if 'None' was selected
                if (in_array('None', $medicalConditions)) {
                    $medicalConditionsText = 'None';
                } else {
                    $medicalConditionsText = implode(', ', $medicalConditions);
                }
                
                // Update donor profile with comprehensive medical data
                $db->execute("UPDATE users SET first_name = ?, middle_name = ?, last_name = ?, phone = ?, email = ?, address = ?, gender = ? WHERE id = ?", [
                    sanitizeInput($_POST['first_name'] ?? ''),
                    sanitizeInput($_POST['middle_name'] ?? ''),
                    sanitizeInput($_POST['last_name'] ?? ''),
                    sanitizeInput($_POST['phone'] ?? ''),
                    sanitizeInput($_POST['email'] ?? ''),
                    sanitizeInput($_POST['address'] ?? ''),
                    sanitizeInput($_POST['gender'] ?? ''),
                    $currentUser['id']
                ]);
                
                $db->execute("UPDATE donor_profiles SET blood_type_id = ?, weight = ?, birth_date = ?, last_donation_date = ?, medical_conditions = ?, 
                              medications = ?, allergies = ?, emergency_contact = ?, emergency_phone = ?, relationship = ? WHERE user_id = ?", [
                    (int)$_POST['blood_type_id'],
                    (float)($_POST['weight'] ?? 0),
                    $_POST['birth_date'] ?? null,
                    $_POST['last_donation'] ?? null,
                    $medicalConditionsText,
                    sanitizeInput($_POST['medications'] ?? ''),
                    sanitizeInput($_POST['allergies'] ?? ''),
                    sanitizeInput($_POST['emergency_contact'] ?? ''),
                    sanitizeInput($_POST['emergency_phone'] ?? ''),
                    sanitizeInput($_POST['relationship'] ?? ''),
                    $currentUser['id']
                ]);
                
                // Check required consents
                $requiredConsents = ['authorization_disclosure', 'consent_medical_history', 'consent_health_screening', 
                                   'consent_blood_testing', 'consent_data_sharing', 'terms_agreement'];
                
                foreach ($requiredConsents as $consent) {
                    if (empty($_POST[$consent])) {
                        throw new Exception('All consent checkboxes must be checked to proceed.');
                    }
                }
                
            } elseif ($requestedRole === 'recipient') {
                // Update recipient profile
                $db->execute("UPDATE recipient_profiles SET medical_condition = ?, emergency_contact = ?, emergency_phone = ?, doctor_name = ?, doctor_contact = ? WHERE user_id = ?", [
                    sanitizeInput($_POST['medical_condition'] ?? ''),
                    sanitizeInput($_POST['emergency_contact'] ?? ''),
                    sanitizeInput($_POST['emergency_phone'] ?? ''),
                    sanitizeInput($_POST['doctor_name'] ?? ''),
                    sanitizeInput($_POST['doctor_contact'] ?? ''),
                    $currentUser['id']
                ]);
                
                // Update user contact info
                $db->execute("UPDATE users SET phone = ?, email = ? WHERE id = ?", [
                    sanitizeInput($_POST['phone'] ?? ''),
                    sanitizeInput($_POST['email'] ?? ''),
                    $currentUser['id']
                ]);
            }
            
            redirectWithMessage('index.php', 'Role added successfully! You can now switch to ' . ucfirst($requestedRole) . ' mode.', 'success');
            
        } catch (Exception $e) {
            $errors[] = $e->getMessage();
        }
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add <?php echo ucfirst($requestedRole); ?> Role - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 2px 10px rgba(139,0,0,0.3);">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header blood-theme text-white">
                        <h4>
                            <i class="fas fa-<?php echo $requestedRole === 'donor' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                            Add <?php echo ucfirst($requestedRole); ?> Role
                        </h4>
                        <p class="mb-0">Complete your <?php echo $requestedRole; ?> profile to get started</p>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="" id="roleApplicationForm">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">

                            <?php if ($requestedRole === 'donor'): ?>
                                <!-- Comprehensive Medical Screening Form -->
                                <div class="alert alert-info">
                                    <h6 class="text-center"><i class="fas fa-heart"></i> BLOOD DONOR REGISTRATION FORM</h6>
                                    <p class="text-center mb-0">Cotabato Regional and Medical Center Authorization</p>
                                </div>

                                <div class="form-section mb-4">
                                    <h5 class="section-title"><i class="fas fa-user"></i> SECTION A: PERSONAL INFORMATION</h5>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="first_name" class="form-label">First Name *</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                                   value="<?php echo htmlspecialchars($currentUser['first_name'] ?? ''); ?>" required>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="middle_name" class="form-label">Middle Name</label>
                                            <input type="text" class="form-control" id="middle_name" name="middle_name" 
                                                   value="<?php echo htmlspecialchars($currentUser['middle_name'] ?? ''); ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="last_name" class="form-label">Last Name *</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                                   value="<?php echo htmlspecialchars($currentUser['last_name'] ?? ''); ?>" required>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="birth_date" class="form-label">Date of Birth *</label>
                                            <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="gender" class="form-label">Gender *</label>
                                            <select class="form-select" id="gender" name="gender" required>
                                                <option value="">Select Gender</option>
                                                <option value="Male">Male</option>
                                                <option value="Female">Female</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="blood_type_id" class="form-label">Blood Type *</label>
                                            <select class="form-select" id="blood_type_id" name="blood_type_id" required>
                                                <option value="">Select Blood Type</option>
                                                <?php foreach ($bloodTypes as $type): ?>
                                                    <option value="<?php echo $type['id']; ?>"
                                                        <?php echo (isset($_POST['blood_type_id']) && $_POST['blood_type_id'] == $type['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($type['type']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="weight" class="form-label">Weight (kg) *</label>
                                            <input type="number" class="form-control" id="weight" name="weight" min="50" step="0.1" placeholder="Enter weight in kilograms" required>
                                            <div class="form-text">Minimum weight requirement: 50kg</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="last_donation" class="form-label">Last Blood Donation Date</label>
                                            <input type="date" class="form-control" id="last_donation" name="last_donation">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">Phone Number *</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($currentUser['email'] ?? ''); ?>">
                                            <div class="form-text">We'll never share your email with anyone else.</div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="address" class="form-label">Complete Address *</label>
                                        <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($currentUser['address'] ?? ''); ?></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="emergency_contact" class="form-label">Emergency Contact Person *</label>
                                        <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" required>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="emergency_phone" class="form-label">Emergency Contact Number *</label>
                                            <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="relationship" class="form-label">Relationship to Contact *</label>
                                            <input type="text" class="form-control" id="relationship" name="relationship" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section mb-4">
                                    <h5 class="section-title"><i class="fas fa-heartbeat"></i> SECTION B: MEDICAL HISTORY SCREENING</h5>
                                    <p class="text-muted">Please check all that apply to you:</p>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition1" name="medical_conditions[]" value="Heart condition">
                                                <label class="form-check-label" for="condition1">Heart condition, disease or damage</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition2" name="medical_conditions[]" value="High blood pressure">
                                                <label class="form-check-label" for="condition2">High blood pressure</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition3" name="medical_conditions[]" value="Stroke">
                                                <label class="form-check-label" for="condition3">Stroke</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition4" name="medical_conditions[]" value="Cancer">
                                                <label class="form-check-label" for="condition4">Cancer</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition5" name="medical_conditions[]" value="Diabetes">
                                                <label class="form-check-label" for="condition5">Diabetes</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition6" name="medical_conditions[]" value="Epilepsy">
                                                <label class="form-check-label" for="condition6">Epilepsy</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition7" name="medical_conditions[]" value="Asthma">
                                                <label class="form-check-label" for="condition7">Asthma</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition8" name="medical_conditions[]" value="Hepatitis">
                                                <label class="form-check-label" for="condition8">Hepatitis B or C</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition9" name="medical_conditions[]" value="HIV">
                                                <label class="form-check-label" for="condition9">HIV/AIDS</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition10" name="medical_conditions[]" value="STD">
                                                <label class="form-check-label" for="condition10">Sexually transmitted disease</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition11" name="medical_conditions[]" value="Tuberculosis">
                                                <label class="form-check-label" for="condition11">Tuberculosis</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition12" name="medical_conditions[]" value="Malaria">
                                                <label class="form-check-label" for="condition12">Malaria</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="condition13" name="medical_conditions[]" value="Bleeding disorder">
                                                <label class="form-check-label" for="condition13">Bleeding disorder</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="none_conditions" name="medical_conditions[]" value="None">
                                                <label class="form-check-label" for="none_conditions">None of the above</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="medications" class="form-label">Current Medications</label>
                                        <input type="text" class="form-control" id="medications" name="medications" placeholder="List all medications you are currently taking">
                                    </div>

                                    <div class="mb-3">
                                        <label for="allergies" class="form-label">Allergies</label>
                                        <input type="text" class="form-control" id="allergies" name="allergies" placeholder="List any allergies you have">
                                    </div>
                                </div>

                                <div class="form-section mb-4">
                                    <h5 class="section-title"><i class="fas fa-file-medical"></i> SECTION C: HEALTH INFORMATION DISCLOSURE AUTHORIZATION</h5>
                                    <p>I authorize the Cotabato Regional and Medical Center to:</p>
                                    <ul>
                                        <li>Obtain and/or release necessary medical information for blood donation purposes</li>
                                        <li>Perform medical tests to determine my eligibility as a blood donor</li>
                                        <li>Contact my emergency contact person if needed</li>
                                        <li>Use my personal information in accordance with privacy regulations</li>
                                    </ul>
                                </div>

                                <div class="form-section mb-4">
                                    <h5 class="section-title"><i class="fas fa-shield-alt"></i> SECTION D: CONSENT AND AUTHORIZATION</h5>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="authorization_disclosure" name="authorization_disclosure" required>
                                        <label class="form-check-label" for="authorization_disclosure">
                                            <strong>Authorization for Disclosure of Personal Medical Information</strong><br>
                                            I authorize Cotabato Regional and Medical Center to disclose my personal medical information
                                            as required for blood donation purposes.
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="consent_medical_history" name="consent_medical_history" required>
                                        <label class="form-check-label" for="consent_medical_history">
                                            <strong>Medical History Consent</strong><br>
                                            I have truthfully answered all questions regarding my medical history and understand that
                                            providing false information may result in serious health consequences for recipients.
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="consent_health_screening" name="consent_health_screening" required>
                                        <label class="form-check-label" for="consent_health_screening">
                                            <strong>Health Screening Consent</strong><br>
                                            I consent to undergo health screening procedures including physical examination and blood tests
                                            to determine my eligibility as a blood donor.
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="consent_blood_testing" name="consent_blood_testing" required>
                                        <label class="form-check-label" for="consent_blood_testing">
                                            <strong>Blood Testing Consent</strong><br>
                                            I consent to have my blood tested for infectious diseases and other medical conditions
                                            as required by medical standards and regulations.
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="consent_data_sharing" name="consent_data_sharing" required>
                                        <label class="form-check-label" for="consent_data_sharing">
                                            <strong>Data Sharing Consent</strong><br>
                                            I consent to share my personal and medical information with authorized medical personnel
                                            for blood donation coordination and safety purposes.
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="terms_agreement" name="terms_agreement" required>
                                        <label class="form-check-label" for="terms_agreement">
                                            <strong>Terms Agreement</strong><br>
                                            I agree to all terms and conditions of the blood donation program and understand my
                                            responsibilities as a blood donor.
                                        </label>
                                    </div>
                                </div>

                                <div class="signature-section mb-4">
                                    <h5 class="section-title"><i class="fas fa-signature"></i> SECTION E: CERTIFICATION</h5>
                                    <p>By submitting this form, I certify that:</p>
                                    <ul>
                                        <li>All information provided is true and complete to the best of my knowledge</li>
                                        <li>I understand the blood donation process and its potential risks</li>
                                        <li>I am voluntarily participating in the blood donation program</li>
                                        <li>I have read and understood all consent and authorization statements above</li>
                                    </ul>
                                    
                                    <div class="row mt-4">
                                        <div class="col-md-8">
                                            <p><strong>Electronic Signature:</strong> By clicking "Add Donor Role" below, I electronically sign this document.</p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <p><strong>Date:</strong> <?php echo date('Y-m-d'); ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php elseif ($requestedRole === 'recipient'): ?>
                                <!-- Recipient-specific fields -->
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Recipient Information</h6>
                                    <p class="mb-0">Provide emergency contact and medical information to help us assist you better during blood requests.</p>
                                </div>

                                <div class="mb-3">
                                    <label for="medical_condition" class="form-label">Medical Condition</label>
                                    <textarea class="form-control" id="medical_condition" name="medical_condition" 
                                              rows="3" placeholder="Describe your medical condition or reason for needing blood..."></textarea>
                                </div>

                                <h6><i class="fas fa-phone"></i> Emergency Contact Information</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_contact" class="form-label">Emergency Contact Name</label>
                                            <input type="text" class="form-control" id="emergency_contact" name="emergency_contact">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_phone" class="form-label">Emergency Contact Phone</label>
                                            <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone">
                                        </div>
                                    </div>
                                </div>

                                <h6><i class="fas fa-user-md"></i> Doctor Information</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="doctor_name" class="form-label">Doctor Name</label>
                                            <input type="text" class="form-control" id="doctor_name" name="doctor_name">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="doctor_contact" class="form-label">Doctor Contact</label>
                                            <input type="tel" class="form-control" id="doctor_contact" name="doctor_contact">
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="index.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-danger" id="submitBtn">
                                    <span class="btn-text">
                                        <i class="fas fa-plus"></i> Add <?php echo ucfirst($requestedRole); ?> Role
                                    </span>
                                    <span class="btn-loading d-none">
                                        <i class="fas fa-spinner fa-spin"></i> Processing...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('roleApplicationForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const form = this;
            const submitBtn = document.getElementById('submitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');

            // Show loading state
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            submitBtn.disabled = true;

            // Clear previous errors
            const existingAlert = document.querySelector('.alert-danger');
            if (existingAlert) {
                existingAlert.remove();
            }

            // Prepare form data
            const formData = new FormData(form);

            // Submit via AJAX
            fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // Parse response to check for errors
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const errorAlert = doc.querySelector('.alert-danger');

                if (errorAlert) {
                    // Show errors
                    const cardBody = document.querySelector('.card-body');
                    cardBody.insertBefore(errorAlert, cardBody.firstChild);

                    // Reset button state
                    btnText.classList.remove('d-none');
                    btnLoading.classList.add('d-none');
                    submitBtn.disabled = false;
                } else {
                    // Success - redirect to dashboard with success message
                    window.location.href = 'index.php?success=role_added&role=<?php echo $requestedRole; ?>';
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Show generic error
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger';
                errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> An error occurred. Please try again.';

                const cardBody = document.querySelector('.card-body');
                cardBody.insertBefore(errorDiv, cardBody.firstChild);

                // Reset button state
                btnText.classList.remove('d-none');
                btnLoading.classList.add('d-none');
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>
