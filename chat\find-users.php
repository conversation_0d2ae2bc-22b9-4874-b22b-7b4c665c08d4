<?php
/**
 * Find Users to Chat With
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';
require_once '../classes/UnifiedUser.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

// Get search parameters
$search = sanitizeInput($_GET['search'] ?? '');
$roleFilter = sanitizeInput($_GET['role'] ?? '');
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query to find compatible users
$whereConditions = ["u.status = 'active'", "u.id != ?"];
$params = [$currentUser['id']];

// Add search filter
if (!empty($search)) {
    $whereConditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.username LIKE ?)";
    $searchTerm = '%' . $search . '%';
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

// Build role-based filtering
$roleConditions = [];

// Get current user's roles to determine who they can chat with
$currentUserRoles = [];
if ($currentUser['user_type'] === 'unified') {
    $currentUserRoles = getUserRoles($currentUser['id']);
} elseif ($currentUser['user_type'] === USER_TYPE_DONOR) {
    $currentUserRoles = ['donor'];
} elseif ($currentUser['user_type'] === USER_TYPE_RECIPIENT) {
    $currentUserRoles = ['recipient'];
} elseif ($currentUser['user_type'] === USER_TYPE_ADMIN) {
    $currentUserRoles = ['admin'];
}

// Determine compatible roles
$compatibleRoles = [];
if (in_array('donor', $currentUserRoles)) {
    $compatibleRoles[] = 'recipient';
}
if (in_array('recipient', $currentUserRoles)) {
    $compatibleRoles[] = 'donor';
}

// Admin can chat with anyone
if ($currentUser['user_type'] === USER_TYPE_ADMIN) {
    // No additional role restrictions for admin
} else {
    // Build role filtering for non-admin users
    if (!empty($compatibleRoles)) {
        $roleConditionParts = [];
        
        // Legacy users
        foreach ($compatibleRoles as $role) {
            if ($role === 'donor') {
                $roleConditionParts[] = "u.user_type = 'donor'";
            } elseif ($role === 'recipient') {
                $roleConditionParts[] = "u.user_type = 'recipient'";
            }
        }
        
        // Unified users with compatible roles
        if (!empty($compatibleRoles)) {
            $roleConditionParts[] = "(u.is_unified_user = TRUE AND EXISTS (
                SELECT 1 FROM user_roles ur 
                WHERE ur.user_id = u.id 
                AND ur.is_active = TRUE 
                AND ur.role_type IN ('" . implode("','", $compatibleRoles) . "')
            ))";
        }
        
        if (!empty($roleConditionParts)) {
            $whereConditions[] = "(" . implode(" OR ", $roleConditionParts) . ")";
        }
    }
}

// Apply role filter if specified
if (!empty($roleFilter) && in_array($roleFilter, ['donor', 'recipient', 'unified'])) {
    if ($roleFilter === 'unified') {
        $whereConditions[] = "u.is_unified_user = TRUE";
    } else {
        $whereConditions[] = "(u.user_type = ? OR (u.is_unified_user = TRUE AND EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = u.id 
            AND ur.is_active = TRUE 
            AND ur.role_type = ?
        )))";
        $params[] = $roleFilter;
        $params[] = $roleFilter;
    }
}

$whereClause = implode(' AND ', $whereConditions);

// Get total count
$countSql = "SELECT COUNT(DISTINCT u.id) as total 
             FROM users u 
             LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = TRUE
             WHERE $whereClause";
$totalResult = $db->fetch($countSql, $params);
$total = $totalResult['total'];
$totalPages = ceil($total / $limit);

// Get users
$sql = "SELECT DISTINCT u.id, u.username, u.first_name, u.last_name, u.user_type, u.profile_photo, u.is_unified_user,
               u.is_online, u.last_activity, u.last_seen,
               GROUP_CONCAT(DISTINCT ur.role_type) as roles,
               u.created_at
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = TRUE
        WHERE $whereClause
        GROUP BY u.id, u.username, u.first_name, u.last_name, u.user_type, u.profile_photo, u.is_unified_user, u.is_online, u.last_activity, u.last_seen, u.created_at
        ORDER BY u.is_online DESC, u.first_name, u.last_name
        LIMIT ? OFFSET ?";

$params[] = $limit;
$params[] = $offset;
$users = $db->fetchAll($sql, $params);

// Determine dashboard URL
$dashboardUrl = '../';
switch ($currentUser['user_type']) {
    case USER_TYPE_ADMIN:
        $dashboardUrl = '../admin/';
        break;
    case 'unified':
        $dashboardUrl = '../dashboard/';
        break;
    default:
        $dashboardUrl = '../dashboard/';
        break;
}

$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Users to Chat - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
        }

        .search-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(139,0,0,0.1);
            border: 2px solid #DC143C;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .search-container h4 {
            color: #8B0000;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .form-control:focus {
            border-color: #DC143C;
            box-shadow: 0 0 0 0.2rem rgba(220, 20, 60, 0.25);
        }

        .form-select:focus {
            border-color: #DC143C;
            box-shadow: 0 0 0 0.2rem rgba(220, 20, 60, 0.25);
        }

        .btn-danger {
            background: linear-gradient(45deg, #8B0000, #DC143C);
            border: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: linear-gradient(45deg, #A0001A, #FF1744);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(139,0,0,0.3);
        }

        .user-card {
            background: white;
            border-radius: 15px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .user-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(139,0,0,0.15);
            border-color: #DC143C;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #8B0000, #DC143C);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }

        .active-indicator {
            position: relative;
        }

        .active-indicator::before {
            content: '';
            position: absolute;
            top: 5px;
            right: 5px;
            width: 14px;
            height: 14px;
            border: 2px solid white;
            border-radius: 50%;
            animation: pulse 2s infinite;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .active-indicator.active::before {
            background: #28a745;
        }

        .active-indicator.inactive::before {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.35em 0.65em;
        }

        .card-header {
            background: linear-gradient(45deg, #8B0000, #DC143C);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }

        .pagination .page-link {
            color: #8B0000;
            border-color: #DC143C;
        }

        .pagination .page-item.active .page-link {
            background-color: #DC143C;
            border-color: #DC143C;
        }

        .pagination .page-link:hover {
            color: white;
            background-color: #8B0000;
            border-color: #8B0000;
        }

        .no-users-found {
            background: white;
            border-radius: 15px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(139,0,0,0.1);
        }

        .no-users-found i {
            color: #DC143C;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 2px 10px rgba(139,0,0,0.3);">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?php echo $dashboardUrl; ?>">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?>
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-comments"></i> Chat
                </a>
                <a class="nav-link active" href="find-users.php">
                    <i class="fas fa-search"></i> Find Users
                </a>
            </div>
            
            <div class="navbar-nav">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo $currentUser['first_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo $dashboardUrl; ?>"><i class="fas fa-dashboard"></i> Dashboard</a></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header blood-theme text-white">
                        <h2><i class="fas fa-search"></i> Find Users to Chat With</h2>
                        <p class="mb-0">Discover and connect with other members of the blood donation community</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="search-container">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search Users</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Search by name or username...">
                            </div>
                            <div class="col-md-4">
                                <label for="role" class="form-label">Filter by Role</label>
                                <select class="form-select" id="role" name="role">
                                    <option value="">All Compatible Users</option>
                                    <?php if (in_array('donor', $currentUserRoles) || $currentUser['user_type'] === USER_TYPE_ADMIN): ?>
                                        <option value="recipient" <?php echo $roleFilter === 'recipient' ? 'selected' : ''; ?>>Recipients</option>
                                    <?php endif; ?>
                                    <?php if (in_array('recipient', $currentUserRoles) || $currentUser['user_type'] === USER_TYPE_ADMIN): ?>
                                        <option value="donor" <?php echo $roleFilter === 'donor' ? 'selected' : ''; ?>>Donors</option>
                                    <?php endif; ?>
                                    <option value="unified" <?php echo $roleFilter === 'unified' ? 'selected' : ''; ?>>Unified Users</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>
                            <i class="fas fa-users"></i> 
                            Found <?php echo number_format($total); ?> compatible user<?php echo $total !== 1 ? 's' : ''; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No users found</h5>
                                <p class="text-muted">Try adjusting your search criteria or check back later for new members.</p>
                                <a href="find-users.php" class="btn btn-outline-primary">Clear Filters</a>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($users as $user): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="user-card h-100" data-user-id="<?php echo $user['id']; ?>">
                                            <div class="card-body text-center">
                                                <?php if ($user['profile_photo']): ?>
                                                    <div class="user-avatar active-indicator <?php echo isUserActive($user['id']) ? 'active' : 'inactive'; ?>">
                                                        <img src="../uploads/<?php echo htmlspecialchars($user['profile_photo']); ?>"
                                                             alt="Profile Photo" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                                                    </div>
                                                <?php else: ?>
                                                    <div class="user-avatar active-indicator <?php echo isUserActive($user['id']) ? 'active' : 'inactive'; ?>">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <h6 class="card-title">
                                                    <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                                    <?php if (isUserActive($user['id'])): ?>
                                                        <span class="badge bg-success ms-1 status-badge" title="Active">
                                                            Active
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary ms-1 status-badge" title="Inactive">
                                                            Inactive
                                                        </span>
                                                    <?php endif; ?>
                                                </h6>
                                                <p class="text-muted small">@<?php echo htmlspecialchars($user['username']); ?></p>

                                                <div class="mb-3">
                                                    <?php
                                                    // Display user roles
                                                    if ($user['is_unified_user'] && !empty($user['roles'])) {
                                                        $roles = explode(',', $user['roles']);
                                                        foreach ($roles as $role) {
                                                            $badgeClass = $role === 'donor' ? 'success' : 'info';
                                                            echo '<span class="badge bg-' . $badgeClass . ' me-1">' . ucfirst($role) . '</span>';
                                                        }
                                                    } else {
                                                        // Legacy user types
                                                        $badgeClass = $user['user_type'] === 'donor' ? 'success' : 'info';
                                                        echo '<span class="badge bg-' . $badgeClass . '">' . ucfirst($user['user_type']) . '</span>';
                                                    }
                                                    ?>
                                                </div>
                                                
                                                <?php if (canChatWith($currentUser['id'], $user['id'])): ?>
                                                    <a href="index.php?user_id=<?php echo $user['id']; ?>" class="btn btn-danger btn-sm">
                                                        <i class="fas fa-comment"></i> Start Chat
                                                    </a>
                                                <?php else: ?>
                                                    <button class="btn btn-secondary btn-sm" disabled>
                                                        <i class="fas fa-ban"></i> Cannot Chat
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                                <nav aria-label="User search pagination" class="mt-4">
                                    <ul class="pagination justify-content-center">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">Previous</a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <?php if ($page < $totalPages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">Next</a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/online-status.js"></script>
    <script>
        // Initialize online status monitoring for find users
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof OnlineStatusManager !== 'undefined') {
                const statusManager = new OnlineStatusManager();
                statusManager.start();

                // Get all user IDs from user cards
                const userIds = [];
                document.querySelectorAll('[data-user-id]').forEach(element => {
                    const userId = element.getAttribute('data-user-id');
                    if (userId && !userIds.includes(userId)) {
                        userIds.push(userId);
                    }
                });

                // Monitor these users
                if (userIds.length > 0) {
                    statusManager.monitorUsers(userIds);
                }
            }
        });
    </script>
</body>
</html>
