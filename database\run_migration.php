<?php
/**
 * Database Migration Runner
 * Blood Donation Management System
 * 
 * Safely runs the unified system migration
 */

require_once '../config/database.php';
require_once '../config/constants.php';
require_once '../includes/functions.php';

// Only allow running from command line or admin access
if (php_sapi_name() !== 'cli') {
    // For initial setup, allow running without admin check if no admin users exist
    session_start();

    // Check if any admin users exist
    try {
        $db = Database::getInstance();
        $adminExists = $db->fetch("SELECT id FROM users WHERE user_type = 'admin' LIMIT 1");

        if ($adminExists) {
            // Admin users exist, require authentication
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
                die('Access denied. Admin privileges required.');
            }
        } else {
            // No admin users exist, allow migration for initial setup
            echo "<!-- No admin users found. Allowing migration for initial setup. -->\n";
        }
    } catch (Exception $e) {
        // Database connection failed, allow migration to proceed
        echo "<!-- Database connection issue. Allowing migration to proceed. -->\n";
    }
}

echo "=== Blood Donation System - Unified Migration ===\n";
echo "Starting database migration to unified user system...\n\n";

try {
    $db = Database::getInstance();
    
    // Check if migration has already been run
    $migrationExists = false;
    try {
        $result = $db->fetch("SELECT * FROM schema_migrations WHERE migration_name = 'migrate_to_unified_system'");
        if ($result) {
            $migrationExists = true;
        }
    } catch (Exception $e) {
        // Table doesn't exist yet, which is fine
    }
    
    if ($migrationExists) {
        echo "Migration has already been executed. Skipping...\n";
        exit(0);
    }
    
    // Read migration file
    $migrationFile = __DIR__ . '/migrate_to_unified_system.sql';
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    if ($sql === false) {
        throw new Exception("Failed to read migration file");
    }
    
    echo "Executing migration...\n";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    // Don't use transactions for DDL statements as they can cause issues
    $executedCount = 0;
    $skippedCount = 0;

    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $db->getConnection()->exec($statement);
                $executedCount++;
                echo ".";
            } catch (PDOException $e) {
                // Some statements might fail if they already exist, which is okay
                if (strpos($e->getMessage(), 'already exists') !== false ||
                    strpos($e->getMessage(), 'Duplicate') !== false ||
                    strpos($e->getMessage(), 'Unknown column') !== false) {
                    $skippedCount++;
                    echo "s"; // skipped
                } else {
                    throw $e;
                }
            }
        }
    }
    
    echo "\n\nMigration completed successfully!\n";
    echo "Executed $executedCount SQL statements.\n";
    echo "Skipped $skippedCount statements (already exist).\n\n";
    
    // Verify migration
    echo "Verifying migration...\n";
    
    // Check if user_roles table exists
    $result = $db->fetch("SHOW TABLES LIKE 'user_roles'");
    if ($result) {
        echo "✓ user_roles table created\n";
    } else {
        echo "✗ user_roles table missing\n";
    }
    
    // Check if unified fields exist in users table
    $result = $db->fetch("SHOW COLUMNS FROM users LIKE 'is_unified_user'");
    if ($result) {
        echo "✓ Unified user fields added to users table\n";
    } else {
        echo "✗ Unified user fields missing\n";
    }
    
    // Check if system_announcements table exists
    $result = $db->fetch("SHOW TABLES LIKE 'system_announcements'");
    if ($result) {
        echo "✓ system_announcements table created\n";
    } else {
        echo "✗ system_announcements table missing\n";
    }
    
    echo "\nMigration verification completed.\n";
    echo "The system is now ready for unified user functionality!\n\n";
    
    // Log the migration
    logEvent('INFO', 'Database migration completed', [
        'migration' => 'migrate_to_unified_system',
        'statements_executed' => $executedCount
    ]);
    
} catch (Exception $e) {
    echo "\nMigration failed: " . $e->getMessage() . "\n";
    echo "Database may have been partially modified.\n";

    // Log the error
    try {
        logEvent('ERROR', 'Database migration failed', [
            'migration' => 'migrate_to_unified_system',
            'error' => $e->getMessage()
        ]);
    } catch (Exception $logError) {
        // Ignore logging errors during migration
    }

    exit(1);
}
?>
