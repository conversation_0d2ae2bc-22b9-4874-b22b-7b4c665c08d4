const faker = require('faker');

// Set seed for consistent test data
faker.seed(parseInt(process.env.FAKER_SEED) || 12345);

class TestDataGenerator {
  static generateUser(options = {}) {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    return {
      username: options.username || `${process.env.TEST_USER_PREFIX || 'test_user_'}${timestamp}_${random}`,
      firstName: options.firstName || faker.name.firstName(),
      lastName: options.lastName || faker.name.lastName(),
      phone: options.phone || faker.phone.phoneNumber('##########'),
      address: options.address || faker.address.streetAddress(),
      password: options.password || 'testpass123',
      ...options
    };
  }

  static generateDonorData(options = {}) {
    return {
      bloodTypeId: options.bloodTypeId || 1, // A+
      weight: options.weight || faker.datatype.number({ min: 50, max: 100 }),
      birthDate: options.birthDate || faker.date.between('1960-01-01', '2000-01-01').toISOString().split('T')[0],
      medicalConditions: options.medicalConditions || '',
      ...options
    };
  }

  static generateRecipientData(options = {}) {
    return {
      medicalCondition: options.medicalCondition || faker.lorem.sentence(),
      emergencyContact: options.emergencyContact || faker.name.findName(),
      emergencyPhone: options.emergencyPhone || faker.phone.phoneNumber('##########'),
      doctorName: options.doctorName || `Dr. ${faker.name.lastName()}`,
      doctorContact: options.doctorContact || faker.phone.phoneNumber('##########'),
      ...options
    };
  }

  static generateBloodRequest(options = {}) {
    return {
      bloodTypeId: options.bloodTypeId || 1,
      unitsNeeded: options.unitsNeeded || faker.datatype.number({ min: 1, max: 5 }),
      urgencyLevel: options.urgencyLevel || faker.random.arrayElement(['low', 'medium', 'high', 'critical']),
      medicalReason: options.medicalReason || faker.lorem.sentence(),
      hospitalName: options.hospitalName || faker.company.companyName() + ' Hospital',
      doctorName: options.doctorName || `Dr. ${faker.name.lastName()}`,
      contactPhone: options.contactPhone || faker.phone.phoneNumber('##########'),
      neededBy: options.neededBy || faker.date.future().toISOString().split('T')[0],
      ...options
    };
  }

  static getTestCredentials() {
    return {
      admin: {
        username: process.env.ADMIN_USERNAME || 'admin',
        password: process.env.ADMIN_PASSWORD || 'admin123'
      },
      testUser: {
        username: 'test_user_main',
        password: 'testpass123'
      }
    };
  }

  static getTestScenarios() {
    return {
      newUser: {
        description: 'New user with no roles',
        user: this.generateUser({ username: 'test_new_user' }),
        expectedRoles: []
      },
      donorOnly: {
        description: 'User with only donor role',
        user: this.generateUser({ username: 'test_donor_only' }),
        roles: ['donor'],
        donorData: this.generateDonorData()
      },
      recipientOnly: {
        description: 'User with only recipient role',
        user: this.generateUser({ username: 'test_recipient_only' }),
        roles: ['recipient'],
        recipientData: this.generateRecipientData()
      },
      multiRole: {
        description: 'User with both donor and recipient roles',
        user: this.generateUser({ username: 'test_multi_role' }),
        roles: ['donor', 'recipient'],
        donorData: this.generateDonorData(),
        recipientData: this.generateRecipientData()
      }
    };
  }

  static getValidationTestCases() {
    return {
      donor: {
        valid: {
          bloodTypeId: 1,
          weight: 70,
          birthDate: '1990-01-01',
          medicalConditions: ''
        },
        invalid: {
          bloodTypeId: '', // Missing blood type
          weight: 30, // Too low weight
          birthDate: '2010-01-01', // Too young
          medicalConditions: 'A'.repeat(1001) // Too long
        }
      },
      recipient: {
        valid: {
          medicalCondition: 'Test condition',
          emergencyContact: 'John Doe',
          emergencyPhone: '**********',
          doctorName: 'Dr. Smith',
          doctorContact: '**********'
        },
        invalid: {
          medicalCondition: '', // Missing condition
          emergencyContact: '', // Missing contact
          emergencyPhone: '123', // Invalid phone
          doctorName: '', // Missing doctor
          doctorContact: 'invalid' // Invalid contact
        }
      }
    };
  }

  static getSecurityTestCases() {
    return {
      sqlInjection: {
        username: "'; DROP TABLE users; --",
        firstName: "Robert'; DELETE FROM user_roles WHERE '1'='1",
        lastName: "'; UPDATE users SET user_type='admin' WHERE '1'='1"
      },
      xss: {
        firstName: "<script>alert('XSS')</script>",
        lastName: "<img src=x onerror=alert('XSS')>",
        medicalCondition: "javascript:alert('XSS')"
      },
      csrf: {
        invalidToken: 'invalid_csrf_token_12345',
        emptyToken: '',
        expiredToken: 'expired_token_from_yesterday'
      }
    };
  }

  static getPerformanceTestData() {
    return {
      bulkUsers: Array.from({ length: 10 }, (_, i) => 
        this.generateUser({ username: `test_bulk_user_${i}` })
      ),
      stressTestData: {
        concurrentUsers: 5,
        actionsPerUser: 10,
        delayBetweenActions: 100
      }
    };
  }
}

module.exports = TestDataGenerator;
