const { expect } = require('@playwright/test');
const path = require('path');
const fs = require('fs');

class CommonUtils {
  static async takeScreenshot(page, testInfo, name = 'screenshot') {
    if (process.env.SCREENSHOT_ON_FAILURE !== 'false') {
      const screenshotPath = path.join(testInfo.outputDir, `${name}-${Date.now()}.png`);
      await page.screenshot({ path: screenshotPath, fullPage: true });
      testInfo.attachments.push({
        name: name,
        path: screenshotPath,
        contentType: 'image/png'
      });
      return screenshotPath;
    }
  }

  static async waitForPageLoad(page, timeout = 30000) {
    await page.waitForLoadState('networkidle', { timeout });
    await page.waitForLoadState('domcontentloaded', { timeout });
  }

  static async waitForElement(page, selector, options = {}) {
    const defaultOptions = {
      state: 'visible',
      timeout: 10000
    };
    return await page.waitForSelector(selector, { ...defaultOptions, ...options });
  }

  static async fillFormField(page, selector, value, options = {}) {
    await this.waitForElement(page, selector);
    await page.fill(selector, value, options);
  }

  static async clickElement(page, selector, options = {}) {
    await this.waitForElement(page, selector);
    await page.click(selector, options);
  }

  static async selectOption(page, selector, value, options = {}) {
    await this.waitForElement(page, selector);
    await page.selectOption(selector, value, options);
  }

  static async verifyElementText(page, selector, expectedText, options = {}) {
    await this.waitForElement(page, selector);
    const element = page.locator(selector);
    if (options.exact) {
      await expect(element).toHaveText(expectedText);
    } else {
      await expect(element).toContainText(expectedText);
    }
  }

  static async verifyElementVisible(page, selector, timeout = 10000) {
    await expect(page.locator(selector)).toBeVisible({ timeout });
  }

  static async verifyElementHidden(page, selector, timeout = 10000) {
    await expect(page.locator(selector)).toBeHidden({ timeout });
  }

  static async verifyUrl(page, expectedUrl, options = {}) {
    if (options.exact) {
      await expect(page).toHaveURL(expectedUrl);
    } else {
      await expect(page).toHaveURL(new RegExp(expectedUrl));
    }
  }

  static async verifyPageTitle(page, expectedTitle, options = {}) {
    if (options.exact) {
      await expect(page).toHaveTitle(expectedTitle);
    } else {
      await expect(page).toHaveTitle(new RegExp(expectedTitle));
    }
  }

  static async handleAlert(page, action = 'accept', promptText = '') {
    page.on('dialog', async dialog => {
      if (action === 'accept') {
        await dialog.accept(promptText);
      } else {
        await dialog.dismiss();
      }
    });
  }

  static async scrollToElement(page, selector) {
    await page.locator(selector).scrollIntoViewIfNeeded();
  }

  static async getElementCount(page, selector) {
    return await page.locator(selector).count();
  }

  static async getElementText(page, selector) {
    await this.waitForElement(page, selector);
    return await page.locator(selector).textContent();
  }

  static async getElementAttribute(page, selector, attribute) {
    await this.waitForElement(page, selector);
    return await page.locator(selector).getAttribute(attribute);
  }

  static async isElementChecked(page, selector) {
    await this.waitForElement(page, selector);
    return await page.locator(selector).isChecked();
  }

  static async isElementEnabled(page, selector) {
    await this.waitForElement(page, selector);
    return await page.locator(selector).isEnabled();
  }

  static async waitForResponse(page, urlPattern, action) {
    const responsePromise = page.waitForResponse(urlPattern);
    await action();
    return await responsePromise;
  }

  static async waitForRequest(page, urlPattern, action) {
    const requestPromise = page.waitForRequest(urlPattern);
    await action();
    return await requestPromise;
  }

  static generateRandomString(length = 10) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  static generateRandomEmail() {
    return `test_${this.generateRandomString(8)}@example.com`;
  }

  static generateRandomPhone() {
    return Math.floor(1000000000 + Math.random() * 9000000000).toString();
  }

  static async logTestStep(testInfo, step, details = '') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${step}${details ? ': ' + details : ''}`;
    console.log(logMessage);
    
    // Attach to test info if available
    if (testInfo) {
      testInfo.annotations.push({
        type: 'step',
        description: logMessage
      });
    }
  }

  static async createTestReport(testResults, outputPath) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: testResults.length,
        passed: testResults.filter(r => r.status === 'passed').length,
        failed: testResults.filter(r => r.status === 'failed').length,
        skipped: testResults.filter(r => r.status === 'skipped').length
      },
      results: testResults
    };

    await fs.promises.writeFile(outputPath, JSON.stringify(report, null, 2));
    return report;
  }

  static async verifyFormValidation(page, formSelector, fieldSelector, invalidValue, expectedErrorMessage) {
    await this.fillFormField(page, fieldSelector, invalidValue);
    await page.click(`${formSelector} button[type="submit"]`);
    
    // Wait for validation message
    await page.waitForTimeout(1000);
    
    // Check for validation message
    const validationMessage = await page.locator(`${fieldSelector}:invalid`).count();
    expect(validationMessage).toBeGreaterThan(0);
  }

  static async verifyAccessControl(page, restrictedUrl, expectedRedirectUrl) {
    await page.goto(restrictedUrl);
    await this.waitForPageLoad(page);
    await this.verifyUrl(page, expectedRedirectUrl);
  }

  static async measurePageLoadTime(page, url) {
    const startTime = Date.now();
    await page.goto(url);
    await this.waitForPageLoad(page);
    const endTime = Date.now();
    return endTime - startTime;
  }

  static async verifyResponsiveDesign(page, breakpoints = [1920, 1024, 768, 375]) {
    const results = {};
    
    for (const width of breakpoints) {
      await page.setViewportSize({ width, height: 800 });
      await page.waitForTimeout(500); // Allow layout to settle
      
      results[width] = {
        viewport: await page.viewportSize(),
        screenshot: await page.screenshot({ fullPage: true })
      };
    }
    
    return results;
  }
}

module.exports = CommonUtils;
