<?php
/**
 * Inspect database schema and migrations table
 * Blood Donation Management System
 */

require_once '../config/database.php';

try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    echo "=== Database Tables ===\n";
    
    // Show all tables
    $stmt = $connection->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo $table . "\n";
    }
    
    echo "\n=== Schema Migrations Table ===\n";
    
    // Check if schema_migrations table exists
    $stmt = $connection->query("SHOW TABLES LIKE 'schema_migrations'");
    $result = $stmt->fetch();
    
    if ($result) {
        // Describe the schema_migrations table
        $stmt = $connection->query("DESCRIBE schema_migrations");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            echo "Field: " . $column['Field'] . "\n";
            echo "Type: " . $column['Type'] . "\n";
            echo "Null: " . $column['Null'] . "\n";
            echo "Key: " . $column['Key'] . "\n";
            echo "Default: " . $column['Default'] . "\n";
            echo "Extra: " . $column['Extra'] . "\n";
            echo "---\n";
        }
        
        // Show existing migrations
        echo "\n=== Existing Migrations ===\n";
        $stmt = $connection->query("SELECT * FROM schema_migrations");
        $migrations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($migrations as $migration) {
            echo "Migration: " . $migration['migration_name'] . "\n";
            echo "Success: " . ($migration['success'] ? 'Yes' : 'No') . "\n";
            echo "Executed At: " . $migration['executed_at'] . "\n";
            echo "Error Message: " . $migration['error_message'] . "\n";
            echo "---\n";
        }
    } else {
        echo "schema_migrations table does not exist\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
