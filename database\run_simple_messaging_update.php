<?php
/**
 * Run Simple Messaging System Database Update
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();

// Only allow running from command line or admin access
if (php_sapi_name() !== 'cli') {
    // Check if user is admin when running from web
    if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
        die('Access denied. Admin privileges required.');
    }
}

echo "=== Blood Donation System - Simple Messaging Update ===\n";
echo "Updating database schema for restricted messaging system...\n\n";

try {
    $db = Database::getInstance();
    
    // Read the SQL file
    $sqlFile = __DIR__ . '/simple_messaging_update.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    if ($sql === false) {
        throw new Exception("Failed to read SQL file");
    }
    
    echo "Executing messaging schema update...\n";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', preg_split('/;(?=(?:[^\']*\'[^\']*\')*[^\']*$)/', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt) && !preg_match('/^\s*USE\s+/', $stmt);
        }
    );
    
    $executedCount = 0;
    $skippedCount = 0;
    
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $db->getConnection()->exec($statement);
                $executedCount++;
                echo ".";
            } catch (PDOException $e) {
                // Some statements might fail if they already exist, which is okay
                if (strpos($e->getMessage(), 'already exists') !== false || 
                    strpos($e->getMessage(), 'Duplicate') !== false ||
                    strpos($e->getMessage(), 'Unknown column') !== false ||
                    strpos($e->getMessage(), 'Duplicate key') !== false) {
                    $skippedCount++;
                    echo "s"; // skipped
                } else {
                    echo "\nError executing statement: " . $e->getMessage() . "\n";
                    echo "Statement: " . substr($statement, 0, 100) . "...\n";
                    // Continue with other statements
                    $skippedCount++;
                    echo "s";
                }
            }
        }
    }
    
    echo "\n\nSimple messaging schema update completed!\n";
    echo "Executed $executedCount SQL statements.\n";
    echo "Skipped $skippedCount statements (already exist or had issues).\n\n";
    
    echo "The restricted messaging system is now ready!\n\n";
    
    // Log the update
    try {
        logEvent('INFO', 'Simple messaging schema updated', [
            'executed_statements' => $executedCount,
            'skipped_statements' => $skippedCount
        ]);
    } catch (Exception $logError) {
        // Ignore logging errors
    }
    
} catch (Exception $e) {
    echo "\nSimple messaging schema update failed: " . $e->getMessage() . "\n";
    
    // Log the error
    try {
        logEvent('ERROR', 'Simple messaging schema update failed', [
            'error' => $e->getMessage()
        ]);
    } catch (Exception $logError) {
        // Ignore logging errors during update
    }
    
    exit(1);
}

if (php_sapi_name() !== 'cli') {
    echo "<br><br><a href='../admin/'>Return to Admin Panel</a>";
}
?>
