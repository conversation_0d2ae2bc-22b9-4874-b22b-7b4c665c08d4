<?php
/**
 * Admin Donations Management
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();

// Handle add donation
if (isset($_POST['action']) && $_POST['action'] === 'add_donation') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('Invalid security token. Please try again.', 'error');
    } else {
        try {
            $donorId = (int)($_POST['donor_id'] ?? 0);
            $donationDate = sanitizeInput($_POST['donation_date'] ?? '');
            $location = sanitizeInput($_POST['location'] ?? '');
            $status = sanitizeInput($_POST['status'] ?? 'scheduled');
            $unitsDonated = (int)($_POST['units_donated'] ?? 1);
            $medicalClearance = (int)($_POST['medical_clearance'] ?? 0);
            $notes = sanitizeInput($_POST['notes'] ?? '');

            if ($donorId <= 0 || empty($donationDate) || empty($location)) {
                throw new Exception('Please fill in all required fields.');
            }

            // Get donor's blood type
            $donorInfo = $db->fetch("SELECT dp.blood_type_id FROM donor_profiles dp WHERE dp.user_id = ?", [$donorId]);
            if (!$donorInfo) {
                throw new Exception('Invalid donor selected.');
            }

            // Insert donation
            $sql = "INSERT INTO donations (donor_id, blood_type_id, units_donated, donation_date, location, status, medical_clearance, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

            $db->execute($sql, [
                $donorId,
                $donorInfo['blood_type_id'],
                $unitsDonated,
                $donationDate,
                $location,
                $status,
                $medicalClearance,
                $notes
            ]);

            // If completed, update donor profile
            if ($status === 'completed') {
                $db->execute(
                    "UPDATE donor_profiles SET last_donation_date = ?, total_donations = total_donations + 1 WHERE user_id = ?",
                    [$donationDate, $donorId]
                );
            }

            setFlashMessage('Donation added successfully!', 'success');

        } catch (Exception $e) {
            setFlashMessage('Error adding donation: ' . $e->getMessage(), 'error');
        }
    }

    // Redirect to prevent form resubmission
    header('Location: donations.php');
    exit;
}

// Handle donation status updates
if (isset($_POST['update_status']) && isset($_POST['donation_id'])) {
    $donationId = (int)$_POST['donation_id'];
    $newStatus = sanitizeInput($_POST['status']);
    $units = isset($_POST['units']) ? (int)$_POST['units'] : 1;
    
    if (in_array($newStatus, array_keys(DONATION_STATUSES))) {
        try {
            $db->beginTransaction();
            
            // Update donation status
            $db->execute(
                "UPDATE donations SET status = ?, units_donated = ?, updated_at = NOW() WHERE id = ?",
                [$newStatus, $units, $donationId]
            );
            
            // If completed, update donor profile
            if ($newStatus === DONATION_STATUS_COMPLETED) {
                $donation = $db->fetch("SELECT donor_id, donation_date FROM donations WHERE id = ?", [$donationId]);
                if ($donation) {
                    // Update last donation date
                    $db->execute(
                        "UPDATE donor_profiles SET last_donation_date = ?, total_donations = total_donations + 1 WHERE user_id = ?",
                        [$donation['donation_date'], $donation['donor_id']]
                    );
                }
            }
            
            $db->commit();
            redirectWithMessage('donations.php', 'Donation status updated successfully', 'success');
        } catch (Exception $e) {
            $db->rollback();
            redirectWithMessage('donations.php', 'Failed to update donation status: ' . $e->getMessage(), 'danger');
        }
    }
}

// Handle donation archiving (instead of deletion)
if (isset($_GET['archive']) && isset($_GET['id'])) {
    $donationId = (int)$_GET['id'];
    $adminId = getCurrentUser()['id'];

    try {
        $db->execute("UPDATE donations SET is_archived = TRUE, archived_at = NOW(), archived_by = ? WHERE id = ?",
                    [$adminId, $donationId]);
        redirectWithMessage('donations.php', 'Donation record archived successfully', 'success');
    } catch (Exception $e) {
        redirectWithMessage('donations.php', 'Failed to archive donation: ' . $e->getMessage(), 'danger');
    }
}

// Handle donation restoration
if (isset($_GET['restore']) && isset($_GET['id'])) {
    $donationId = (int)$_GET['id'];
    $adminId = getCurrentUser()['id'];

    try {
        $db->execute("UPDATE donations SET is_archived = FALSE, archived_at = NULL, archived_by = NULL WHERE id = ?",
                    [$donationId]);
        redirectWithMessage('donations.php', 'Donation record restored successfully', 'success');
    } catch (Exception $e) {
        redirectWithMessage('donations.php', 'Failed to restore donation: ' . $e->getMessage(), 'danger');
    }
}

// Get filter parameters
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$bloodType = isset($_GET['blood_type']) ? (int)$_GET['blood_type'] : 0;
$startDate = isset($_GET['start_date']) ? sanitizeInput($_GET['start_date']) : '';
$endDate = isset($_GET['end_date']) ? sanitizeInput($_GET['end_date']) : '';

// Build query conditions
$conditions = [];
$params = [];

if ($status) {
    $conditions[] = "d.status = ?";
    $params[] = $status;
}

if ($bloodType > 0) {
    $conditions[] = "d.blood_type_id = ?";
    $params[] = $bloodType;
}

if ($startDate) {
    $conditions[] = "d.donation_date >= ?";
    $params[] = $startDate;
}

if ($endDate) {
    $conditions[] = "d.donation_date <= ?";
    $params[] = $endDate;
}

$whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$recordsPerPage = RECORDS_PER_PAGE;
$offset = ($page - 1) * $recordsPerPage;

// Get total records
$countSql = "SELECT COUNT(*) FROM donations d $whereClause";
$totalRecords = (int)$db->fetch($countSql, $params)['COUNT(*)'];

// Get paginated records
$sql = "SELECT d.*, u.first_name, u.last_name, u.username, bt.type as blood_type
        FROM donations d
        JOIN users u ON d.donor_id = u.id
        JOIN blood_types bt ON d.blood_type_id = bt.id
        $whereClause
        ORDER BY d.donation_date DESC
        LIMIT $offset, $recordsPerPage";

$donations = $db->fetchAll($sql, $params);

// Get blood types for filter
$bloodTypes = $db->fetchAll("SELECT id, type FROM blood_types ORDER BY type");

// Generate pagination
$pagination = paginate($totalRecords, $page, $recordsPerPage);

// Build pagination URL
$paginationUrl = 'donations.php?';
if ($status) $paginationUrl .= "status=$status&";
if ($bloodType > 0) $paginationUrl .= "blood_type=$bloodType&";
if ($startDate) $paginationUrl .= "start_date=$startDate&";
if ($endDate) $paginationUrl .= "end_date=$endDate&";

// Get flash message
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Donations - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">Blood Donation</div>
                        <div class="brand-subtitle">Administrator Panel</div>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donation-centers.php">
                            <i class="fas fa-hospital nav-icon"></i>
                            <span class="nav-text">Donation Centers</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory.php">
                            <i class="fas fa-tint nav-icon"></i>
                            <span class="nav-text">Inventory</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>

                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="fas fa-heart"></i> Manage Blood Donations</h2>
            </div>
            <div class="col-md-4 text-end">
                <a href="#" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addDonationModal">
                    <i class="fas fa-plus"></i> Add Donation
                </a>
                <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                    <i class="fas fa-file-export"></i> Export
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> Filter Donations</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="donations.php" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <?php foreach (DONATION_STATUSES as $key => $value): ?>
                                <option value="<?php echo $key; ?>" <?php echo $status === $key ? 'selected' : ''; ?>>
                                    <?php echo $value; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="blood_type" class="form-label">Blood Type</label>
                        <select class="form-select" id="blood_type" name="blood_type">
                            <option value="0">All Types</option>
                            <?php foreach ($bloodTypes as $type): ?>
                                <option value="<?php echo $type['id']; ?>" <?php echo $bloodType == $type['id'] ? 'selected' : ''; ?>>
                                    <?php echo $type['type']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                    </div>
                    
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                    </div>
                    
                    <div class="col-12 text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                        <a href="donations.php" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Donations Table -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> Donation Records</h5>
            </div>
            <div class="card-body">
                <?php if (empty($donations)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No donation records found.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Donor</th>
                                    <th>Blood Type</th>
                                    <th>Units</th>
                                    <th>Date</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($donations as $donation): ?>
                                    <tr>
                                        <td><?php echo $donation['id']; ?></td>
                                        <td>
                                            <a href="users.php?id=<?php echo $donation['donor_id']; ?>">
                                                <?php echo htmlspecialchars($donation['first_name'] . ' ' . $donation['last_name']); ?>
                                            </a>
                                            <small class="text-muted d-block"><?php echo htmlspecialchars($donation['username']); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger"><?php echo htmlspecialchars($donation['blood_type']); ?></span>
                                        </td>
                                        <td><?php echo $donation['units_donated']; ?> units</td>
                                        <td><?php echo formatDate($donation['donation_date']); ?></td>
                                        <td><?php echo htmlspecialchars($donation['location']); ?></td>
                                        <td>
                                            <?php 
                                                $statusClass = '';
                                                switch ($donation['status']) {
                                                    case DONATION_STATUS_SCHEDULED:
                                                        $statusClass = 'bg-warning';
                                                        break;
                                                    case DONATION_STATUS_COMPLETED:
                                                        $statusClass = 'bg-success';
                                                        break;
                                                    case DONATION_STATUS_CANCELLED:
                                                        $statusClass = 'bg-danger';
                                                        break;
                                                }
                                            ?>
                                            <span class="badge <?php echo $statusClass; ?>">
                                                <?php echo DONATION_STATUSES[$donation['status']]; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editModal<?php echo $donation['id']; ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <a href="#" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $donation['id']; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                            
                                            <!-- Edit Modal -->
                                            <div class="modal fade" id="editModal<?php echo $donation['id']; ?>" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Update Donation Status</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <form method="POST" action="donations.php">
                                                            <div class="modal-body">
                                                                <input type="hidden" name="donation_id" value="<?php echo $donation['id']; ?>">
                                                                <input type="hidden" name="update_status" value="1">
                                                                
                                                                <div class="mb-3">
                                                                    <label for="status<?php echo $donation['id']; ?>" class="form-label">Status</label>
                                                                    <select class="form-select" id="status<?php echo $donation['id']; ?>" name="status" required>
                                                                        <?php foreach (DONATION_STATUSES as $key => $value): ?>
                                                                            <option value="<?php echo $key; ?>" <?php echo $donation['status'] === $key ? 'selected' : ''; ?>>
                                                                                <?php echo $value; ?>
                                                                            </option>
                                                                        <?php endforeach; ?>
                                                                    </select>
                                                                </div>
                                                                
                                                                <div class="mb-3">
                                                                    <label for="units<?php echo $donation['id']; ?>" class="form-label">Units Donated</label>
                                                                    <input type="number" class="form-control" id="units<?php echo $donation['id']; ?>" name="units" value="<?php echo $donation['units_donated']; ?>" min="1" max="5" required>
                                                                </div>
                                                                
                                                                <div class="mb-3">
                                                                    <label for="notes<?php echo $donation['id']; ?>" class="form-label">Notes</label>
                                                                    <textarea class="form-control" id="notes<?php echo $donation['id']; ?>" name="notes" rows="3"><?php echo htmlspecialchars($donation['notes'] ?? ''); ?></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                <button type="submit" class="btn btn-primary">Update</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal<?php echo $donation['id']; ?>" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Confirm Deletion</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>Are you sure you want to delete this donation record?</p>
                                                            <p><strong>Donor:</strong> <?php echo htmlspecialchars($donation['first_name'] . ' ' . $donation['last_name']); ?></p>
                                                            <p><strong>Date:</strong> <?php echo formatDate($donation['donation_date']); ?></p>
                                                            <p class="text-danger"><strong>This action cannot be undone.</strong></p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <a href="donations.php?delete=1&id=<?php echo $donation['id']; ?>" class="btn btn-danger">Delete</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination['totalPages'] > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <?php if ($pagination['currentPage'] > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php echo $paginationUrl; ?>page=<?php echo $pagination['currentPage'] - 1; ?>">
                                            <i class="fas fa-angle-left"></i> Previous
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php foreach ($pagination['pages'] as $p): ?>
                                    <li class="page-item <?php echo $p === $pagination['currentPage'] ? 'active' : ''; ?>">
                                        <a class="page-link" href="<?php echo $paginationUrl; ?>page=<?php echo $p; ?>">
                                            <?php echo $p; ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                                
                                <?php if ($pagination['currentPage'] < $pagination['totalPages']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php echo $paginationUrl; ?>page=<?php echo $pagination['currentPage'] + 1; ?>">
                                            Next <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Donation Modal -->
    <div class="modal fade" id="addDonationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Donation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="donations.php">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="add_donation">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Select Donor *</label>
                                    <select class="form-select" name="donor_id" required>
                                        <option value="">Choose a donor...</option>
                                        <?php
                                        $donors = $db->fetchAll("SELECT u.id, u.first_name, u.last_name, u.username, bt.type as blood_type
                                                                FROM users u
                                                                JOIN donor_profiles dp ON u.id = dp.user_id
                                                                JOIN blood_types bt ON dp.blood_type_id = bt.id
                                                                WHERE u.user_type IN ('donor', 'unified') AND u.status = 'active'
                                                                ORDER BY u.first_name, u.last_name");
                                        foreach ($donors as $donor): ?>
                                            <option value="<?php echo $donor['id']; ?>">
                                                <?php echo htmlspecialchars($donor['first_name'] . ' ' . $donor['last_name']); ?>
                                                (<?php echo htmlspecialchars($donor['username']); ?>) - <?php echo htmlspecialchars($donor['blood_type']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Donation Date *</label>
                                    <input type="date" class="form-control" name="donation_date" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Donation Center *</label>
                                    <select class="form-select" name="location" required>
                                        <option value="">Select donation center...</option>
                                        <?php
                                        $centers = $db->fetchAll("SELECT * FROM donation_centers WHERE is_active = 1 ORDER BY city, name");
                                        foreach ($centers as $center): ?>
                                            <option value="<?php echo htmlspecialchars($center['name'] . ' - ' . $center['city']); ?>">
                                                <?php echo htmlspecialchars($center['name'] . ' - ' . $center['city']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status *</label>
                                    <select class="form-select" name="status" required>
                                        <option value="scheduled">Scheduled</option>
                                        <option value="completed">Completed</option>
                                        <option value="cancelled">Cancelled</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Units Donated</label>
                                    <input type="number" class="form-control" name="units_donated" min="1" max="5" value="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Medical Clearance</label>
                                    <select class="form-select" name="medical_clearance">
                                        <option value="0">Pending</option>
                                        <option value="1">Cleared</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="Any additional notes about the donation..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Add Donation</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal fade" id="exportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Export Donation Records</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">This feature is currently under development.</p>
                    <p>Export functionality will be available in the next update.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 