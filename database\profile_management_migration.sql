-- Profile Management System Migration
-- Blood Donation Management System
-- Ensures database schema supports comprehensive profile management

USE blood_donation_system;

-- Ensure profile_photo column exists in users table (should already exist from schema.sql)
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS profile_photo VARCHAR(255) NULL;

-- Add profile update tracking columns
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS profile_updated_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP NULL;

-- Create profile_pictures directory tracking table
CREATE TABLE IF NOT EXISTS profile_pictures (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VA<PERSON>HAR(255) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_active (is_active),
    INDEX idx_upload_date (upload_date)
);

-- Create password change history table for security tracking
CREATE TABLE IF NOT EXISTS password_change_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_changed_at (changed_at)
);

-- Create profile update audit log
CREATE TABLE IF NOT EXISTS profile_update_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    old_value TEXT NULL,
    new_value TEXT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_field_name (field_name),
    INDEX idx_updated_at (updated_at)
);

-- Add indexes for better profile management performance
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_profile_updated (profile_updated_at),
ADD INDEX IF NOT EXISTS idx_password_changed (password_changed_at);

-- Ensure donor_profiles has all necessary fields for profile management
ALTER TABLE donor_profiles 
ADD COLUMN IF NOT EXISTS preferred_donation_location VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS notification_preferences JSON NULL,
ADD COLUMN IF NOT EXISTS privacy_settings JSON NULL;

-- Ensure recipient_profiles has all necessary fields for profile management
ALTER TABLE recipient_profiles 
ADD COLUMN IF NOT EXISTS preferred_hospital VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS notification_preferences JSON NULL,
ADD COLUMN IF NOT EXISTS privacy_settings JSON NULL;

-- Create user preferences table for profile customization
CREATE TABLE IF NOT EXISTS user_profile_preferences (
    user_id INT PRIMARY KEY,
    theme VARCHAR(20) DEFAULT 'light',
    language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'UTC',
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT FALSE,
    profile_visibility ENUM('public', 'private', 'contacts_only') DEFAULT 'private',
    show_donation_history BOOLEAN DEFAULT FALSE,
    show_contact_info BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default preferences for existing users
INSERT IGNORE INTO user_profile_preferences (user_id)
SELECT id FROM users WHERE id NOT IN (SELECT user_id FROM user_profile_preferences);

-- Record this migration
INSERT INTO schema_migrations (migration_name, success) 
VALUES ('profile_management_migration', TRUE)
ON DUPLICATE KEY UPDATE 
    executed_at = CURRENT_TIMESTAMP,
    success = TRUE,
    error_message = NULL;

-- Create uploads directory structure (this would be handled by PHP)
-- The following directories should be created with proper permissions:
-- uploads/
-- uploads/profiles/
-- uploads/profiles/thumbnails/

COMMIT;
