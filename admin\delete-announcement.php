<?php
/**
 * Delete System Announcement
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $announcementId = (int)($input['id'] ?? 0);
        
        if ($announcementId <= 0) {
            throw new Exception('Invalid announcement ID.');
        }
        
        $db = Database::getInstance();
        
        // Get announcement details for logging
        $announcement = $db->fetch("SELECT title FROM system_announcements WHERE id = ?", [$announcementId]);
        
        if (!$announcement) {
            throw new Exception('Announcement not found.');
        }
        
        // Delete announcement (this will cascade delete user_announcement_views)
        $db->execute("DELETE FROM system_announcements WHERE id = ?", [$announcementId]);
        
        // Log the action
        logEvent('INFO', 'System announcement deleted', [
            'announcement_id' => $announcementId,
            'title' => $announcement['title'],
            'admin_id' => getCurrentUser()['id']
        ]);
        
        echo json_encode(['success' => true]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?>
