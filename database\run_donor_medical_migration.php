<?php
/**
 * Donor Medical Fields Migration Runner
 * Blood Donation Management System
 * 
 * Safely runs the donor medical fields migration
 */

require_once '../config/database.php';
require_once '../config/constants.php';
require_once '../includes/functions.php';

// Only allow running from command line or admin access
if (php_sapi_name() !== 'cli') {
    // Require admin authentication
    session_start();
    
    try {
        $db = Database::getInstance();
        $adminExists = $db->fetch("SELECT id FROM users WHERE user_type = 'admin' LIMIT 1");

        if ($adminExists) {
            // Admin users exist, require authentication
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
                die('Access denied. Admin privileges required.');
            }
        } else {
            // No admin users exist, allow migration for initial setup
            echo "<!-- No admin users found. Allowing migration for initial setup. -->\n";
        }
    } catch (Exception $e) {
        // Database connection failed, allow migration to proceed
        echo "<!-- Database connection issue. Allowing migration to proceed. -->\n";
    }
}

echo "=== Blood Donation System - Donor Medical Fields Migration ===\n";
echo "Starting database migration to add donor medical fields...\n\n";

try {
    $db = Database::getInstance();
    
    // Check if migration has already been run
    $migrationExists = false;
    try {
        $result = $db->fetch("SELECT * FROM schema_migrations WHERE migration_name = 'donor_medical_fields_migration'");
        if ($result) {
            $migrationExists = true;
        }
    } catch (Exception $e) {
        // Table doesn't exist yet, which is fine
    }
    
    if ($migrationExists) {
        echo "Donor medical fields migration has already been executed. Skipping...\n";
        exit(0);
    }
    
    // Read migration file
    $migrationFile = __DIR__ . '/donor_medical_fields_migration.sql';
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    if ($sql === false) {
        throw new Exception("Failed to read migration file");
    }
    
    echo "Executing migration...\n";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    // Don't use transactions for DDL statements as they can cause issues
    $executedCount = 0;
    $skippedCount = 0;

    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $db->getConnection()->exec($statement);
                $executedCount++;
                echo ".";
            } catch (PDOException $e) {
                // Some statements might fail if they already exist, which is okay
                if (strpos($e->getMessage(), 'already exists') !== false ||
                    strpos($e->getMessage(), 'Duplicate') !== false ||
                    strpos($e->getMessage(), 'Unknown column') !== false) {
                    $skippedCount++;
                    echo "s"; // skipped
                } else {
                    throw $e;
                }
            }
        }
    }
    
    echo "\n\nMigration completed successfully!\n";
    echo "Executed $executedCount SQL statements.\n";
    echo "Skipped $skippedCount statements (already exist).\n\n";
    
    // Verify migration
    echo "Verifying migration...\n";
    
    // Check if new fields exist in donor_profiles table
    $fields = ['medications', 'allergies', 'emergency_contact', 'emergency_phone', 'relationship'];
    foreach ($fields as $field) {
        $result = $db->fetch("SHOW COLUMNS FROM donor_profiles LIKE '$field'");
        if ($result) {
            echo "✓ $field field added to donor_profiles table\n";
        } else {
            echo "✗ $field field missing\n";
        }
    }
    
    echo "\nMigration verification completed.\n";
    echo "The donor_profiles table is now ready for comprehensive medical data!\n\n";
    
    // Log the migration
    logEvent('INFO', 'Donor medical fields database migration completed', [
        'migration' => 'donor_medical_fields_migration',
        'statements_executed' => $executedCount
    ]);
    
} catch (Exception $e) {
    echo "\nMigration failed: " . $e->getMessage() . "\n";
    echo "Database may have been partially modified.\n";

    // Log the error
    try {
        logEvent('ERROR', 'Donor medical fields database migration failed', [
            'migration' => 'donor_medical_fields_migration',
            'error' => $e->getMessage()
        ]);
    } catch (Exception $logError) {
        // Ignore logging errors during migration
    }

    exit(1);
}
?>
