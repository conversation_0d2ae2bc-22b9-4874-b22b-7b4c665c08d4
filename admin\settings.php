<?php
/**
 * Admin Settings
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

$db = Database::getInstance();
$errors = [];
$success = '';

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // In a real application, we would update the settings in the database or config files
        // For this demo, we'll just show a success message
        $success = 'System settings updated successfully';
    }
}

// Handle email settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_email'])) {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Update email settings in production
        $success = 'Email settings updated successfully';
    }
}



// Get flash message
$flash = getFlashMessage();
if ($flash) {
    if ($flash['type'] === 'success') {
        $success = $flash['message'];
    } else {
        $errors[] = $flash['message'];
    }
}

// Generate CSRF token
$csrfToken = generateCSRFToken();

// Default system settings
$settings = [
    'app_name' => APP_NAME,
    'app_url' => APP_URL,
    'records_per_page' => RECORDS_PER_PAGE,
    'min_donation_interval' => MIN_DONATION_INTERVAL,
    'min_donor_weight' => MIN_DONOR_WEIGHT,
    'min_donor_age' => MIN_DONOR_AGE,
    'max_donor_age' => MAX_DONOR_AGE,
    'timezone' => DEFAULT_TIMEZONE
];

$emailSettings = [
    'smtp_host' => '',
    'smtp_port' => '587',
    'smtp_username' => '',
    'smtp_password' => '',
    'from_email' => '',
    'from_name' => 'Blood Donation System'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Enhanced Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark admin-navbar">
        <div class="container-fluid px-4">
            <a class="navbar-brand fw-bold" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="navbar-brand-icon me-2">
                        <i class="fas fa-tint"></i>
                    </div>
                    <div>
                        <div class="brand-title"><?php echo APP_NAME; ?></div>
                        <small class="brand-subtitle">Administrator Panel</small>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical nav-icon"></i>
                            <span class="nav-text">Blood Requests</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart nav-icon"></i>
                            <span class="nav-text">Donations</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.php">
                            <i class="fas fa-file-alt nav-icon"></i>
                            <span class="nav-text">Audit Logs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">Chat</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>

                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-profile" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="d-flex align-items-center">
                                <div class="admin-avatar me-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="admin-info d-none d-lg-block">
                                    <div class="admin-name">System</div>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item active" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="admin-panel-section">
                    <div class="section-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-cog"></i> System Configuration & Settings</h5>
                            <a href="index.php" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                    <div class="section-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="admin-panel-section">
                                    <div class="section-header">
                                        <h6><i class="fas fa-list"></i> Settings Categories</h6>
                                    </div>
                                    <div class="section-body p-2">
                                        <div class="list-group list-group-flush">
                                            <a href="#general" class="list-group-item list-group-item-action active border-0" data-bs-toggle="list">
                                                <i class="fas fa-info-circle text-danger"></i> General Settings
                                            </a>
                                            <a href="#donation" class="list-group-item list-group-item-action border-0" data-bs-toggle="list">
                                                <i class="fas fa-heart text-danger"></i> Donation Rules
                                            </a>
                                            <a href="#email" class="list-group-item list-group-item-action border-0" data-bs-toggle="list">
                                                <i class="fas fa-envelope text-danger"></i> Email Settings
                                            </a>
                                            <a href="#security" class="list-group-item list-group-item-action border-0" data-bs-toggle="list">
                                                <i class="fas fa-shield-alt text-danger"></i> Security
                                            </a>

                                            <a href="#backup" class="list-group-item list-group-item-action border-0" data-bs-toggle="list">
                                                <i class="fas fa-database text-danger"></i> Backup & Restore
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> System Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Version:</strong> <?php echo APP_VERSION; ?></p>
                        <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
                        <p><strong>Database:</strong> MySQL</p>
                        <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-9">
                <div class="tab-content">
                    <!-- General Settings -->
                    <div class="tab-pane fade show active" id="general">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cogs"></i> General Settings</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="settings.php">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="update_settings" value="1">
                                    
                                    <div class="mb-3">
                                        <label for="app_name" class="form-label">Application Name</label>
                                        <input type="text" class="form-control" id="app_name" name="app_name" value="<?php echo htmlspecialchars($settings['app_name']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="app_url" class="form-label">Application URL</label>
                                        <input type="text" class="form-control" id="app_url" name="app_url" value="<?php echo htmlspecialchars($settings['app_url']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">Default Timezone</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <?php foreach (timezone_identifiers_list() as $tz): ?>
                                                <option value="<?php echo $tz; ?>" <?php echo $settings['timezone'] === $tz ? 'selected' : ''; ?>>
                                                    <?php echo $tz; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="records_per_page" class="form-label">Records Per Page</label>
                                        <input type="number" class="form-control" id="records_per_page" name="records_per_page" value="<?php echo htmlspecialchars($settings['records_per_page']); ?>" min="5" max="100" required>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Save General Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Email Settings -->
                    <div class="tab-pane fade" id="email">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-envelope"></i> Email Settings</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="settings.php">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="update_email" value="1">
                                    
                                    <div class="mb-3">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host" value="<?php echo htmlspecialchars($emailSettings['smtp_host']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="text" class="form-control" id="smtp_port" name="smtp_port" value="<?php echo htmlspecialchars($emailSettings['smtp_port']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="smtp_username" class="form-label">SMTP Username</label>
                                        <input type="text" class="form-control" id="smtp_username" name="smtp_username" value="<?php echo htmlspecialchars($emailSettings['smtp_username']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="smtp_password" class="form-label">SMTP Password</label>
                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password" value="<?php echo htmlspecialchars($emailSettings['smtp_password']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="from_email" class="form-label">From Email</label>
                                        <input type="email" class="form-control" id="from_email" name="from_email" value="<?php echo htmlspecialchars($emailSettings['from_email']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="from_name" class="form-label">From Name</label>
                                        <input type="text" class="form-control" id="from_name" name="from_name" value="<?php echo htmlspecialchars($emailSettings['from_name']); ?>" required>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Save Email Settings
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#testEmailModal">
                                        <i class="fas fa-paper-plane"></i> Send Test Email
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Donation Settings -->
                    <div class="tab-pane fade" id="donation">
                        <div class="admin-panel-section">
                            <div class="section-header">
                                <h5><i class="fas fa-heart"></i> Blood Donation Rules & Requirements</h5>
                            </div>
                            <div class="section-body">
                                <form method="POST" action="settings.php">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="update_settings" value="1">

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="min_donation_interval" class="form-label">
                                                    <i class="fas fa-calendar-alt text-danger"></i> Minimum Days Between Donations
                                                </label>
                                                <input type="number" class="form-control" id="min_donation_interval" name="min_donation_interval"
                                                       value="<?php echo htmlspecialchars($settings['min_donation_interval']); ?>" min="30" max="120" required>
                                                <div class="form-text">Recommended: 56 days (8 weeks) for whole blood</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="min_donor_weight" class="form-label">
                                                    <i class="fas fa-weight text-danger"></i> Minimum Donor Weight (kg)
                                                </label>
                                                <input type="number" class="form-control" id="min_donor_weight" name="min_donor_weight"
                                                       value="<?php echo htmlspecialchars($settings['min_donor_weight']); ?>" min="45" max="70" required>
                                                <div class="form-text">Recommended: 50 kg minimum for safety</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="min_donor_age" class="form-label">
                                                    <i class="fas fa-birthday-cake text-danger"></i> Minimum Donor Age
                                                </label>
                                                <input type="number" class="form-control" id="min_donor_age" name="min_donor_age"
                                                       value="<?php echo htmlspecialchars($settings['min_donor_age']); ?>" min="16" max="21" required>
                                                <div class="form-text">Recommended: 18 years</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="max_donor_age" class="form-label">
                                                    <i class="fas fa-user-clock text-danger"></i> Maximum Donor Age
                                                </label>
                                                <input type="number" class="form-control" id="max_donor_age" name="max_donor_age"
                                                       value="<?php echo htmlspecialchars($settings['max_donor_age']); ?>" min="60" max="80" required>
                                                <div class="form-text">Recommended: 65 years</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="auto_approve_donations" name="auto_approve_donations"
                                                           <?php echo ($settings['auto_approve_donations'] ?? 0) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="auto_approve_donations">
                                                        Auto-approve eligible donations
                                                    </label>
                                                </div>
                                                <div class="form-text">Automatically approve donations from eligible donors</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="send_donation_reminders" name="send_donation_reminders"
                                                           <?php echo ($settings['send_donation_reminders'] ?? 1) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="send_donation_reminders">
                                                        Send donation reminders
                                                    </label>
                                                </div>
                                                <div class="form-text">Email reminders to eligible donors</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-save"></i> Save Donation Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Security Settings -->
                    <div class="tab-pane fade" id="security">
                        <div class="admin-panel-section">
                            <div class="section-header">
                                <h5><i class="fas fa-shield-alt"></i> Security & Authentication Settings</h5>
                            </div>
                            <div class="section-body">
                                <form method="POST" action="settings.php">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="update_settings" value="1">

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="session_timeout" class="form-label">
                                                    <i class="fas fa-clock text-danger"></i> Session Timeout (seconds)
                                                </label>
                                                <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                                                       value="<?php echo htmlspecialchars($settings['session_timeout'] ?? 3600); ?>" min="300" max="86400" required>
                                                <div class="form-text">Default: 3600 seconds (1 hour)</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="max_login_attempts" class="form-label">
                                                    <i class="fas fa-ban text-danger"></i> Max Login Attempts
                                                </label>
                                                <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts"
                                                       value="<?php echo htmlspecialchars($settings['max_login_attempts'] ?? 5); ?>" min="3" max="10" required>
                                                <div class="form-text">Default: 5 attempts before lockout</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="require_email_verification" name="require_email_verification"
                                                           <?php echo ($settings['require_email_verification'] ?? 0) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="require_email_verification">
                                                        Require email verification
                                                    </label>
                                                </div>
                                                <div class="form-text">Users must verify email before accessing system</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_audit_logging" name="enable_audit_logging"
                                                           <?php echo ($settings['enable_audit_logging'] ?? 1) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="enable_audit_logging">
                                                        Enable audit logging
                                                    </label>
                                                </div>
                                                <div class="form-text">Log all admin actions for security</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-save"></i> Save Security Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>


                    
                    <!-- Backup & Restore -->
                    <div class="tab-pane fade" id="backup">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-database"></i> Backup & Restore</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> Regular backups are essential to prevent data loss. We recommend scheduling daily backups.
                                </div>
                                
                                <div class="mb-4">
                                    <h6>Create Backup</h6>
                                    <p>Generate a backup of your database and system files.</p>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#backupModal">
                                            <i class="fas fa-download"></i> Create Database Backup
                                        </button>
                                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#backupModal">
                                            <i class="fas fa-file-archive"></i> Create Full System Backup
                                        </button>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <div class="mb-4">
                                    <h6>Restore Backup</h6>
                                    <p>Restore your system from a previous backup.</p>
                                    <div class="mb-3">
                                        <label for="backup_file" class="form-label">Select Backup File</label>
                                        <input class="form-control" type="file" id="backup_file">
                                    </div>
                                    <div class="d-grid">
                                        <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#restoreModal">
                                            <i class="fas fa-upload"></i> Restore from Backup
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Email Modal -->
    <div class="modal fade" id="testEmailModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Send Test Email</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">This feature is currently under development.</p>
                    <p>Email testing functionality will be available in the next update.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Modal -->
    <div class="modal fade" id="backupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create Backup</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">This feature is currently under development.</p>
                    <p>Backup functionality will be available in the next update.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Restore Modal -->
    <div class="modal fade" id="restoreModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Restore from Backup</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">This feature is currently under development.</p>
                    <p>Restore functionality will be available in the next update.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 