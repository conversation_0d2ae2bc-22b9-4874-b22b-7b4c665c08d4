<?php
/**
 * BloodRequest Class
 * Blood Donation Management System
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../includes/functions.php';

class BloodRequest {
    private $db;
    private $id;
    private $recipientId;
    private $bloodTypeId;
    private $unitsNeeded;
    private $urgencyLevel;
    private $hospitalName;
    private $hospitalAddress;
    private $hospitalContact;
    private $requestDate;
    private $requiredByDate;
    private $status;
    private $adminNotes;
    private $fulfilledDate;
    private $createdByAdmin;
    private $isArchived;
    private $archivedAt;
    private $archivedBy;
    private $createdAt;
    private $updatedAt;
    
    public function __construct($id = null) {
        $this->db = Database::getInstance();
        
        if ($id) {
            $this->loadRequest($id);
        }
    }
    
    /**
     * Load request data from database
     */
    private function loadRequest($id) {
        $sql = "SELECT * FROM blood_requests WHERE id = ?";
        $requestData = $this->db->fetch($sql, [$id]);

        if ($requestData) {
            $this->id = $requestData['id'];
            $this->recipientId = $requestData['recipient_id'];
            $this->bloodTypeId = $requestData['blood_type_id'];
            $this->unitsNeeded = $requestData['units_needed'];
            $this->urgencyLevel = $requestData['urgency_level'];
            $this->hospitalName = $requestData['hospital_name'];
            $this->hospitalAddress = $requestData['hospital_address'];
            $this->hospitalContact = $requestData['hospital_contact'];
            $this->requestDate = $requestData['request_date'];
            $this->requiredByDate = $requestData['required_by_date'];
            $this->status = $requestData['status'];
            $this->adminNotes = $requestData['admin_notes'];
            $this->fulfilledDate = $requestData['fulfilled_date'];
            $this->createdByAdmin = $requestData['created_by_admin'];
            $this->isArchived = $requestData['is_archived'] ?? false;
            $this->archivedAt = $requestData['archived_at'] ?? null;
            $this->archivedBy = $requestData['archived_by'] ?? null;
            $this->createdAt = $requestData['created_at'];
            $this->updatedAt = $requestData['updated_at'];
        }
    }
    
    /**
     * Create new blood request
     */
    public static function create($data) {
        $db = Database::getInstance();
        
        $sql = "INSERT INTO blood_requests (
                    recipient_id, blood_type_id, units_needed, urgency_level,
                    hospital_name, hospital_address, hospital_contact,
                    required_by_date, status, created_by_admin
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $db->execute($sql, [
            $data['recipient_id'],
            $data['blood_type_id'],
            $data['units_needed'],
            $data['urgency_level'],
            $data['hospital_name'],
            $data['hospital_address'],
            $data['hospital_contact'] ?? '',
            $data['required_by_date'],
            $data['status'] ?? REQUEST_STATUS_PENDING,
            $data['created_by_admin'] ?? null
        ]);
        
        $requestId = $db->lastInsertId();
        
        logEvent('INFO', 'Blood request created', [
            'request_id' => $requestId,
            'recipient_id' => $data['recipient_id'],
            'blood_type_id' => $data['blood_type_id'],
            'urgency_level' => $data['urgency_level']
        ]);
        
        return new self($requestId);
    }
    
    /**
     * Update blood request
     */
    public function update($data) {
        $sql = "UPDATE blood_requests SET 
                blood_type_id = ?, 
                units_needed = ?, 
                urgency_level = ?, 
                hospital_name = ?, 
                hospital_address = ?, 
                hospital_contact = ?, 
                required_by_date = ?, 
                updated_at = NOW() 
                WHERE id = ?";
        
        $this->db->execute($sql, [
            $data['blood_type_id'] ?? $this->bloodTypeId,
            $data['units_needed'] ?? $this->unitsNeeded,
            $data['urgency_level'] ?? $this->urgencyLevel,
            $data['hospital_name'] ?? $this->hospitalName,
            $data['hospital_address'] ?? $this->hospitalAddress,
            $data['hospital_contact'] ?? $this->hospitalContact,
            $data['required_by_date'] ?? $this->requiredByDate,
            $this->id
        ]);
        
        // Update object properties
        $this->bloodTypeId = $data['blood_type_id'] ?? $this->bloodTypeId;
        $this->unitsNeeded = $data['units_needed'] ?? $this->unitsNeeded;
        $this->urgencyLevel = $data['urgency_level'] ?? $this->urgencyLevel;
        $this->hospitalName = $data['hospital_name'] ?? $this->hospitalName;
        $this->hospitalAddress = $data['hospital_address'] ?? $this->hospitalAddress;
        $this->hospitalContact = $data['hospital_contact'] ?? $this->hospitalContact;
        $this->requiredByDate = $data['required_by_date'] ?? $this->requiredByDate;
        
        logEvent('INFO', 'Blood request updated', ['request_id' => $this->id]);
        
        return true;
    }
    
    /**
     * Approve blood request
     */
    public function approve($adminId, $notes = '') {
        $sql = "UPDATE blood_requests SET 
                status = ?, 
                admin_notes = ?, 
                created_by_admin = ?, 
                updated_at = NOW() 
                WHERE id = ?";
        
        $this->db->execute($sql, [
            REQUEST_STATUS_APPROVED,
            $notes,
            $adminId,
            $this->id
        ]);
        
        $this->status = REQUEST_STATUS_APPROVED;
        $this->adminNotes = $notes;
        $this->createdByAdmin = $adminId;
        
        logEvent('INFO', 'Blood request approved', [
            'request_id' => $this->id,
            'admin_id' => $adminId
        ]);
        
        return true;
    }
    
    /**
     * Reject blood request
     */
    public function reject($adminId, $reason) {
        $sql = "UPDATE blood_requests SET 
                status = ?, 
                admin_notes = ?, 
                created_by_admin = ?, 
                updated_at = NOW() 
                WHERE id = ?";
        
        $this->db->execute($sql, [
            REQUEST_STATUS_CANCELLED,
            $reason,
            $adminId,
            $this->id
        ]);
        
        $this->status = REQUEST_STATUS_CANCELLED;
        $this->adminNotes = $reason;
        $this->createdByAdmin = $adminId;
        
        logEvent('INFO', 'Blood request rejected', [
            'request_id' => $this->id,
            'admin_id' => $adminId,
            'reason' => $reason
        ]);
        
        return true;
    }
    
    /**
     * Mark as fulfilled
     */
    public function fulfill($adminId, $notes = '') {
        $sql = "UPDATE blood_requests SET 
                status = ?, 
                admin_notes = CONCAT(COALESCE(admin_notes, ''), '\n', ?), 
                fulfilled_date = NOW(), 
                updated_at = NOW() 
                WHERE id = ?";
        
        $this->db->execute($sql, [
            REQUEST_STATUS_FULFILLED,
            $notes,
            $this->id
        ]);
        
        $this->status = REQUEST_STATUS_FULFILLED;
        $this->fulfilledDate = date(DATETIME_FORMAT);
        
        logEvent('INFO', 'Blood request fulfilled', [
            'request_id' => $this->id,
            'admin_id' => $adminId
        ]);
        
        return true;
    }

    /**
     * Archive blood request instead of deleting
     */
    public function archive($adminId, $reason = '') {
        $sql = "UPDATE blood_requests SET
                is_archived = TRUE,
                archived_at = NOW(),
                archived_by = ?,
                admin_notes = CONCAT(COALESCE(admin_notes, ''), '\n', 'ARCHIVED: ', ?),
                updated_at = NOW()
                WHERE id = ?";

        $this->db->execute($sql, [
            $adminId,
            $reason,
            $this->id
        ]);

        $this->isArchived = true;
        $this->archivedAt = date(DATETIME_FORMAT);
        $this->archivedBy = $adminId;

        logEvent('INFO', 'Blood request archived', [
            'request_id' => $this->id,
            'admin_id' => $adminId,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * Restore archived blood request
     */
    public function restore($adminId, $reason = '') {
        $sql = "UPDATE blood_requests SET
                is_archived = FALSE,
                archived_at = NULL,
                archived_by = NULL,
                admin_notes = CONCAT(COALESCE(admin_notes, ''), '\n', 'RESTORED: ', ?),
                updated_at = NOW()
                WHERE id = ?";

        $this->db->execute($sql, [
            $reason,
            $this->id
        ]);

        $this->isArchived = false;
        $this->archivedAt = null;
        $this->archivedBy = null;

        logEvent('INFO', 'Blood request restored', [
            'request_id' => $this->id,
            'admin_id' => $adminId,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * Auto-archive overdue requests
     */
    public static function archiveOverdueRequests($adminId = null) {
        $db = Database::getInstance();

        // Find overdue requests that are not archived and not fulfilled/cancelled
        $sql = "SELECT id FROM blood_requests
                WHERE required_by_date < CURDATE()
                AND status NOT IN (?, ?)
                AND (is_archived IS NULL OR is_archived = FALSE)";

        $overdueRequests = $db->fetchAll($sql, [REQUEST_STATUS_FULFILLED, REQUEST_STATUS_CANCELLED]);

        $archivedCount = 0;
        foreach ($overdueRequests as $requestData) {
            $request = new self($requestData['id']);
            $request->archive($adminId ?? 1, 'Auto-archived: Request overdue');
            $archivedCount++;
        }

        if ($archivedCount > 0) {
            logEvent('INFO', 'Auto-archived overdue requests', [
                'count' => $archivedCount,
                'admin_id' => $adminId
            ]);
        }

        return $archivedCount;
    }

    /**
     * Get compatible donors
     */
    public function getCompatibleDonors($page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        
        // Get blood type
        $bloodTypeSql = "SELECT type FROM blood_types WHERE id = ?";
        $bloodTypeResult = $this->db->fetch($bloodTypeSql, [$this->bloodTypeId]);
        
        if (!$bloodTypeResult) {
            return [
                'donors' => [],
                'pagination' => paginate(0, $page, $limit)
            ];
        }
        
        $recipientBloodType = $bloodTypeResult['type'];
        
        // Get compatible donor blood types
        $compatibleTypes = getCompatibleBloodTypes($recipientBloodType);
        
        if (empty($compatibleTypes)) {
            return [
                'donors' => [],
                'pagination' => paginate(0, $page, $limit)
            ];
        }
        
        // Get blood type IDs
        $placeholders = implode(',', array_fill(0, count($compatibleTypes), '?'));
        $bloodTypesSql = "SELECT id FROM blood_types WHERE type IN ($placeholders)";
        $bloodTypesResult = $this->db->fetchAll($bloodTypesSql, $compatibleTypes);
        $bloodTypeIds = array_column($bloodTypesResult, 'id');
        
        if (empty($bloodTypeIds)) {
            return [
                'donors' => [],
                'pagination' => paginate(0, $page, $limit)
            ];
        }
        
        // Get total count
        $placeholders = implode(',', array_fill(0, count($bloodTypeIds), '?'));
        $countSql = "SELECT COUNT(*) as total 
                     FROM users u 
                     JOIN donor_profiles dp ON u.id = dp.user_id 
                     WHERE u.user_type = ? 
                     AND u.status = ? 
                     AND dp.blood_type_id IN ($placeholders) 
                     AND dp.eligibility_status = ?";
        
        $countParams = array_merge([USER_TYPE_DONOR, USER_STATUS_ACTIVE], $bloodTypeIds, ['eligible']);
        $totalResult = $this->db->fetch($countSql, $countParams);
        $total = $totalResult['total'];
        
        // Get donors
        $sql = "SELECT u.*, dp.*, bt.type as blood_type 
                FROM users u 
                JOIN donor_profiles dp ON u.id = dp.user_id 
                JOIN blood_types bt ON dp.blood_type_id = bt.id 
                WHERE u.user_type = ? 
                AND u.status = ? 
                AND dp.blood_type_id IN ($placeholders) 
                AND dp.eligibility_status = ?
                ORDER BY dp.last_donation_date ASC, u.created_at DESC
                LIMIT ? OFFSET ?";
        
        $params = array_merge([USER_TYPE_DONOR, USER_STATUS_ACTIVE], $bloodTypeIds, ['eligible', $limit, $offset]);
        $donors = $this->db->fetchAll($sql, $params);
        
        return [
            'donors' => $donors,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
    
    /**
     * Get related donations
     */
    public function getDonations() {
        $sql = "SELECT d.*, u.first_name, u.last_name, u.phone, u.email 
                FROM donations d
                JOIN users u ON d.donor_id = u.id
                WHERE d.request_id = ?
                ORDER BY d.donation_date DESC";
        
        return $this->db->fetchAll($sql, [$this->id]);
    }
    
    /**
     * Get request details with related data
     */
    public function getDetails() {
        $sql = "SELECT br.*, 
                       bt.type as blood_type,
                       u.first_name as recipient_first_name, 
                       u.last_name as recipient_last_name,
                       u.phone as recipient_phone,
                       u.email as recipient_email,
                       rp.medical_condition,
                       rp.emergency_contact,
                       rp.emergency_phone,
                       admin.first_name as admin_first_name,
                       admin.last_name as admin_last_name
                FROM blood_requests br
                JOIN blood_types bt ON br.blood_type_id = bt.id
                JOIN users u ON br.recipient_id = u.id
                JOIN recipient_profiles rp ON u.id = rp.user_id
                LEFT JOIN users admin ON br.created_by_admin = admin.id
                WHERE br.id = ?";
        
        $details = $this->db->fetch($sql, [$this->id]);
        
        if ($details) {
            $details['donations'] = $this->getDonations();
        }
        
        return $details;
    }
    
    /**
     * Check if request is urgent
     */
    public function isUrgent() {
        return in_array($this->urgencyLevel, [URGENCY_HIGH, URGENCY_CRITICAL]);
    }
    
    /**
     * Check if request is overdue
     */
    public function isOverdue() {
        return strtotime($this->requiredByDate) < strtotime('today');
    }
    
    /**
     * Get days until required
     */
    public function getDaysUntilRequired() {
        $requiredDate = new DateTime($this->requiredByDate);
        $today = new DateTime();
        $diff = $today->diff($requiredDate);
        
        if ($requiredDate < $today) {
            return -$diff->days; // Negative for overdue
        }
        
        return $diff->days;
    }
    
    /**
     * Delete blood request
     */
    public function delete() {
        $sql = "DELETE FROM blood_requests WHERE id = ?";
        $this->db->execute($sql, [$this->id]);
        
        logEvent('INFO', 'Blood request deleted', ['request_id' => $this->id]);
        
        return true;
    }
    
    // Getters
    public function getId() { return $this->id; }
    public function getRecipientId() { return $this->recipientId; }
    public function getBloodTypeId() { return $this->bloodTypeId; }
    public function getUnitsNeeded() { return $this->unitsNeeded; }
    public function getUrgencyLevel() { return $this->urgencyLevel; }
    public function getHospitalName() { return $this->hospitalName; }
    public function getHospitalAddress() { return $this->hospitalAddress; }
    public function getHospitalContact() { return $this->hospitalContact; }
    public function getRequestDate() { return $this->requestDate; }
    public function getRequiredByDate() { return $this->requiredByDate; }
    public function getStatus() { return $this->status; }
    public function getAdminNotes() { return $this->adminNotes; }
    public function getFulfilledDate() { return $this->fulfilledDate; }
    public function getCreatedByAdmin() { return $this->createdByAdmin; }
    public function getCreatedAt() { return $this->createdAt; }
    public function getUpdatedAt() { return $this->updatedAt; }
    
    /**
     * Get all blood requests with pagination
     */
    public static function getAll($page = 1, $limit = RECORDS_PER_PAGE, $filters = []) {
        $db = Database::getInstance();
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE 1=1";
        $params = [];

        // Exclude archived requests by default unless specifically requested (temporarily disabled for debugging)
        // if (!isset($filters['include_archived']) || !$filters['include_archived']) {
        //     $whereClause .= " AND (br.is_archived IS NULL OR br.is_archived = FALSE)";
        // }

        // Auto-archive overdue requests before displaying (temporarily disabled for debugging)
        // if (!isset($filters['skip_auto_archive']) || !$filters['skip_auto_archive']) {
        //     self::archiveOverdueRequests();
        // }

        if (!empty($filters['status'])) {
            $whereClause .= " AND br.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['urgency_level'])) {
            $whereClause .= " AND br.urgency_level = ?";
            $params[] = $filters['urgency_level'];
        }
        
        if (!empty($filters['blood_type_id'])) {
            $whereClause .= " AND br.blood_type_id = ?";
            $params[] = $filters['blood_type_id'];
        }
        
        if (!empty($filters['search'])) {
            $whereClause .= " AND (u.first_name LIKE ? OR u.last_name LIKE ? OR br.hospital_name LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM blood_requests br 
                     JOIN users u ON br.recipient_id = u.id 
                     $whereClause";
        
        $totalResult = $db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get requests
        $sql = "SELECT br.*, 
                       bt.type as blood_type,
                       u.first_name as recipient_first_name, 
                       u.last_name as recipient_last_name,
                       u.phone as recipient_phone
                FROM blood_requests br
                JOIN blood_types bt ON br.blood_type_id = bt.id
                JOIN users u ON br.recipient_id = u.id
                $whereClause 
                ORDER BY br.urgency_level DESC, br.required_by_date ASC, br.created_at DESC
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $requests = $db->fetchAll($sql, $params);
        
        return [
            'requests' => $requests,
            'pagination' => paginate($total, $page, $limit)
        ];
    }
    
    /**
     * Get urgent requests
     */
    public static function getUrgentRequests($limit = 10) {
        $db = Database::getInstance();
        
        $sql = "SELECT br.*, 
                       bt.type as blood_type,
                       u.first_name as recipient_first_name, 
                       u.last_name as recipient_last_name
                FROM blood_requests br
                JOIN blood_types bt ON br.blood_type_id = bt.id
                JOIN users u ON br.recipient_id = u.id
                WHERE br.status IN (?, ?) 
                AND br.urgency_level IN (?, ?)
                AND br.required_by_date >= CURDATE()
                ORDER BY br.urgency_level DESC, br.required_by_date ASC
                LIMIT ?";
        
        return $db->fetchAll($sql, [
            REQUEST_STATUS_PENDING,
            REQUEST_STATUS_APPROVED,
            URGENCY_HIGH,
            URGENCY_CRITICAL,
            $limit
        ]);
    }
    
    /**
     * Get statistics
     */
    public static function getStatistics() {
        $db = Database::getInstance();
        
        $sql = "SELECT 
                COUNT(*) as total_requests,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as pending_requests,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as approved_requests,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as fulfilled_requests,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as cancelled_requests,
                SUM(CASE WHEN urgency_level = ? THEN 1 ELSE 0 END) as critical_requests,
                SUM(CASE WHEN urgency_level = ? THEN 1 ELSE 0 END) as high_requests
                FROM blood_requests";
        
        return $db->fetch($sql, [
            REQUEST_STATUS_PENDING,
            REQUEST_STATUS_APPROVED,
            REQUEST_STATUS_FULFILLED,
            REQUEST_STATUS_CANCELLED,
            URGENCY_CRITICAL,
            URGENCY_HIGH
        ]);
    }
}
?>
