<?php
/**
 * Test Admin Access
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing Admin Access</h2>";

try {
    // Start session
    session_start();
    
    echo "<p><strong>Session Data:</strong></p>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    
    // Test database connection
    $pdo = new PDO('mysql:host=localhost;dbname=blood_donation_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? AND user_type = ?");
    $stmt->execute(['admin', 'admin']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✅ Admin user found in database</p>";
        echo "<p><strong>Admin Details:</strong></p>";
        echo "<ul>";
        echo "<li>ID: " . $admin['id'] . "</li>";
        echo "<li>Username: " . htmlspecialchars($admin['username']) . "</li>";
        echo "<li>User Type: " . htmlspecialchars($admin['user_type']) . "</li>";
        echo "<li>Status: " . htmlspecialchars($admin['status']) . "</li>";
        echo "</ul>";
        
        // Test login simulation
        echo "<h3>Simulating Login:</h3>";
        
        // Simulate the login process
        $_SESSION['user_id'] = $admin['id'];
        $_SESSION['username'] = $admin['username'];
        $_SESSION['user_type'] = $admin['user_type'];
        $_SESSION['first_name'] = $admin['first_name'];
        $_SESSION['last_name'] = $admin['last_name'];
        $_SESSION['logged_in'] = true;
        $_SESSION['last_activity'] = time();
        
        echo "<p style='color: green;'>✅ Session variables set</p>";
        echo "<p><strong>Updated Session Data:</strong></p>";
        echo "<pre>" . print_r($_SESSION, true) . "</pre>";
        
        // Test admin permission check
        if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin') {
            echo "<p style='color: green;'>✅ Admin permissions verified</p>";
        } else {
            echo "<p style='color: red;'>❌ Admin permissions failed</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Admin user not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?> 