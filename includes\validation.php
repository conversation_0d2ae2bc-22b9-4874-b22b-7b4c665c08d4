<?php
/**
 * Input Validation Functions
 * Blood Donation Management System
 */

require_once __DIR__ . '/../config/constants.php';

/**
 * Validation result class
 */
class ValidationResult {
    public $isValid = true;
    public $errors = [];
    
    public function addError($field, $message) {
        $this->isValid = false;
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        $this->errors[$field][] = $message;
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    public function getFirstError($field = null) {
        if ($field) {
            return isset($this->errors[$field]) ? $this->errors[$field][0] : null;
        }
        
        foreach ($this->errors as $fieldErrors) {
            return $fieldErrors[0];
        }
        
        return null;
    }
}

/**
 * Validate basic user registration data (no role-specific validation)
 */
function validateBasicRegistration($data) {
    $result = new ValidationResult();

    // Username validation
    if (empty($data['username'])) {
        $result->addError('username', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['username']) < 3) {
        $result->addError('username', 'Username must be at least 3 characters long');
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
        $result->addError('username', 'Username can only contain letters, numbers, and underscores');
    }

    // Password validation
    if (empty($data['password'])) {
        $result->addError('password', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['password']) < 8) {
        $result->addError('password', 'Password must be at least 8 characters long');
    } elseif (!preg_match('/[A-Z]/', $data['password']) || !preg_match('/[a-z]/', $data['password']) || !preg_match('/[0-9]/', $data['password'])) {
        $result->addError('password', 'Password must include at least one uppercase letter, one lowercase letter, and one number');
    }

    // Password confirmation
    if (empty($data['confirm_password'])) {
        $result->addError('confirm_password', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif ($data['password'] !== $data['confirm_password']) {
        $result->addError('confirm_password', 'Passwords do not match');
    }

    // First name validation
    if (empty($data['first_name'])) {
        $result->addError('first_name', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['first_name']) < 2) {
        $result->addError('first_name', 'First name must be at least 2 characters long');
    }

    // Last name validation
    if (empty($data['last_name'])) {
        $result->addError('last_name', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['last_name']) < 2) {
        $result->addError('last_name', 'Last name must be at least 2 characters long');
    }

    // Phone validation (optional)
    if (!empty($data['phone']) && !preg_match('/^[\+]?[0-9\s\-\(\)]{10,}$/', $data['phone'])) {
        $result->addError('phone', 'Invalid phone number format');
    }

    return $result;
}

/**
 * Validate unified user registration data
 */
function validateUnifiedRegistration($data, $selectedRoles) {
    $result = new ValidationResult();

    // Basic validation
    $basicValidation = validateUserRegistration($data);
    if (!$basicValidation->isValid) {
        $result->errors = array_merge($result->errors, $basicValidation->errors);
        $result->isValid = false;
    }

    // Role-specific validation
    if (in_array('donor', $selectedRoles)) {
        if (empty($data['blood_type_id']) || $data['blood_type_id'] < 1) {
            $result->addError('blood_type_id', 'Please select a valid blood type for donor registration.');
        }

        if (!empty($data['birth_date'])) {
            $birthDate = DateTime::createFromFormat('Y-m-d', $data['birth_date']);
            if (!$birthDate) {
                $result->addError('birth_date', 'Please enter a valid birth date.');
            } else {
                $age = $birthDate->diff(new DateTime())->y;
                if ($age < 18 || $age > 65) {
                    $result->addError('birth_date', 'Donors must be between 18 and 65 years old.');
                }
            }
        }

        if (!empty($data['weight']) && $data['weight'] < 50) {
            $result->addError('weight', 'Minimum weight for blood donation is 50 kg.');
        }
    }

    if (in_array('recipient', $selectedRoles)) {
        // Recipient-specific validation can be added here if needed
        // For now, recipient fields are optional
    }

    return $result;
}

/**
 * Validate user registration data
 */
function validateUserRegistration($data) {
    $result = new ValidationResult();
    
    // Username validation
    if (empty($data['username'])) {
        $result->addError('username', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['username']) < 3) {
        $result->addError('username', 'Username must be at least 3 characters long');
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
        $result->addError('username', 'Username can only contain letters, numbers, and underscores');
    }
    
    // Email validation
    if (empty($data['email'])) {
        $result->addError('email', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (!validateEmail($data['email'])) {
        $result->addError('email', ERROR_MESSAGES['INVALID_EMAIL']);
    }
    
    // Password validation
    if (empty($data['password'])) {
        $result->addError('password', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (!validatePassword($data['password'])) {
        $result->addError('password', ERROR_MESSAGES['WEAK_PASSWORD']);
    }
    
    // Confirm password
    if (empty($data['confirm_password'])) {
        $result->addError('confirm_password', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif ($data['password'] !== $data['confirm_password']) {
        $result->addError('confirm_password', 'Passwords do not match');
    }
    
    // First name validation
    if (empty($data['first_name'])) {
        $result->addError('first_name', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['first_name']) < 2) {
        $result->addError('first_name', 'First name must be at least 2 characters long');
    }
    
    // Last name validation
    if (empty($data['last_name'])) {
        $result->addError('last_name', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['last_name']) < 2) {
        $result->addError('last_name', 'Last name must be at least 2 characters long');
    }
    
    // Phone validation
    if (!empty($data['phone']) && !preg_match('/^[\+]?[0-9\s\-\(\)]{10,}$/', $data['phone'])) {
        $result->addError('phone', 'Invalid phone number format');
    }
    
    // User type validation
    if (empty($data['user_type'])) {
        $result->addError('user_type', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (!in_array($data['user_type'], [USER_TYPE_DONOR, USER_TYPE_RECIPIENT])) {
        $result->addError('user_type', 'Invalid user type');
    }
    
    // Donor-specific validation
    if ($data['user_type'] === USER_TYPE_DONOR) {
        if (empty($data['blood_type_id'])) {
            $result->addError('blood_type_id', ERROR_MESSAGES['REQUIRED_FIELD']);
        }
        
        if (empty($data['weight']) || !is_numeric($data['weight'])) {
            $result->addError('weight', 'Weight is required and must be a number');
        } elseif ($data['weight'] < MIN_DONOR_WEIGHT) {
            $result->addError('weight', 'Minimum weight requirement is ' . MIN_DONOR_WEIGHT . ' kg');
        }
        
        if (empty($data['birth_date'])) {
            $result->addError('birth_date', ERROR_MESSAGES['REQUIRED_FIELD']);
        } else {
            $age = calculateAge($data['birth_date']);
            if ($age < MIN_DONOR_AGE || $age > MAX_DONOR_AGE) {
                $result->addError('birth_date', 'Age must be between ' . MIN_DONOR_AGE . ' and ' . MAX_DONOR_AGE . ' years');
            }
        }
    }
    
    return $result;
}

/**
 * Validate login data
 */
function validateLogin($data) {
    $result = new ValidationResult();
    
    if (empty($data['username'])) {
        $result->addError('username', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    if (empty($data['password'])) {
        $result->addError('password', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    return $result;
}

/**
 * Validate password reset request
 */
function validatePasswordResetRequest($data) {
    $result = new ValidationResult();
    
    if (empty($data['username'])) {
        $result->addError('username', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['username']) < 3) {
        $result->addError('username', 'Username must be at least 3 characters long');
    }
    
    return $result;
}

/**
 * Validate password reset
 */
function validatePasswordReset($data) {
    $result = new ValidationResult();
    
    if (empty($data['token'])) {
        $result->addError('token', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    if (empty($data['password'])) {
        $result->addError('password', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (!validatePassword($data['password'])) {
        $result->addError('password', ERROR_MESSAGES['WEAK_PASSWORD']);
    }
    
    if (empty($data['confirm_password'])) {
        $result->addError('confirm_password', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif ($data['password'] !== $data['confirm_password']) {
        $result->addError('confirm_password', 'Passwords do not match');
    }
    
    return $result;
}

/**
 * Validate blood request data
 */
function validateBloodRequest($data) {
    $result = new ValidationResult();
    
    if (empty($data['blood_type_id'])) {
        $result->addError('blood_type_id', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    if (empty($data['units_needed']) || !is_numeric($data['units_needed'])) {
        $result->addError('units_needed', 'Units needed is required and must be a number');
    } elseif ($data['units_needed'] < 1 || $data['units_needed'] > 10) {
        $result->addError('units_needed', 'Units needed must be between 1 and 10');
    }
    
    if (empty($data['urgency_level'])) {
        $result->addError('urgency_level', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (!array_key_exists($data['urgency_level'], URGENCY_LEVELS)) {
        $result->addError('urgency_level', 'Invalid urgency level');
    }
    
    if (empty($data['hospital_name'])) {
        $result->addError('hospital_name', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    if (empty($data['hospital_address'])) {
        $result->addError('hospital_address', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    if (empty($data['required_by_date'])) {
        $result->addError('required_by_date', ERROR_MESSAGES['REQUIRED_FIELD']);
    } else {
        $requiredDate = strtotime($data['required_by_date']);
        $today = strtotime('today');
        
        if ($requiredDate < $today) {
            $result->addError('required_by_date', 'Required date cannot be in the past');
        }
    }
    
    return $result;
}

/**
 * Validate donation scheduling data
 */
function validateDonationScheduling($data) {
    $result = new ValidationResult();
    
    if (empty($data['donation_date'])) {
        $result->addError('donation_date', ERROR_MESSAGES['REQUIRED_FIELD']);
    } else {
        $donationDate = strtotime($data['donation_date']);
        $today = strtotime('today');
        
        if ($donationDate < $today) {
            $result->addError('donation_date', 'Donation date cannot be in the past');
        }
    }
    
    if (empty($data['location'])) {
        $result->addError('location', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    return $result;
}

/**
 * Validate profile update data
 */
function validateProfileUpdate($data, $userType) {
    $result = new ValidationResult();
    
    // Common fields
    if (empty($data['first_name'])) {
        $result->addError('first_name', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    if (empty($data['last_name'])) {
        $result->addError('last_name', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    if (!empty($data['phone']) && !preg_match('/^[\+]?[0-9\s\-\(\)]{10,}$/', $data['phone'])) {
        $result->addError('phone', 'Invalid phone number format');
    }
    
    // User type specific validation
    if ($userType === USER_TYPE_DONOR) {
        if (!empty($data['weight']) && (!is_numeric($data['weight']) || $data['weight'] < MIN_DONOR_WEIGHT)) {
            $result->addError('weight', 'Weight must be at least ' . MIN_DONOR_WEIGHT . ' kg');
        }
    }
    
    return $result;
}

/**
 * Validate notification data
 */
function validateNotification($data) {
    $result = new ValidationResult();
    
    if (empty($data['title'])) {
        $result->addError('title', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    if (empty($data['message'])) {
        $result->addError('message', ERROR_MESSAGES['REQUIRED_FIELD']);
    }
    
    if (empty($data['target_audience'])) {
        $result->addError('target_audience', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (!in_array($data['target_audience'], ['all', 'donors', 'recipients'])) {
        $result->addError('target_audience', 'Invalid target audience');
    }
    
    return $result;
}

/**
 * Validate chat message data
 */
function validateChatMessage($data) {
    $result = new ValidationResult();
    
    if (empty($data['message'])) {
        $result->addError('message', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['message']) > 1000) {
        $result->addError('message', 'Message cannot exceed 1000 characters');
    }
    
    if (empty($data['receiver_id']) || !is_numeric($data['receiver_id'])) {
        $result->addError('receiver_id', 'Invalid receiver');
    }
    
    return $result;
}

/**
 * Validate date format
 */
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * Validate blood type
 */
function validateBloodType($bloodType) {
    return array_key_exists($bloodType, BLOOD_TYPES);
}

/**
 * Sanitize and validate integer
 */
function validateInteger($value, $min = null, $max = null) {
    $value = filter_var($value, FILTER_VALIDATE_INT);
    
    if ($value === false) {
        return false;
    }
    
    if ($min !== null && $value < $min) {
        return false;
    }
    
    if ($max !== null && $value > $max) {
        return false;
    }
    
    return $value;
}

/**
 * Validate and sanitize string
 */
function validateString($value, $minLength = 0, $maxLength = null, $pattern = null) {
    $value = trim($value);
    
    if (strlen($value) < $minLength) {
        return false;
    }
    
    if ($maxLength !== null && strlen($value) > $maxLength) {
        return false;
    }
    
    if ($pattern !== null && !preg_match($pattern, $value)) {
        return false;
    }
    
    return $value;
}

/**
 * Validate array contains only allowed values
 */
function validateArrayValues($array, $allowedValues) {
    if (!is_array($array)) {
        return false;
    }
    
    foreach ($array as $value) {
        if (!in_array($value, $allowedValues)) {
            return false;
        }
    }
    
    return true;
}

/**
 * Custom validation rule executor
 */
function executeValidationRules($data, $rules) {
    $result = new ValidationResult();
    
    foreach ($rules as $field => $fieldRules) {
        $value = $data[$field] ?? null;
        
        foreach ($fieldRules as $rule => $params) {
            switch ($rule) {
                case 'required':
                    if (empty($value)) {
                        $result->addError($field, ERROR_MESSAGES['REQUIRED_FIELD']);
                    }
                    break;
                    
                case 'email':
                    if (!empty($value) && !validateEmail($value)) {
                        $result->addError($field, ERROR_MESSAGES['INVALID_EMAIL']);
                    }
                    break;
                    
                case 'min_length':
                    if (!empty($value) && strlen($value) < $params) {
                        $result->addError($field, "Minimum length is $params characters");
                    }
                    break;
                    
                case 'max_length':
                    if (!empty($value) && strlen($value) > $params) {
                        $result->addError($field, "Maximum length is $params characters");
                    }
                    break;
                    
                case 'numeric':
                    if (!empty($value) && !is_numeric($value)) {
                        $result->addError($field, "Must be a number");
                    }
                    break;
                    
                case 'in':
                    if (!empty($value) && !in_array($value, $params)) {
                        $result->addError($field, "Invalid value");
                    }
                    break;
            }
        }
    }
    
    return $result;
}

/**
 * Validate profile visibility setting
 */
function validateProfileVisibility($visibility) {
    return in_array($visibility, ['public', 'private', 'contacts_only']);
}

/**
 * Validate theme setting
 */
function validateTheme($theme) {
    return in_array($theme, ['light', 'dark', 'auto']);
}

/**
 * Validate language code
 */
function validateLanguage($language) {
    return in_array($language, ['en', 'es', 'fr', 'de', 'it', 'pt']);
}

/**
 * Validate timezone
 */
function validateTimezone($timezone) {
    return in_array($timezone, timezone_identifiers_list());
}

/**
 * Validate username step for multi-step password reset
 */
function validateUsernameStep($data) {
    $result = new ValidationResult();
    
    if (empty($data['username'])) {
        $result->addError('username', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['username']) < 3) {
        $result->addError('username', 'Username must be at least 3 characters long');
    }
    
    return $result;
}

/**
 * Validate identity step for multi-step password reset
 */
function validateIdentityStep($data) {
    $result = new ValidationResult();
    
    if (empty($data['first_name'])) {
        $result->addError('first_name', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['first_name']) < 2) {
        $result->addError('first_name', 'First name must be at least 2 characters long');
    } elseif (strlen($data['first_name']) > 50) {
        $result->addError('first_name', 'First name cannot exceed 50 characters');
    }
    
    if (empty($data['last_name'])) {
        $result->addError('last_name', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (strlen($data['last_name']) < 2) {
        $result->addError('last_name', 'Last name must be at least 2 characters long');
    } elseif (strlen($data['last_name']) > 50) {
        $result->addError('last_name', 'Last name cannot exceed 50 characters');
    }
    
    return $result;
}

/**
 * Validate password change step for multi-step password reset
 */
function validatePasswordChangeStep($data) {
    $result = new ValidationResult();
    
    if (empty($data['password'])) {
        $result->addError('password', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif (!validatePassword($data['password'])) {
        $result->addError('password', ERROR_MESSAGES['WEAK_PASSWORD']);
    }
    
    if (empty($data['confirm_password'])) {
        $result->addError('confirm_password', ERROR_MESSAGES['REQUIRED_FIELD']);
    } elseif ($data['password'] !== $data['confirm_password']) {
        $result->addError('confirm_password', 'Passwords do not match');
    }
    
    return $result;
}
?>
