<?php
/**
 * Toggle System Announcement Status
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');
requirePermission(USER_TYPE_ADMIN, '../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $announcementId = (int)($input['id'] ?? 0);
        
        if ($announcementId <= 0) {
            throw new Exception('Invalid announcement ID.');
        }
        
        $db = Database::getInstance();
        
        // Get current status
        $announcement = $db->fetch("SELECT is_active FROM system_announcements WHERE id = ?", [$announcementId]);
        
        if (!$announcement) {
            throw new Exception('Announcement not found.');
        }
        
        // Toggle status
        $newStatus = !$announcement['is_active'];
        $db->execute("UPDATE system_announcements SET is_active = ? WHERE id = ?", [$newStatus, $announcementId]);
        
        // Log the action
        logEvent('INFO', 'System announcement status toggled', [
            'announcement_id' => $announcementId,
            'new_status' => $newStatus ? 'active' : 'inactive',
            'admin_id' => getCurrentUser()['id']
        ]);
        
        echo json_encode(['success' => true, 'new_status' => $newStatus]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?>
