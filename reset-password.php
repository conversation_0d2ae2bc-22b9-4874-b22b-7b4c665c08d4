<?php
/**
 * Reset Password Page
 * Blood Donation Management System
 */

require_once 'config/constants.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

// Start session
if (!startSecureSession()) {
    redirectWithMessage('login.php', ERROR_MESSAGES['SESSION_EXPIRED'], 'error');
}

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    switch ($user['user_type']) {
        case USER_TYPE_ADMIN:
            header('Location: admin/');
            break;
        case USER_TYPE_DONOR:
            header('Location: donor/');
            break;
        case USER_TYPE_RECIPIENT:
            header('Location: recipient/');
            break;
    }
    exit;
}

$errors = [];
$success = '';
$token = sanitizeInput($_GET['token'] ?? '');

// Verify token
$tokenData = null;
if ($token) {
    $tokenData = verifyPasswordResetToken($token);
    if (!$tokenData) {
        redirectWithMessage('forgot-password.php', ERROR_MESSAGES['INVALID_TOKEN'], 'error');
    }
} else {
    redirectWithMessage('forgot-password.php', 'Invalid reset link', 'error');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Validate input
        $resetData = [
            'token' => sanitizeInput($_POST['token'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? ''
        ];
        
        $validation = validatePasswordReset($resetData);
        
        if ($validation->isValid) {
            try {
                // Reset password
                $result = resetPassword($resetData['token'], $resetData['password']);
                
                if ($result) {
                    redirectWithMessage('login.php', SUCCESS_MESSAGES['PASSWORD_CHANGED'], 'success');
                } else {
                    $errors[] = 'Failed to reset password. Please try again.';
                }
                
            } catch (Exception $e) {
                $errors[] = $e->getMessage();
                logEvent('ERROR', 'Password reset failed', [
                    'token' => $resetData['token'],
                    'error' => $e->getMessage()
                ]);
            }
        } else {
            foreach ($validation->getErrors() as $field => $fieldErrors) {
                $errors = array_merge($errors, $fieldErrors);
            }
        }
    }
}

// Generate CSRF token
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow-sm mt-5">
                    <div class="card-header bg-danger text-white text-center">
                        <h4><i class="fas fa-lock"></i> Reset Password</h4>
                        <p class="mb-0">Enter your new password</p>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($tokenData): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-user"></i> 
                                Resetting password for: <strong><?php echo htmlspecialchars($tokenData['email']); ?></strong>
                            </div>
                            
                            <form method="POST" action="reset-password.php">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">New Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        Password must be at least <?php echo PASSWORD_MIN_LENGTH; ?> characters long 
                                        and contain uppercase, lowercase, and number.
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-save"></i> Reset Password
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="login.php" class="text-decoration-none">
                                <i class="fas fa-arrow-left"></i> Back to Login
                            </a>
                        </div>
                    </div>
                    <div class="card-footer text-center text-muted">
                        <small>
                            <i class="fas fa-shield-alt"></i> Secure Password Reset
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        function togglePasswordVisibility(inputId, buttonId) {
            const input = document.getElementById(inputId);
            const button = document.getElementById(buttonId);
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        document.getElementById('togglePassword').addEventListener('click', function() {
            togglePasswordVisibility('password', 'togglePassword');
        });
        
        document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
            togglePasswordVisibility('confirm_password', 'toggleConfirmPassword');
        });
        
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Password strength indicator
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const minLength = <?php echo PASSWORD_MIN_LENGTH; ?>;
            
            let strength = 0;
            let feedback = [];
            
            if (password.length >= minLength) strength++;
            else feedback.push('At least ' + minLength + ' characters');
            
            if (/[a-z]/.test(password)) strength++;
            else feedback.push('Lowercase letter');
            
            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('Uppercase letter');
            
            if (/\d/.test(password)) strength++;
            else feedback.push('Number');
            
            // Update visual feedback (you can enhance this)
            const input = this;
            if (strength < 4) {
                input.classList.remove('is-valid');
                input.classList.add('is-invalid');
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-info)');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
