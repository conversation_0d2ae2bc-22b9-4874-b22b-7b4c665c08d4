<?php
/**
 * Multi-Step Password Reset Page
 * Blood Donation Management System
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

// Start session
if (!startSecureSession()) {
    redirectWithMessage('login.php', ERROR_MESSAGES['SESSION_EXPIRED'], 'error');
}

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    switch ($user['user_type']) {
        case USER_TYPE_ADMIN:
            header('Location: admin/');
            break;
        case USER_TYPE_DONOR:
            header('Location: donor/');
            break;
        case USER_TYPE_RECIPIENT:
            header('Location: recipient/');
            break;
    }
    exit;
}

$errors = [];
$success = '';
$currentStep = 1;
$verifiedUsername = '';
$verifiedUser = null;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        $step = (int)($_POST['step'] ?? 1);
        
        switch ($step) {
            case 1:
                // Step 1: Username Verification
                $validation = validateUsernameStep($_POST);
                if ($validation->isValid) {
                    $username = sanitizeInput($_POST['username']);
                    $user = verifyUsernameExists($username);
                    
                    if ($user) {
                        $verifiedUsername = $username;
                        $verifiedUser = $user;
                        $currentStep = 2;
                        // Store verification data in session
                        $_SESSION['reset_username'] = $username;
                        $_SESSION['reset_user_id'] = $user['id'];
                    } else {
                        $errors[] = 'Username not found. Please check your username and try again.';
                    }
                } else {
                    $errors = array_merge($errors, array_values($validation->getErrors()));
                }
                break;
                
            case 2:
                // Step 2: Identity Verification
                if (!isset($_SESSION['reset_username'])) {
                    redirectWithMessage('multi-step-reset.php', 'Please start from the beginning', 'error');
                }
                
                $validation = validateIdentityStep($_POST);
                if ($validation->isValid) {
                    $firstName = sanitizeInput($_POST['first_name']);
                    $lastName = sanitizeInput($_POST['last_name']);
                    
                    $user = verifyUserIdentity($_SESSION['reset_user_id'], $firstName, $lastName);
                    
                    if ($user) {
                        $verifiedUser = $user;
                        $currentStep = 3;
                        // Store identity verification in session
                        $_SESSION['reset_identity_verified'] = true;
                    } else {
                        $errors[] = 'The provided information does not match our records. Please try again.';
                    }
                } else {
                    $errors = array_merge($errors, array_values($validation->getErrors()));
                }
                break;
                
            case 3:
                // Step 3: Password Change
                if (!isset($_SESSION['reset_username']) || !isset($_SESSION['reset_identity_verified'])) {
                    redirectWithMessage('multi-step-reset.php', 'Please start from the beginning', 'error');
                }
                
                $validation = validatePasswordChangeStep($_POST);
                if ($validation->isValid) {
                    $newPassword = $_POST['password'];
                    
                    try {
                        $result = changeUserPassword($_SESSION['reset_user_id'], $newPassword);
                        
                        if ($result) {
                            // Clear session data
                            unset($_SESSION['reset_username']);
                            unset($_SESSION['reset_user_id']);
                            unset($_SESSION['reset_identity_verified']);
                            
                            $success = 'Password has been successfully reset. You can now login with your new password.';
                            
                            logEvent('INFO', 'Password reset completed via multi-step verification', [
                                'user_id' => $_SESSION['reset_user_id'] ?? 'unknown',
                                'username' => $_SESSION['reset_username'] ?? 'unknown'
                            ]);
                        } else {
                            $errors[] = 'Failed to update password. Please try again.';
                        }
                    } catch (Exception $e) {
                        $errors[] = 'An error occurred while updating your password. Please try again.';
                        logEvent('ERROR', 'Password reset failed', [
                            'user_id' => $_SESSION['reset_user_id'] ?? 'unknown',
                            'error' => $e->getMessage()
                        ]);
                    }
                } else {
                    $errors = array_merge($errors, array_values($validation->getErrors()));
                }
                break;
        }
    }
}

// Check current step from session
if (isset($_SESSION['reset_username']) && !isset($_SESSION['reset_identity_verified'])) {
    $currentStep = 2;
    $verifiedUsername = $_SESSION['reset_username'];
} elseif (isset($_SESSION['reset_username']) && isset($_SESSION['reset_identity_verified'])) {
    $currentStep = 3;
    $verifiedUsername = $_SESSION['reset_username'];
}

// Generate CSRF token
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .step.active .step-number {
            background-color: #dc3545;
            color: white;
        }
        .step.completed .step-number {
            background-color: #28a745;
            color: white;
        }
        .step.pending .step-number {
            background-color: #6c757d;
            color: white;
        }
        .step-line {
            width: 60px;
            height: 2px;
            background-color: #dee2e6;
            margin: 0 0.5rem;
        }
        .step-line.active {
            background-color: #dc3545;
        }
        .step-line.completed {
            background-color: #28a745;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow-sm mt-5">
                    <div class="card-header bg-danger text-white text-center">
                        <h4><i class="fas fa-key"></i> Reset Password</h4>
                        <p class="mb-0">Multi-step verification process</p>
                    </div>
                    
                    <!-- Step Indicator -->
                    <div class="card-body">
                        <div class="step-indicator">
                            <div class="step <?php echo $currentStep >= 1 ? ($currentStep == 1 ? 'active' : 'completed') : 'pending'; ?>">
                                <div class="step-number">1</div>
                                <span>Username</span>
                            </div>
                            <div class="step-line <?php echo $currentStep >= 2 ? 'completed' : 'active'; ?>"></div>
                            <div class="step <?php echo $currentStep >= 2 ? ($currentStep == 2 ? 'active' : 'completed') : 'pending'; ?>">
                                <div class="step-number">2</div>
                                <span>Identity</span>
                            </div>
                            <div class="step-line <?php echo $currentStep >= 3 ? 'completed' : 'active'; ?>"></div>
                            <div class="step <?php echo $currentStep >= 3 ? ($currentStep == 3 ? 'active' : 'completed') : 'pending'; ?>">
                                <div class="step-number">3</div>
                                <span>Password</span>
                            </div>
                        </div>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                            </div>
                            <div class="text-center">
                                <a href="login.php" class="btn btn-danger">
                                    <i class="fas fa-sign-in-alt"></i> Proceed to Login
                                </a>
                            </div>
                        <?php else: ?>
                            <?php if ($currentStep == 1): ?>
                                <!-- Step 1: Username Verification -->
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> 
                                    Enter your username to begin the password reset process.
                                </div>
                                
                                <form method="POST" action="multi-step-reset.php">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="step" value="1">
                                    
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" class="form-control" id="username" name="username" 
                                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                                   placeholder="Enter your username" required>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-arrow-right"></i> Continue
                                        </button>
                                    </div>
                                </form>
                                
                            <?php elseif ($currentStep == 2): ?>
                                <!-- Step 2: Identity Verification -->
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> 
                                    Please verify your identity by entering your first and last name.
                                </div>
                                
                                <form method="POST" action="multi-step-reset.php">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="step" value="2">
                                    
                                    <div class="mb-3">
                                        <label for="first_name" class="form-label">First Name *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                                   value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" 
                                                   placeholder="Enter your first name" required>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="last_name" class="form-label">Last Name *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                                   value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" 
                                                   placeholder="Enter your last name" required>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-arrow-right"></i> Verify Identity
                                        </button>
                                        <a href="multi-step-reset.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left"></i> Back to Username
                                        </a>
                                    </div>
                                </form>
                                
                            <?php elseif ($currentStep == 3): ?>
                                <!-- Step 3: Password Change -->
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> 
                                    Set your new password. Make sure it's strong and secure.
                                </div>
                                
                                <form method="POST" action="multi-step-reset.php">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="step" value="3">
                                    
                                    <div class="mb-3">
                                        <label for="password" class="form-label">New Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" class="form-control" id="password" name="password" required>
                                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">
                                            Password must be at least <?php echo PASSWORD_MIN_LENGTH; ?> characters long 
                                            and contain uppercase, lowercase, and number.
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                            <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-save"></i> Reset Password
                                        </button>
                                        <a href="multi-step-reset.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left"></i> Back to Identity Verification
                                        </a>
                                    </div>
                                </form>
                            <?php endif; ?>
                            
                            <hr>
                            
                            <div class="text-center">
                                <a href="login.php" class="text-decoration-none">
                                    <i class="fas fa-arrow-left"></i> Back to Login
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        function togglePasswordVisibility(inputId, buttonId) {
            const input = document.getElementById(inputId);
            const button = document.getElementById(buttonId);
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        // Add event listeners for password toggles if they exist
        const togglePassword = document.getElementById('togglePassword');
        const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
        
        if (togglePassword) {
            togglePassword.addEventListener('click', function() {
                togglePasswordVisibility('password', 'togglePassword');
            });
        }
        
        if (toggleConfirmPassword) {
            toggleConfirmPassword.addEventListener('click', function() {
                togglePasswordVisibility('confirm_password', 'toggleConfirmPassword');
            });
        }
        
        // Password confirmation validation
        const confirmPassword = document.getElementById('confirm_password');
        if (confirmPassword) {
            confirmPassword.addEventListener('input', function() {
                const password = document.getElementById('password').value;
                const confirmPasswordValue = this.value;
                
                if (password !== confirmPasswordValue) {
                    this.setCustomValidity('Passwords do not match');
                } else {
                    this.setCustomValidity('');
                }
            });
        }
        
        // Password strength indicator
        const password = document.getElementById('password');
        if (password) {
            password.addEventListener('input', function() {
                const passwordValue = this.value;
                const minLength = <?php echo PASSWORD_MIN_LENGTH; ?>;
                
                let strength = 0;
                let feedback = [];
                
                if (passwordValue.length >= minLength) strength++;
                else feedback.push('At least ' + minLength + ' characters');
                
                if (/[a-z]/.test(passwordValue)) strength++;
                else feedback.push('Lowercase letter');
                
                if (/[A-Z]/.test(passwordValue)) strength++;
                else feedback.push('Uppercase letter');
                
                if (/\d/.test(passwordValue)) strength++;
                else feedback.push('Number');
                
                // Update visual feedback
                const input = this;
                if (strength < 4) {
                    input.classList.remove('is-valid');
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                    input.classList.add('is-valid');
                }
            });
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-info)');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html> 