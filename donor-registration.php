<?php
/**
 * Comprehensive Donor Registration Form
 * Blood Donation Management System - Medical Authorization Form
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';
require_once 'classes/UnifiedUser.php';

// Start session
if (!startSecureSession()) {
    redirectWithMessage('login.php', ERROR_MESSAGES['SESSION_EXPIRED'], 'error');
}

$errors = [];
$success = '';
$db = Database::getInstance();
$bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Process comprehensive donor registration
        // This would include all the medical screening data
        $userData = [
            // Basic Information
            'username' => sanitizeInput($_POST['username'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'first_name' => sanitizeInput($_POST['first_name'] ?? ''),
            'last_name' => sanitizeInput($_POST['last_name'] ?? ''),
            'middle_name' => sanitizeInput($_POST['middle_name'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'email' => sanitizeInput($_POST['email'] ?? ''),
            'address' => sanitizeInput($_POST['address'] ?? ''),
            'birth_date' => $_POST['birth_date'] ?? '',
            'gender' => $_POST['gender'] ?? '',
            'blood_type' => $_POST['blood_type'] ?? '',
            
            // Medical Information
            'emergency_contact' => sanitizeInput($_POST['emergency_contact'] ?? ''),
            'emergency_phone' => sanitizeInput($_POST['emergency_phone'] ?? ''),
            'medical_conditions' => $_POST['medical_conditions'] ?? [],
            'medications' => sanitizeInput($_POST['medications'] ?? ''),
            'allergies' => sanitizeInput($_POST['allergies'] ?? ''),
            'last_donation' => $_POST['last_donation'] ?? '',
            
            // Consents and Authorization
            'consent_medical_history' => isset($_POST['consent_medical_history']),
            'consent_health_screening' => isset($_POST['consent_health_screening']),
            'consent_blood_testing' => isset($_POST['consent_blood_testing']),
            'consent_data_sharing' => isset($_POST['consent_data_sharing']),
            'authorization_disclosure' => isset($_POST['authorization_disclosure']),
            'terms_agreement' => isset($_POST['terms_agreement'])
        ];
        
        // Validate comprehensive donor data
        if (empty($errors)) {
            try {
                // Create donor account with medical information
                $user = UnifiedUser::createDonorAccount($userData);
                redirectWithMessage('login.php', 'Donor registration successful! You can now login to access your donor dashboard.', 'success');
            } catch (Exception $e) {
                $errors[] = $e->getMessage();
            }
        }
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donor Registration - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .medical-form {
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .form-section {
            border-left: 4px solid #dc3545;
            padding-left: 20px;
            margin-bottom: 30px;
        }
        .section-title {
            color: #dc3545;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .checkbox-group {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        .signature-section {
            border: 2px solid #333;
            padding: 20px;
            margin-top: 30px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container my-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="medical-form p-4">
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <h2 class="text-danger">
                            <i class="fas fa-tint"></i> <?php echo APP_NAME; ?>
                        </h2>
                        <h4>BLOOD DONOR REGISTRATION & MEDICAL AUTHORIZATION</h4>
                        <p class="text-muted">Complete Medical History and Consent Form</p>
                    </div>

                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" id="donorRegistrationForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">

                        <!-- SECTION A: PERSONAL INFORMATION -->
                        <div class="form-section">
                            <h5 class="section-title"><i class="fas fa-user"></i> SECTION A: PERSONAL INFORMATION</h5>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required
                                           value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="middle_name" class="form-label">Middle Name</label>
                                    <input type="text" class="form-control" id="middle_name" name="middle_name"
                                           value="<?php echo htmlspecialchars($_POST['middle_name'] ?? ''); ?>">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required
                                           value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="birth_date" class="form-label">Date of Birth *</label>
                                    <input type="date" class="form-control" id="birth_date" name="birth_date" required
                                           value="<?php echo $_POST['birth_date'] ?? ''; ?>">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="gender" class="form-label">Gender *</label>
                                    <select class="form-control" id="gender" name="gender" required>
                                        <option value="">Select Gender</option>
                                        <option value="Male" <?php echo ($_POST['gender'] ?? '') === 'Male' ? 'selected' : ''; ?>>Male</option>
                                        <option value="Female" <?php echo ($_POST['gender'] ?? '') === 'Female' ? 'selected' : ''; ?>>Female</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="blood_type" class="form-label">Blood Type *</label>
                                    <select class="form-control" id="blood_type" name="blood_type" required>
                                        <option value="">Select Blood Type</option>
                                        <?php foreach ($bloodTypes as $type): ?>
                                            <option value="<?php echo $type['id']; ?>" 
                                                    <?php echo ($_POST['blood_type'] ?? '') == $type['id'] ? 'selected' : ''; ?>>
                                                <?php echo $type['type']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" required
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">Complete Address *</label>
                                <textarea class="form-control" id="address" name="address" rows="2" required><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                            </div>
                        </div>

                        <!-- SECTION B: ACCOUNT INFORMATION -->
                        <div class="form-section">
                            <h5 class="section-title"><i class="fas fa-lock"></i> SECTION B: ACCOUNT INFORMATION</h5>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="username" name="username" required
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password *</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>
                        </div>

                        <!-- SECTION C: EMERGENCY CONTACT -->
                        <div class="form-section">
                            <h5 class="section-title"><i class="fas fa-phone"></i> SECTION C: EMERGENCY CONTACT</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="emergency_contact" class="form-label">Emergency Contact Name *</label>
                                    <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" required
                                           value="<?php echo htmlspecialchars($_POST['emergency_contact'] ?? ''); ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="emergency_phone" class="form-label">Emergency Contact Phone *</label>
                                    <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" required
                                           value="<?php echo htmlspecialchars($_POST['emergency_phone'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>

                        <!-- SECTION D: MEDICAL HISTORY -->
                        <div class="form-section">
                            <h5 class="section-title"><i class="fas fa-notes-medical"></i> SECTION D: MEDICAL HISTORY DISCLOSURE</h5>
                            
                            <div class="checkbox-group">
                                <p><strong>Do you have any of the following medical conditions?</strong> (Check all that apply)</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="medical_conditions[]" value="hypertension" id="hypertension">
                                            <label class="form-check-label" for="hypertension">Hypertension</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="medical_conditions[]" value="diabetes" id="diabetes">
                                            <label class="form-check-label" for="diabetes">Diabetes</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="medical_conditions[]" value="heart_disease" id="heart_disease">
                                            <label class="form-check-label" for="heart_disease">Heart Disease</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="medical_conditions[]" value="hepatitis" id="hepatitis">
                                            <label class="form-check-label" for="hepatitis">Hepatitis (A, B, C)</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="medical_conditions[]" value="hiv" id="hiv">
                                            <label class="form-check-label" for="hiv">HIV/AIDS</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="medical_conditions[]" value="cancer" id="cancer">
                                            <label class="form-check-label" for="cancer">Cancer</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="medical_conditions[]" value="anemia" id="anemia">
                                            <label class="form-check-label" for="anemia">Anemia</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="medical_conditions[]" value="none" id="none_conditions">
                                            <label class="form-check-label" for="none_conditions">None of the above</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6 mb-3">
                                    <label for="medications" class="form-label">Current Medications (if any)</label>
                                    <textarea class="form-control" id="medications" name="medications" rows="3"
                                              placeholder="List any medications you are currently taking"><?php echo htmlspecialchars($_POST['medications'] ?? ''); ?></textarea>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="allergies" class="form-label">Known Allergies (if any)</label>
                                    <textarea class="form-control" id="allergies" name="allergies" rows="3"
                                              placeholder="List any known allergies"><?php echo htmlspecialchars($_POST['allergies'] ?? ''); ?></textarea>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="last_donation" class="form-label">Last Blood Donation Date (if any)</label>
                                <input type="date" class="form-control" id="last_donation" name="last_donation"
                                       value="<?php echo $_POST['last_donation'] ?? ''; ?>">
                            </div>
                        </div>

                        <!-- SECTION E: AUTHORIZATION AND CONSENT -->
                        <div class="form-section">
                            <h5 class="section-title"><i class="fas fa-file-contract"></i> SECTION E: AUTHORIZATION AND CONSENT</h5>
                            
                            <div class="checkbox-group">
                                <p><strong>AUTHORIZATION TO USE AND DISCLOSE HEALTH INFORMATION</strong></p>
                                <p>I understand that information about me and my health will be used and disclosed for the following purposes:</p>
                                <ul>
                                    <li>Determination of my eligibility to donate blood</li>
                                    <li>Blood testing and screening</li>
                                    <li>Record keeping and donor management</li>
                                    <li>Communication regarding donation opportunities</li>
                                </ul>
                                
                                <div class="form-check mt-3">
                                    <input class="form-check-input" type="checkbox" name="authorization_disclosure" id="authorization_disclosure" required>
                                    <label class="form-check-label" for="authorization_disclosure">
                                        <strong>I authorize the use and disclosure of my health information as described above. *</strong>
                                    </label>
                                </div>
                            </div>

                            <div class="checkbox-group mt-3">
                                <p><strong>CONSENT TO MEDICAL PROCEDURES AND SCREENING</strong></p>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="consent_medical_history" id="consent_medical_history" required>
                                    <label class="form-check-label" for="consent_medical_history">
                                        I consent to provide my complete medical history for donor eligibility screening. *
                                    </label>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="consent_health_screening" id="consent_health_screening" required>
                                    <label class="form-check-label" for="consent_health_screening">
                                        I consent to health screening procedures including vital signs check and basic physical examination. *
                                    </label>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="consent_blood_testing" id="consent_blood_testing" required>
                                    <label class="form-check-label" for="consent_blood_testing">
                                        I consent to blood testing for infectious diseases and blood typing as required by medical standards. *
                                    </label>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="consent_data_sharing" id="consent_data_sharing" required>
                                    <label class="form-check-label" for="consent_data_sharing">
                                        I consent to sharing of relevant medical information with partner hospitals and medical facilities. *
                                    </label>
                                </div>
                            </div>

                            <div class="checkbox-group mt-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="terms_agreement" id="terms_agreement" required>
                                    <label class="form-check-label" for="terms_agreement">
                                        <strong>I have read, understood, and agree to all terms and conditions of the blood donation program. *</strong>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- SIGNATURE SECTION -->
                        <div class="signature-section">
                            <h6><strong>DONOR CERTIFICATION AND ELECTRONIC SIGNATURE</strong></h6>
                            <p>By submitting this form, I certify that:</p>
                            <ul>
                                <li>All information provided is true and complete to the best of my knowledge</li>
                                <li>I understand the blood donation process and its potential risks</li>
                                <li>I am voluntarily participating in the blood donation program</li>
                                <li>I have read and understood all consent and authorization statements above</li>
                            </ul>
                            
                            <div class="row mt-4">
                                <div class="col-md-8">
                                    <p><strong>Electronic Signature:</strong> By clicking "Register as Donor" below, I electronically sign this document.</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <p><strong>Date:</strong> <?php echo date('Y-m-d'); ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- SUBMIT BUTTON -->
                        <div class="d-grid mt-4">
                            <button type="submit" class="btn btn-danger btn-lg">
                                <i class="fas fa-tint"></i> REGISTER AS BLOOD DONOR
                            </button>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="index.php" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });

        // "None of the above" checkbox logic
        document.getElementById('none_conditions').addEventListener('change', function() {
            const medicalCheckboxes = document.querySelectorAll('input[name="medical_conditions[]"]:not(#none_conditions)');
            if (this.checked) {
                medicalCheckboxes.forEach(cb => cb.checked = false);
            }
        });

        // If any medical condition is checked, uncheck "none"
        document.querySelectorAll('input[name="medical_conditions[]"]:not(#none_conditions)').forEach(function(cb) {
            cb.addEventListener('change', function() {
                if (this.checked) {
                    document.getElementById('none_conditions').checked = false;
                }
            });
        });

        // Form validation
        document.getElementById('donorRegistrationForm').addEventListener('submit', function(e) {
            const requiredConsents = [
                'authorization_disclosure',
                'consent_medical_history', 
                'consent_health_screening',
                'consent_blood_testing',
                'consent_data_sharing',
                'terms_agreement'
            ];

            let allChecked = true;
            requiredConsents.forEach(function(id) {
                if (!document.getElementById(id).checked) {
                    allChecked = false;
                }
            });

            if (!allChecked) {
                e.preventDefault();
                alert('Please check all required consent and authorization boxes to continue.');
                return false;
            }
        });
    </script>
</body>
</html>
